# Инструкции по деплою PactCRM

## Настройка автоматического деплоя на Vercel

### 1. Создание проектов на Vercel

Для каждого приложения в монорепозитории необходимо создать отдельный проект на Vercel:

1. **tenant-dashboard** - Панель управления для застройщиков
2. **client-app** - Клиентское приложение для покупателей
3. **super-admin** - Панель суперадминистратора

#### Настройка проектов на Vercel:

1. Зайдите на [vercel.com](https://vercel.com) и войдите в аккаунт
2. Нажмите "New Project" и выберите репозиторий `KLASTER-DIGITAL/pactcrm`
3. Для каждого приложения:
   - **Project Name**: `pactcrm-tenant-dashboard`, `pactcrm-client-app`, `pactcrm-super-admin`
   - **Framework Preset**: Next.js
   - **Root Directory**: `apps/tenant-dashboard`, `apps/client-app`, `apps/super-admin`
   - **Build Command**: `cd ../.. && pnpm run build --filter=[app-name]`
   - **Output Directory**: `.next`
   - **Install Command**: `cd ../.. && pnpm install --frozen-lockfile`

### 2. Получение необходимых токенов и ID

#### Vercel Token:
1. Перейдите в [Vercel Settings > Tokens](https://vercel.com/account/tokens)
2. Создайте новый токен с именем "GitHub Actions PactCRM"
3. Скопируйте токен (он понадобится для GitHub Secrets)

#### Organization ID:
1. Перейдите в [Vercel Settings > General](https://vercel.com/account)
2. Скопируйте "Organization ID"

#### Project IDs:
Для каждого созданного проекта:
1. Перейдите в настройки проекта на Vercel
2. Во вкладке "General" найдите "Project ID"
3. Скопируйте ID для каждого проекта

### 3. Настройка GitHub Secrets

Перейдите в настройки GitHub репозитория: `Settings > Secrets and variables > Actions`

Добавьте следующие секреты:

```
VERCEL_TOKEN=your_vercel_token_here
VERCEL_ORG_ID=your_organization_id_here
VERCEL_PROJECT_ID_TENANT=project_id_for_tenant_dashboard
VERCEL_PROJECT_ID_CLIENT=project_id_for_client_app
VERCEL_PROJECT_ID_ADMIN=project_id_for_super_admin
```

### 4. Настройка переменных окружения

Для каждого проекта на Vercel добавьте переменные окружения:

#### Общие переменные для всех проектов:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXTAUTH_SECRET=your_nextauth_secret
```

#### Специфичные переменные:
- **tenant-dashboard**: `NEXTAUTH_URL=https://your-tenant-dashboard-domain.vercel.app`
- **client-app**: `NEXTAUTH_URL=https://your-client-app-domain.vercel.app`
- **super-admin**: `NEXTAUTH_URL=https://your-super-admin-domain.vercel.app`

### 5. Проверка деплоя

После настройки всех секретов и переменных:

1. Сделайте коммит в ветку `main`
2. Проверьте выполнение GitHub Actions в разделе "Actions" репозитория
3. Убедитесь, что все три приложения успешно деплоятся на Vercel

### 6. Структура доменов

После успешного деплоя у вас будут следующие домены:

- **Tenant Dashboard**: `https://pactcrm-tenant-dashboard.vercel.app`
- **Client App**: `https://pactcrm-client-app.vercel.app`
- **Super Admin**: `https://pactcrm-super-admin.vercel.app`

### 7. Настройка кастомных доменов (опционально)

Для продакшн использования рекомендуется настроить кастомные домены:

- `dashboard.pactcrm.com` → tenant-dashboard
- `app.pactcrm.com` → client-app
- `admin.pactcrm.com` → super-admin

## Troubleshooting

### Проблемы с зависимостями
Если возникают проблемы с установкой зависимостей, убедитесь что:
- Используется правильная версия Node.js (18+)
- Команда установки указывает на корень монорепозитория
- Все workspace зависимости корректно настроены

### Проблемы с сборкой
- Проверьте, что все пакеты (`@pactcrm/ui`, `@pactcrm/supabase-client`) собираются перед сборкой приложений
- Убедитесь, что переменные окружения корректно настроены

### Проблемы с GitHub Actions
- Проверьте, что все секреты добавлены в GitHub
- Убедитесь, что токен Vercel имеет необходимые права
- Проверьте логи выполнения в разделе Actions
