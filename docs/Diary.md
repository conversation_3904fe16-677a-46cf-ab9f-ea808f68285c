# Технический журнал PactCRM

Этот документ служит техническим журналом проекта PactCRM (TenantVerse), фиксирующим наблюдения, решения, проблемы и их решения, возникающие в процессе разработки.

## Формат

Каждая запись следует следующей структуре:
- **Дата**: ГГГГ-ММ-ДД
- **Категория**: Архитектура/Интеграция/AI/Безопасность/Производительность
- **Наблюдение**: Что было замечено или обнаружено
- **Решение**: Что было решено или реализовано
- **Проблемы/Вызовы**: Текущие проблемы или вызовы
- **Следующие шаги**: Планируемые последующие действия

---

## Записи

### 2023-10-01
**Категория**: Архитектура

**Наблюдение**:
Начальная сессия планирования архитектуры проекта. Оценены различные подходы к реализации мультитенантной SaaS-платформы для застройщиков недвижимости.

**Решение**:
Выбран Supabase в качестве бэкенд-платформы благодаря встроенной поддержке Row-Level Security, что критически важно для изоляции данных между арендаторами. Выбран shadcn-admin в качестве основы для веб-интерфейсов для ускорения разработки.

**Проблемы/Вызовы**:
Необходимо тщательно спроектировать схему базы данных для обеспечения правильной изоляции арендаторов при сохранении производительности. Опасения по поводу масштабирования политик RLS по мере роста приложения.

**Следующие шаги**:
Создать детальную схему базы данных с учетом изоляции арендаторов. Исследовать лучшие практики для Supabase RLS в мультитенантных приложениях.

---

### 2023-10-05
**Категория**: Интеграция

**Наблюдение**:
Оценены варианты интеграции с банковскими API TBC Bank и Bank of Georgia. Оба банка предлагают REST API, но с разными механизмами аутентификации и форматами данных.

**Решение**:
Создан дизайн на основе паттерна адаптера для абстрагирования различий между банковскими API. Это позволит добавлять новые банковские интеграции в будущем без изменения основной логики обработки платежей.

**Проблемы/Вызовы**:
Bank of Georgia требует дополнительных мер безопасности, включая белый список IP-адресов и аутентификацию на основе сертификатов. TBC Bank имеет ограничения на количество запросов, что может повлиять на массовые операции.

**Следующие шаги**:
Реализовать интерфейсы адаптеров и создать макеты для тестирования. Запланировать встречи с представителями банков для уточнения лимитов использования API и требований безопасности.

---

### 2023-10-10
**Категория**: AI

**Наблюдение**:
Исследованы возможности OpenAI API для реализации AI-слоя. Протестированы различные модели для оценки рисков и прогнозирования дефолтов по платежам.

**Решение**:
Выбран GPT-4 для сложных задач рассуждения, таких как оценка рисков, и дообучены меньшие модели для конкретных задач прогнозирования для оптимизации стоимости и производительности. Реализован слой кэширования для уменьшения количества API-вызовов.

**Проблемы/Вызовы**:
Необходимо балансировать точность AI с соображениями стоимости. Некоторые прогнозы требуют исторических данных, которые могут отсутствовать для новых арендаторов.

**Следующие шаги**:
Разработать гибридный подход, использующий системы на основе правил для новых арендаторов с ограниченными данными и постепенно включающий AI-прогнозы по мере накопления данных.

---

### 2023-10-15
**Категория**: Безопасность

**Наблюдение**:
Проведен обзор безопасности планируемой системы аутентификации и авторизации. Выявлены потенциальные уязвимости в контроле доступа мультитенантной системы.

**Решение**:
Реализованы дополнительные уровни безопасности помимо Supabase RLS, включая валидацию контекста арендатора на уровне приложения и проверку JWT-утверждений. Добавлено комплексное аудит-логирование для всех попыток межарендаторного доступа.

**Проблемы/Вызовы**:
Дополнительные проверки безопасности добавляют сложность и могут повлиять на производительность. Необходимо обеспечить, чтобы меры безопасности не создавали проблем с удобством использования для легитимных межарендаторных операций (например, для пользователей superadmin).

**Следующие шаги**:
Создать автоматизированный набор тестов безопасности для проверки изоляции арендаторов. Внедрить мониторинг производительности для оценки влияния мер безопасности.

---

### 2023-10-20
**Категория**: Архитектура

**Наблюдение**:
Рассмотрен планируемый подход к генерации и управлению документами. Система должна генерировать контракты, графики платежей и другие юридические документы с шаблонами, специфичными для каждого арендатора.

**Решение**:
Спроектирована система шаблонизатора, позволяющая арендаторам создавать и настраивать шаблоны документов с подстановкой переменных. Шаблоны будут храниться в Supabase Storage с отдельными хранилищами для каждого арендатора.

**Проблемы/Вызовы**:
Необходимо обеспечить надежность генерации документов и обработку крайних случаев, таких как специальные символы и форматирование. Также необходимо учитывать юридические требования к электронным документам в различных юрисдикциях.

**Следующие шаги**:
Реализовать прототип шаблонизатора и протестировать с различными типами документов. Проконсультироваться с юридическими экспертами о требованиях к электронным документам.

---

### 2023-10-25
**Категория**: Интеграция

**Наблюдение**:
Протестирована интеграция WhatsApp API для уведомлений клиентов. Официальный WhatsApp Business API имеет сложные требования к настройке и высокую стоимость.

**Решение**:
Решено использовать стороннего провайдера WhatsApp API (Twilio) для упрощения интеграции и снижения начальной сложности настройки. Спроектирован абстрактный слой уведомлений, который может работать с несколькими каналами (WhatsApp, SMS, email).

**Проблемы/Вызовы**:
WhatsApp имеет строгие политики относительно шаблонов сообщений и частоты отправки. Необходимо обеспечить соответствие требованиям, чтобы избежать пометки как спам.

**Следующие шаги**:
Создать процесс утверждения шаблонов сообщений и реализовать ограничение частоты отправки для обеспечения соответствия политикам WhatsApp.

---

### 2023-10-30
**Категория**: Производительность

**Наблюдение**:
Проведено тестирование производительности планируемой схемы базы данных с симулированными мультитенантными данными. Выявлены потенциальные узкие места в запросах, охватывающих несколько таблиц с политиками RLS.

**Решение**:
Переработаны некоторые отношения в базе данных для уменьшения сложности соединений. Реализованы материализованные представления для часто запрашиваемых отчетных данных для улучшения производительности запросов.

**Проблемы/Вызовы**:
Материализованные представления требуют регулярного обновления, что добавляет сложность процессу обновления данных. Необходимо балансировать нормализацию с производительностью запросов.

**Следующие шаги**:
Внедрить автоматизированное тестирование производительности как часть CI/CD-конвейера. Создать стратегию обновления представлений, минимизирующую влияние на производительность системы.

---

### 2023-11-05
**Категория**: AI

**Наблюдение**:
Протестирована функция анализа контрактов с использованием AI. Начальная реализация испытывала трудности с извлечением ключевой информации из контрактов различных форматов.

**Решение**:
Улучшен конвейер обработки документов с добавлением этапа предварительной обработки, который нормализует структуру документа перед AI-анализом. Добавлен этап верификации с участием человека для критически важных условий контракта.

**Проблемы/Вызовы**:
Система должна обрабатывать множество языков и юрисдикций с различными структурами контрактов. Обучающие данные AI могут не охватывать все возможные вариации.

**Следующие шаги**:
Собрать разнообразный набор образцов контрактов для различных типов недвижимости и юрисдикций. Реализовать механизм обратной связи для повышения точности AI со временем.

---

### 2023-11-10
**Категория**: Архитектура

**Наблюдение**:
Рассмотрен план архитектуры мобильного приложения. Выявлены проблемы в поддержании паритета функций между веб и мобильными интерфейсами при оптимизации пользовательского опыта для мобильных устройств.

**Решение**:
Принят подход с общим API-слоем, где и веб, и мобильные клиенты используют одни и те же бэкенд-эндпоинты. Создана адаптивная система дизайна с компонентами, ориентированными на мобильные устройства, которые могут адаптироваться к обеим платформам.

**Проблемы/Вызовы**:
Мобильные устройства имеют ограниченные ресурсы и проблемы с подключением. Необходимо реализовать эффективную синхронизацию данных и возможности работы офлайн.

**Следующие шаги**:
Разработать стратегии "офлайн-первый" для критически важных функций мобильного приложения. Реализовать фоновую синхронизацию для обработки прерывистого подключения.
