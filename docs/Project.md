# PactCRM: Глобальная архитектура

## Обзор

PactCRM (TenantVerse) — это мультитенантная SaaS-платформа, предназначенная для автоматизации и оптимизации процесса продажи объектов недвижимости с рассрочкой платежа. Платформа представляет собой комплексное решение для застройщиков, предоставляя инструменты для управления договорами, отслеживания платежей, коммуникации с клиентами и аналитики на основе искусственного интеллекта.

## Цели и задачи

- **Оптимизация процесса продаж**: Автоматизация и оптимизация рабочего процесса продаж для застройщиков
- **Улучшение управления клиентами**: Предоставление инструментов для эффективного управления взаимоотношениями с клиентами
- **Упрощение отслеживания платежей**: Автоматизация напоминаний о платежах и отслеживание планов рассрочки
- **Обеспечение принятия решений на основе данных**: Использование ИИ для предиктивного анализа и оценки рисков
- **Обеспечение изоляции данных между арендаторами**: Поддержание строгого разделения данных между различными организациями-арендаторами
- **Предоставление омниканального опыта**: Обеспечение единого опыта на веб и мобильных платформах

## Ключевые компоненты

### 1. Панель управления арендатора (Tenant Dashboard)

Основной интерфейс для застройщиков (компаний-клиентов платформы) для управления своими операциями.

- **Функции**:
  - Управление инвентарем недвижимости
  - База данных клиентов
  - Генерация и управление договорами
  - Отслеживание платежей и напоминания
  - Аналитика продаж и отчетность
  - Управление командой и назначение ролей

- **Технология**: На основе шаблона shadcn-admin с кастомными компонентами
- **Основные пользователи**: tenant_admin, after_sales_manager

[Ссылка на подробную документацию](./prompts/TenantDashboard.md)

### 2. Панель суперадминистратора (SuperAdmin Panel)

Административный интерфейс для операторов платформы для управления компаниями-застройщиками и глобальными настройками.

- **Функции**:
  - Подключение и управление компаниями-застройщиками
  - Глобальная конфигурация системы
  - Аналитика использования платформы
  - Управление биллингом и подписками
  - Система тикетов поддержки

- **Технология**: На основе шаблона shadcn-admin с кастомными компонентами
- **Основные пользователи**: superadmin, support

[Ссылка на подробную документацию](./prompts/SuperAdmin.md)

### 3. Клиентское приложение (Web & Mobile)

Интерфейс для конечных клиентов для управления покупками недвижимости и планами платежей.

- **Функции**:
  - Просмотр и управление договорами
  - История платежей и предстоящие платежи
  - Загрузка и управление документами
  - Коммуникация с представителями продаж
  - Детали и обновления по объектам недвижимости

- **Технология**: Web (React) и Mobile (React Native с EXPO)
- **Основные пользователи**: client

[Ссылка на подробную документацию](./prompts/ClientApp.md)

### 4. Слой интеграции с ИИ

Интеллектуальная система для предиктивной аналитики, автоматизированных рекомендаций и оптимизации процессов.

- **Функции**:
  - Оценка рисков для потенциальных клиентов
  - Прогнозирование дефолтов по платежам
  - Автоматизированные предложения по коммуникации с клиентами
  - Анализ рыночных трендов
  - Рекомендации по оптимизации продаж

- **Технология**: OpenAI API, кастомные ML-модели
- **Точки интеграции**: Встроен в Панель управления арендатора и Панель суперадминистратора

[Ссылка на подробную документацию](./prompts/AILayer.md)

### 5. Система уведомлений

Комплексная система коммуникации для оповещений, напоминаний и обновлений.

- **Функции**:
  - Напоминания о платежах
  - Обновления по договорам
  - Важные сроки
  - Системные уведомления
  - Маркетинговые коммуникации

- **Технология**: Email, SMS, WhatsApp API
- **Точки интеграции**: Подключена ко всем пользовательским интерфейсам

[Ссылка на подробную документацию](./prompts/NotificationSystem.md)

## Система ролей и взаимодействие пользователей

### Роли пользователей (RBAC)

1. **superadmin** — оператор SaaS-платформы (владелец системы)
   - **Уровень доступа**: глобальный, вне tenant_id
   - **Назначение**: Полный контроль над всей платформой
   - **Права**:
     - Управление всеми компаниями (tenants): регистрация, блокировка, удаление
     - Назначение тарифов, лимитов, доступных модулей
     - Управление глобальными справочниками
     - Подключение и настройка общих интеграций
     - Просмотр агрегированной аналитики по всей платформе
     - Управление обновлениями, аудитом безопасности, логами
     - Вход в любую компанию в режиме технического администратора
     - Назначение глобальных ролей support

2. **support** — техническая поддержка платформы
   - **Уровень доступа**: глобальный, с доступом к tenant_id
   - **Назначение**: Временная или постоянная помощь застройщикам
   - **Права**:
     - Доступ в любую панель администратора компании
     - Полный доступ к данным клиентов, договорам, платежам, объектам
     - Настройка интеграций, тестирование логики
     - Работа в режиме временного доступа
     - Действует от имени superadmin

3. **tenant_admin** — администратор компании-застройщика
   - **Уровень доступа**: внутри tenant_id
   - **Назначение**: Полный контроль над бизнес-процессами своей компании
   - **Права**:
     - Управление объектами, квартирами, клиентами, договорами, сотрудниками
     - Назначение внутренних ролей (after_sales_manager и др.)
     - Интеграции с внешними системами
     - Доступ к BI-отчётам, аналитике
     - Управление шаблонами уведомлений, внутренними справочниками

4. **after_sales_manager** — менеджер сопровождения
   - **Уровень доступа**: внутри tenant_id (создаётся tenant_admin)
   - **Назначение**: Пост-продажное сопровождение клиентов
   - **Права**:
     - Создание и ведение договоров с рассрочкой
     - Назначение клиентов, отслеживание графика платежей
     - Отметка поступлений вручную, приём подтверждений об оплате
     - Общение с клиентами (встроенный чат, WhatsApp API)
     - Работа с уведомлениями, реструктуризация графиков

5. **client** — покупатель апартаментов
   - **Уровень доступа**: внутри tenant_id (по договору)
   - **Назначение**: Конечный пользователь (мобильное приложение и веб-кабинет)
   - **Права**:
     - Просмотр информации по объекту и договору
     - Интерактивный график платежей
     - Получение уведомлений
     - Загрузка документов, подтверждений оплаты
     - Поддержка через чат/форму, общение с менеджером
     - Персональные настройки языка и предпочтений

### Взаимодействие ролей

```
superadmin ──────┐
                 │
support ─────────┼─── tenant_admin ─── after_sales_manager ─── client
                 │
                 └─── [мониторинг системы]
```

### Архитектура RBAC

Система ролей и разрешений реализована с использованием следующих таблиц в базе данных:

1. **roles** - таблица ролей
   - `id` - уникальный идентификатор роли
   - `tenant_id` - идентификатор арендатора (компании)
   - `name` - название роли
   - `description` - описание роли
   - `is_system` - флаг, указывающий, является ли роль системной

2. **permissions** - таблица разрешений
   - `id` - уникальный идентификатор разрешения
   - `tenant_id` - идентификатор арендатора (компании)
   - `name` - название разрешения
   - `description` - описание разрешения
   - `resource` - ресурс, к которому относится разрешение
   - `action` - действие, которое можно выполнять с ресурсом

3. **role_permissions** - таблица связи ролей и разрешений
   - `id` - уникальный идентификатор связи
   - `role_id` - идентификатор роли
   - `permission_id` - идентификатор разрешения
   - `tenant_id` - идентификатор арендатора (компании)

4. **user_roles** - таблица связи пользователей и ролей
   - `id` - уникальный идентификатор связи
   - `user_id` - идентификатор пользователя
   - `role_id` - идентификатор роли
   - `tenant_id` - идентификатор арендатора (компании)

### Категории разрешений

Разрешения в системе разделены на следующие категории (ресурсы):

1. **complexes** - жилые комплексы
2. **buildings** - здания
3. **apartments** - квартиры
4. **clients** - клиенты
5. **contracts** - договоры
6. **payments** - платежи
7. **users** - пользователи
8. **roles** - роли
9. **permissions** - разрешения
10. **settings** - настройки
11. **reports** - отчеты

Для каждого ресурса определены следующие действия:
- `read` - просмотр
- `create` - создание
- `update` - редактирование
- `delete` - удаление
- `assign` - назначение (только для разрешений)
- `export` - экспорт (только для отчетов)

### Компоненты для работы с RBAC

В проекте реализованы следующие компоненты для работы с системой ролей и разрешений:

1. **RoleContext** - контекст для работы с ролями и разрешениями
2. **RoleBasedRoute** - компонент для ограничения доступа к маршрутам на основе ролей
3. **PermissionGuard** - компонент для ограничения доступа к компонентам на основе разрешений
4. **RoleManager** - компонент для управления ролями и разрешениями
5. **UserRoleManager** - компонент для управления ролями пользователей

### Функции базы данных для работы с RBAC

В базе данных реализованы следующие функции для работы с системой ролей и разрешений:

1. **get_user_roles** - получение ролей пользователя
2. **get_user_permissions** - получение разрешений пользователя
3. **has_permission** - проверка наличия разрешения у пользователя
4. **has_role** - проверка наличия роли у пользователя

## Принципы изоляции данных (Multi-Tenant)

- **Row-Level Security (RLS)**: Реализовано в Supabase для обеспечения разделения данных арендаторов
- **Идентификация арендатора**: Каждый запрос включает контекст арендатора для правильной фильтрации данных
- **Изолированное хранилище**: Отдельные хранилища для файлов и документов, специфичных для каждого арендатора
- **Контроль доступа**: Разрешения на основе ролей, применяемые на уровнях API и базы данных
- **Аудит-логирование**: Комплексное логирование попыток межарендаторного доступа

## Схема базы данных

### Основные таблицы

1. **tenants** - Компании-застройщики (арендаторы системы)
   - id: uuid (PK)
   - name: text
   - settings: jsonb
   - created_at: timestamp

2. **users** - Пользователи системы
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - [другие поля из auth.users]

3. **roles** - Роли в системе
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - name: text
   - description: text

4. **permissions** - Разрешения в системе
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - name: text
   - description: text

5. **role_permissions** - Связь между ролями и разрешениями
   - id: uuid (PK)
   - role_id: uuid (FK -> roles.id)
   - permission_id: uuid (FK -> permissions.id)
   - tenant_id: uuid (FK -> tenants.id)

6. **user_roles** - Связь между пользователями и ролями
   - id: uuid (PK)
   - user_id: uuid (FK -> auth.users.id)
   - role_id: uuid (FK -> roles.id)
   - tenant_id: uuid (FK -> tenants.id)

### Таблицы для управления недвижимостью

1. **properties** - Объекты недвижимости (жилые комплексы)
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - name: text
   - address: text
   - status: text
   - metadata: jsonb

2. **buildings** - Здания в жилых комплексах
   - id: uuid (PK)
   - property_id: uuid (FK -> properties.id)
   - tenant_id: uuid (FK -> tenants.id)
   - name: text
   - address: text
   - floors: integer
   - status: text
   - metadata: jsonb

3. **apartments** - Квартиры в зданиях
   - id: uuid (PK)
   - building_id: uuid (FK -> buildings.id)
   - property_id: uuid (FK -> properties.id)
   - tenant_id: uuid (FK -> tenants.id)
   - number: text
   - floor: integer
   - rooms: integer
   - area: numeric
   - price: numeric
   - status: text
   - features: jsonb
   - metadata: jsonb

### Таблицы для управления клиентами и договорами

1. **clients** - Клиенты (покупатели недвижимости)
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - full_name: text
   - contact_info: jsonb

2. **contracts** - Договоры с клиентами
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - client_id: uuid (FK -> clients.id)
   - apartment_id: uuid (FK -> apartments.id)
   - [другие поля]

3. **payment_plans** - Планы платежей
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - name: text
   - [другие поля]

4. **payment_schedules** - Графики платежей
   - id: uuid (PK)
   - contract_id: uuid (FK -> contracts.id)
   - tenant_id: uuid (FK -> tenants.id)
   - payment_plan_id: uuid (FK -> payment_plans.id)
   - [другие поля]

5. **scheduled_payments** - Запланированные платежи
   - id: uuid (PK)
   - payment_schedule_id: uuid (FK -> payment_schedules.id)
   - tenant_id: uuid (FK -> tenants.id)
   - due_date: date
   - amount: numeric
   - status: text
   - payment_id: uuid (FK -> payments.id)

6. **payments** - Фактические платежи
   - id: uuid (PK)
   - contract_id: uuid (FK -> contracts.id)
   - amount: numeric
   - status: text

### Таблицы для документов и уведомлений

1. **documents** - Документы
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - name: text
   - file_path: text
   - [другие поля]

2. **document_links** - Связи документов с сущностями
   - id: uuid (PK)
   - document_id: uuid (FK -> documents.id)
   - tenant_id: uuid (FK -> tenants.id)
   - entity_type: text
   - entity_id: uuid

3. **notifications** - Уведомления
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - user_id: uuid (FK -> auth.users.id)
   - title: text
   - message: text
   - [другие поля]

4. **notification_templates** - Шаблоны уведомлений
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - name: text
   - subject: text
   - body: text
   - [другие поля]

### Таблицы для интеграций и коммуникаций

1. **integrations** - Внешние интеграции
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - name: text
   - type: text
   - config: jsonb
   - [другие поля]

2. **integration_logs** - Логи интеграций
   - id: uuid (PK)
   - integration_id: uuid (FK -> integrations.id)
   - tenant_id: uuid (FK -> tenants.id)
   - event_type: text
   - status: text
   - [другие поля]

3. **chats** - Чаты с клиентами
   - id: uuid (PK)
   - tenant_id: uuid (FK -> tenants.id)
   - client_id: uuid (FK -> clients.id)
   - user_id: uuid (FK -> auth.users.id)
   - [другие поля]

4. **messages** - Сообщения в чатах
   - id: uuid (PK)
   - chat_id: uuid (FK -> chats.id)
   - tenant_id: uuid (FK -> tenants.id)
   - [другие поля]

## Технологический стек

### Фронтенд
- **Веб-интерфейсы**: Next.js, React, шаблон shadcn-admin
- **Мобильное приложение**: React Native с EXPO

### Бэкенд
- **База данных и аутентификация**: Supabase (PostgreSQL)
- **AI-сервисы**: OpenAI API
- **Хранилище файлов**: Supabase Storage

## Структура маршрутизации

### TenantDashboard

```
/login                  - Страница авторизации для арендаторов
/dashboard              - Главная страница панели управления
/properties             - Управление объектами недвижимости
/clients                - Управление клиентами
/contracts              - Управление договорами
/contract-templates     - Управление шаблонами договоров
/payments               - Управление платежами
/settings               - Настройки
/client/login           - Страница авторизации для клиентов
/client/dashboard       - Дашборд клиента
```

### SuperAdmin

```
/admin/login            - Страница авторизации для суперадминистраторов
/admin/dashboard        - Главная страница панели суперадминистратора
/admin/tenants          - Управление арендаторами
/admin/users            - Управление пользователями
/admin/settings         - Настройки
```

### Аутентификация и авторизация

Система использует Supabase Auth для аутентификации пользователей и контроля доступа на основе ролей (RBAC). Реализованы следующие компоненты:

1. **Middleware** - проверяет сессию пользователя и перенаправляет на соответствующие страницы в зависимости от роли
2. **AuthContext** - предоставляет доступ к данным пользователя и методам аутентификации
3. **RoleContext** - управляет ролями и разрешениями пользователя
4. **TenantContext** - обеспечивает контекст текущего арендатора для мультитенантности
5. **RoleBasedRoute** - компонент для защиты маршрутов на основе ролей и разрешений

### Интеграции
- **Коммуникация**: WhatsApp API
- **Банковские системы**: TBC Bank API, Bank of Georgia API
- **CRM**: amoCRM
- **Бухгалтерия**: 1C

### DevOps
- **CI/CD**: GitHub Actions
- **Хостинг**: Vercel (фронтенд), Supabase (бэкенд)
- **Мониторинг**: Sentry

## Диаграмма архитектуры системы

```
┌─────────────────────────────────────────────────────────────────┐
│                                                                 │
│                     Клиентские интерфейсы                       │
│                                                                 │
│  ┌───────────────┐   ┌───────────────┐   ┌───────────────────┐  │
│  │               │   │               │   │                   │  │
│  │    Панель     │   │    Панель     │   │  Клиентское Web/  │  │
│  │   арендатора  │   │ суперадмина   │   │ Mobile приложение │  │
│  │               │   │               │   │                   │  │
│  └───────┬───────┘   └───────┬───────┘   └─────────┬─────────┘  │
│          │                   │                     │            │
└──────────┼───────────────────┼─────────────────────┼────────────┘
           │                   │                     │
┌──────────┼───────────────────┼─────────────────────┼────────────┐
│          │                   │                     │            │
│          │                   │                     │            │
│  ┌───────▼───────────────────▼─────────────────────▼───────┐    │
│  │                                                         │    │
│  │                     Supabase Backend                    │    │
│  │                                                         │    │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐  │    │
│  │  │             │  │             │  │                 │  │    │
│  │  │  База данных │  │    Auth     │  │    Хранилище   │  │    │
│  │  │             │  │             │  │                 │  │    │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘  │    │
│  │                                                         │    │
│  └─────────────────────────────┬───────────────────────────┘    │
│                                │                                │
│                                │                                │
│  ┌────────────────────────────▼──────────────────────────────┐  │
│  │                                                           │  │
│  │                    Слой интеграции с ИИ                   │  │
│  │                                                           │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐    │  │
│  │  │             │  │             │  │                 │    │  │
│  │  │  OpenAI API │  │ Кастомные   │  │ Аналитический  │    │  │
│  │  │             │  │ ML-модели   │  │ движок         │    │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘    │  │
│  │                                                           │  │
│  └───────────────────────────────────────────────────────────┘  │
│                                                                 │
│                                                                 │
│  ┌───────────────────────────────────────────────────────────┐  │
│  │                                                           │  │
│  │                    Внешние интеграции                     │  │
│  │                                                           │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐    │  │
│  │  │             │  │             │  │                 │    │  │
│  │  │  WhatsApp   │  │ Банковские  │  │  1C/amoCRM      │    │  │
│  │  │  API        │  │ API         │  │  интеграция     │    │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘    │  │
│  │                                                           │  │
│  └───────────────────────────────────────────────────────────┘  │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## Ссылки на документацию модулей

- [Панель управления застройщика](./prompts/TenantDashboard.md)
- [Панель суперадминистратора](./prompts/SuperAdmin.md)
- [Клиентское приложение](./prompts/ClientApp.md)
- [Слой интеграции с ИИ](./prompts/AILayer.md)
- [Система уведомлений](./prompts/NotificationSystem.md)
- [Внешние интеграции](./prompts/Integrations.md)

## Процесс разработки

1. Перед началом каждого нового шага спрашивать подтверждения.
2. После каждого шага предоставлять краткое резюме изменений (не более 5 пунктов).
3. При возникновении технических проблем или неоднозначностей, предлагать 2-3 альтернативных подхода.
4. Всегда сохранять контекст текущей задачи и общую цель проекта.
5. Периодически напоминать о текущем статусе задачи и оставшихся шагах.
6. Следовать архитектурным решениям и стандартам, описанным в `project.md`.
7. Соблюдать принципы SOLID, KISS, DRY.
8. Проводить code review для всех изменений.
9. Использовать единый стиль кодирования (линтеры, pre-commit hooks).
10. Не оставлять неиспользуемый код и комментарии.
11. Обновлять документацию при внесении изменений в код.

## Система документации

Проект использует автоматизированную систему обновления документации, которая включает:

### Инструменты документирования

1. **Скрипты автоматизации**:
   - `update-docs.js` - основной скрипт для обновления документации
   - `scripts/update-docs.js` - CLI-интерфейс для ручного обновления документации
   - `.husky/post-commit` - Git hook для автоматического обновления документации при коммитах

2. **Команды для обновления документации**:
   - `pnpm docs:changelog <type> <description>` - обновление changelog.md
   - `pnpm docs:task <taskName> <status>` - обновление статуса задачи в tasktracker.md
   - `pnpm docs:project <section> <contentFile>` - обновление раздела в project.md

3. **Теги для автоматического обновления документации в коммитах**:
   - `[docs:changelog:added] <description>` - добавление новой функциональности
   - `[docs:changelog:changed] <description>` - изменение существующей функциональности
   - `[docs:changelog:fixed] <description>` - исправление ошибок
   - `[docs:task:<taskName>:<status>]` - обновление статуса задачи

### Структура документации

1. **Project.md** - глобальная архитектура проекта
   - Описание архитектуры
   - Технологический стек
   - Модули и компоненты
   - Интеграции
   - Процесс разработки

2. **Tasktracker.md** - трекер задач
   - Список задач по модулям
   - Статусы выполнения
   - Приоритеты
   - Зависимости

3. **Changelog.md** - история изменений
   - Добавленные функции
   - Измененные функции
   - Исправленные ошибки

4. **Diary.md** - технический журнал
   - Ежедневные записи о проделанной работе
   - Технические решения
   - Проблемы и их решения

5. **qa.md** - вопросы по архитектуре
   - Открытые вопросы
   - Принятые решения
   - Альтернативные подходы

6. **DEPLOYMENT.md** - инструкции по деплою
   - Настройка Vercel проектов
   - Конфигурация GitHub Actions
   - Переменные окружения
   - Troubleshooting

## GitHub репозиторий и CI/CD

### Репозиторий
- **URL**: https://github.com/KLASTER-DIGITAL/pactcrm
- **Основная ветка**: `main`
- **Структура**: Монорепозиторий с Turborepo

### Автоматический деплой
Проект настроен для автоматического деплоя на Vercel через GitHub Actions:

1. **Триггер**: Push в ветку `main`
2. **Workflow**: `.github/workflows/deploy.yml`
3. **Стратегия**: Параллельный деплой всех трех приложений
4. **Платформа**: Vercel

### Приложения и домены
После деплоя доступны следующие приложения:

1. **Tenant Dashboard** - Панель управления для застройщиков
   - Домен: `pactcrm-tenant-dashboard.vercel.app`
   - Порт разработки: 3000

2. **Client App** - Клиентское приложение для покупателей
   - Домен: `pactcrm-client-app.vercel.app`
   - Порт разработки: 3002

3. **Super Admin** - Панель суперадминистратора
   - Домен: `pactcrm-super-admin.vercel.app`
   - Порт разработки: 3003

### Переменные окружения
Каждое приложение использует следующие переменные:

- `NEXT_PUBLIC_SUPABASE_URL` - URL Supabase проекта
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Публичный ключ Supabase
- `SUPABASE_SERVICE_ROLE_KEY` - Сервисный ключ Supabase
- `NEXTAUTH_SECRET` - Секрет для NextAuth
- `NEXTAUTH_URL` - URL приложения для NextAuth

### Процесс деплоя
1. Разработчик делает push в ветку `main`
2. GitHub Actions запускает workflow
3. Устанавливаются зависимости с помощью pnpm
4. Собираются shared пакеты (`@pactcrm/ui`, `@pactcrm/supabase-client`)
5. Параллельно деплоятся все три приложения на Vercel
6. Vercel автоматически обновляет домены

### Мониторинг деплоя
- **GitHub Actions**: Логи сборки и деплоя в разделе "Actions"
- **Vercel Dashboard**: Статус деплоя и метрики производительности
- **Supabase Dashboard**: Мониторинг базы данных и API
