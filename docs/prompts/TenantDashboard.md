# Tenant Dashboard: Панель управления для застройщика

## Обзор

Tenant Dashboard — это основной интерфейс для застройщиков (компаний-клиентов платформы), предоставляющий инструменты для управления продажами недвижимости в рассрочку, работы с клиентами, контрактами и платежами. Интерфейс построен на базе шаблона shadcn-admin с кастомными компонентами и интеграциями.

## Цели и задачи

- **Централизация управления**: Единая точка доступа ко всем данным и функциям для застройщика
- **Автоматизация процессов**: Снижение ручной работы при управлении продажами и клиентами
- **Аналитика и отчетность**: Предоставление актуальных данных для принятия решений
- **Управление командой**: Распределение ролей и задач между сотрудниками застройщика
- **Интеграция с внешними системами**: Синхронизация с банками, CRM и учетными системами

## Пользовательские роли

1. **tenant_admin** — администратор компании-застройщика
   - **Уровень доступа**: внутри tenant_id
   - **Назначение**: Полный контроль над бизнес-процессами своей компании
   - **Права**:
     - Полный доступ к данным компании
     - Управление пользователями и ролями
     - Настройка параметров компании
     - Доступ к полной аналитике
     - Управление объектами недвижимости и договорами
     - Настройка интеграций с внешними системами

2. **after_sales_manager** — менеджер сопровождения
   - **Уровень доступа**: внутри tenant_id (создаётся tenant_admin)
   - **Назначение**: Пост-продажное сопровождение клиентов
   - **Права**:
     - Управление клиентами после продажи
     - Отслеживание платежей и работа с просрочками
     - Коммуникация с клиентами
     - Ведение договоров и графиков платежей
     - Отправка уведомлений клиентам

3. **sales_manager**
   - Работа с потенциальными клиентами
   - Создание и согласование контрактов
   - Первичные продажи

4. **accountant**
   - Доступ к финансовой информации
   - Сверка платежей
   - Формирование финансовых отчетов

## Ключевые функциональные модули

### 1. Дашборд

Главная страница с ключевыми показателями и уведомлениями.

**Компоненты**:
- Виджеты с KPI (продажи, платежи, просрочки)
- График платежей на ближайший период
- Список задач и напоминаний
- Уведомления о важных событиях

**Технические особенности**:
- Реактивное обновление данных через WebSockets
- Настраиваемые виджеты (drag-and-drop)
- Фильтрация по проектам и периодам

### 2. Управление объектами недвижимости

Модуль для работы с каталогом объектов недвижимости.

**Компоненты**:
- Каталог объектов с фильтрацией и поиском
- Детальная карточка объекта с характеристиками
- Управление статусами объектов (доступен, зарезервирован, продан)
- Загрузка и управление медиафайлами (фото, планы, документы)

**Технические особенности**:
- Интеграция с Supabase Storage для медиафайлов
- Версионирование данных объектов
- Поддержка геолокации и интеграция с картами

### 3. Управление клиентами

Модуль для работы с базой клиентов.

**Компоненты**:
- Список клиентов с фильтрацией и поиском
- Детальная карточка клиента с историей взаимодействия
- Оценка платежеспособности и рисков (AI-интеграция)
- Коммуникация с клиентами (WhatsApp, email)

**Технические особенности**:
- Интеграция с OpenAI для оценки рисков
- Шифрование персональных данных
- История изменений данных клиента

### 4. Управление контрактами

Модуль для создания и управления договорами.

**Компоненты**:
- Конструктор договоров с шаблонами
- Процесс согласования и подписания
- История изменений и версионирование
- Привязка к объектам и клиентам

**Технические особенности**:
- Генерация PDF-документов
- Электронное подписание
- Валидация условий контракта

### 5. Управление платежами

Модуль для отслеживания и управления платежами по рассрочке.

**Компоненты**:
- График платежей с отображением статусов
- Регистрация и верификация платежей
- Автоматические напоминания о платежах
- Работа с просрочками и реструктуризацией

**Технические особенности**:
- Интеграция с банковскими API
- Автоматическая сверка платежей
- Прогнозирование просрочек (AI-интеграция)

### 6. Аналитика и отчеты

Модуль для анализа данных и формирования отчетов.

**Компоненты**:
- Предустановленные отчеты (продажи, платежи, просрочки)
- Конструктор пользовательских отчетов
- Экспорт данных в различных форматах
- Визуализация данных (графики, диаграммы)

**Технические особенности**:
- Оптимизированные запросы для аналитики
- Кэширование отчетов
- Планировщик регулярных отчетов

### 7. Настройки компании

Модуль для управления настройками компании-застройщика.

**Компоненты**:
- Профиль компании
- Управление пользователями и ролями
- Настройка шаблонов документов
- Интеграции с внешними системами

**Технические особенности**:
- Управление доступами на основе ролей
- Аудит действий пользователей
- Конфигурация уведомлений

## Технические особенности

### Архитектура интерфейса

- **Фреймворк**: Next.js на базе шаблона shadcn-admin
- **Состояние**: React Context + SWR для кэширования данных
- **Стилизация**: Tailwind CSS + shadcn/ui компоненты
- **Формы**: React Hook Form + Zod для валидации

### Интеграции с бэкендом

- **API**: REST API через Supabase Functions
- **Реактивность**: WebSockets для обновления данных в реальном времени
- **Аутентификация**: JWT через Supabase Auth
- **Хранилище**: Supabase Storage для файлов и документов

### Оптимизация производительности

- **Кэширование**: SWR для кэширования запросов
- **Пагинация**: Виртуализация списков для больших наборов данных
- **Ленивая загрузка**: Динамический импорт компонентов
- **Предварительная загрузка**: Prefetching данных для часто используемых страниц

### Безопасность

- **Аутентификация**: Многофакторная аутентификация для критичных операций
- **Авторизация**: Проверка прав доступа на уровне компонентов
- **Данные**: Шифрование чувствительных данных на клиенте
- **Аудит**: Логирование действий пользователей

## Пользовательский опыт

### Навигация

- Боковое меню с основными разделами
- Быстрый поиск по всем сущностям
- Хлебные крошки для навигации внутри разделов
- Недавно просмотренные элементы

### Адаптивность

- Поддержка десктопных и планшетных устройств
- Оптимизированные представления для различных размеров экрана
- Прогрессивное улучшение для более мощных устройств

### Доступность

- Соответствие стандартам WCAG 2.1
- Поддержка клавиатурной навигации
- Совместимость со скринридерами
- Настраиваемые цветовые схемы (включая темную тему)

## Дорожная карта развития

### Версия 1.0
- Базовый функционал управления объектами, клиентами и контрактами
- Простая система платежей и напоминаний
- Основные отчеты и аналитика

### Версия 2.0
- Расширенная аналитика с AI-рекомендациями
- Интеграция с банковскими API для автоматической сверки платежей
- Мобильная версия для менеджеров

### Версия 3.0
- Полноценная CRM-функциональность
- Интеграция с маркетплейсами недвижимости
- Расширенные возможности для работы с просрочками и реструктуризацией
