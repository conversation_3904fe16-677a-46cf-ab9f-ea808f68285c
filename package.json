{"name": "pactcrm", "version": "0.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "prepare": "husky install", "docs:changelog": "node scripts/update-docs.js changelog", "docs:task": "node scripts/update-docs.js task", "docs:project": "node scripts/update-docs.js project"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "eslint": "^8.56.0", "husky": "^9.0.11", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "turbo": "^2.0.1"}, "engines": {"node": ">=18.0.0"}, "packageManager": "pnpm@8.15.1", "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}