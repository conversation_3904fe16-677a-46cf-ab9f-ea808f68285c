#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Получаем сообщение последнего коммита
COMMIT_MSG=$(git log -1 --pretty=%B)

# Проверяем, содержит ли сообщение коммита специальные теги для обновления документации
if echo "$COMMIT_MSG" | grep -q "\[docs:changelog:added\]"; then
  DESCRIPTION=$(echo "$COMMIT_MSG" | sed -n 's/.*\[docs:changelog:added\]\s*\(.*\)/\1/p')
  node update-docs.js changelog added "$DESCRIPTION"
fi

if echo "$COMMIT_MSG" | grep -q "\[docs:changelog:changed\]"; then
  DESCRIPTION=$(echo "$COMMIT_MSG" | sed -n 's/.*\[docs:changelog:changed\]\s*\(.*\)/\1/p')
  node update-docs.js changelog changed "$DESCRIPTION"
fi

if echo "$COMMIT_MSG" | grep -q "\[docs:changelog:fixed\]"; then
  DESCRIPTION=$(echo "$COMMIT_MSG" | sed -n 's/.*\[docs:changelog:fixed\]\s*\(.*\)/\1/p')
  node update-docs.js changelog fixed "$DESCRIPTION"
fi

if echo "$COMMIT_MSG" | grep -q "\[docs:task:"; then
  TASK_NAME=$(echo "$COMMIT_MSG" | sed -n 's/.*\[docs:task:\([^:]*\):\([^]]*\)\].*/\1/p')
  STATUS=$(echo "$COMMIT_MSG" | sed -n 's/.*\[docs:task:[^:]*:\([^]]*\)\].*/\1/p')
  node update-docs.js task "$TASK_NAME" "$STATUS"
fi

# Если были изменения в документации, делаем дополнительный коммит
if git diff --name-only | grep -q "docs/"; then
  git add docs/
  git commit -m "docs: Автоматическое обновление документации [skip ci]" --no-verify
fi
