{"buildCommand": "cd ../.. && pnpm run build --filter=tenant-dashboard", "outputDirectory": ".next", "installCommand": "cd ../.. && pnpm install --frozen-lockfile", "framework": "nextjs", "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url"}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}