/**
 * @file: contract-templates/[id]/versions/page.tsx
 * @description: Страница управления версиями шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

import React from 'react';
import { ContractTemplateVersionsList } from '../../components/ContractTemplateVersionsList';
import { PermissionGuard } from '@pactcrm/supabase-client';

interface ContractTemplateVersionsPageProps {
  params: {
    id: string;
  };
}

export default function ContractTemplateVersionsPage({ params }: ContractTemplateVersionsPageProps) {
  return (
    <PermissionGuard 
      resource="contract_templates" 
      action="read"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на просмотр версий шаблонов договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Версии шаблона договора</h1>
        <p className="text-muted-foreground">
          Управление версиями шаблона договора для отслеживания изменений.
        </p>
        <ContractTemplateVersionsList templateId={params.id} />
      </div>
    </PermissionGuard>
  );
}
