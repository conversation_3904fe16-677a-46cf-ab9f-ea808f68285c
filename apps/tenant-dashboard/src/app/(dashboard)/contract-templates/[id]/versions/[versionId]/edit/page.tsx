/**
 * @file: contract-templates/[id]/versions/[versionId]/edit/page.tsx
 * @description: Страница редактирования версии шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

import React from 'react';
import { ContractTemplateVersionForm } from '../../../../components/ContractTemplateVersionForm';
import { PermissionGuard } from '@pactcrm/supabase-client';

interface EditContractTemplateVersionPageProps {
  params: {
    id: string;
    versionId: string;
  };
}

export default function EditContractTemplateVersionPage({ params }: EditContractTemplateVersionPageProps) {
  return (
    <PermissionGuard 
      resource="contract_templates" 
      action="update"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на редактирование версий шаблонов договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Редактирование версии шаблона</h1>
        <ContractTemplateVersionForm 
          templateId={params.id} 
          versionId={params.versionId} 
        />
      </div>
    </PermissionGuard>
  );
}
