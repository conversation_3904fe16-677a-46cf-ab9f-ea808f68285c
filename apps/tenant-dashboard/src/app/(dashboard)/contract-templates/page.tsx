/**
 * @file: contract-templates/page.tsx
 * @description: Страница управления шаблонами договоров
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

import React from 'react';
import { ContractTemplatesList } from './components';
import { PermissionGuard } from '@pactcrm/supabase-client';

export default function ContractTemplatesPage() {
  return (
    <PermissionGuard
      resource="contract_templates"
      action="read"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на просмотр шаблонов договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Шаблоны договоров</h1>
        <p className="text-muted-foreground">
          Управление шаблонами договоров для автоматической генерации документов.
        </p>
        <ContractTemplatesList />
      </div>
    </PermissionGuard>
  );
}
