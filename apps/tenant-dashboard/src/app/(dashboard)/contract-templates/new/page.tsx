/**
 * @file: contract-templates/new/page.tsx
 * @description: Страница создания нового шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

import React from 'react';
import { ContractTemplateForm } from '../components/ContractTemplateForm';
import { PermissionGuard } from '@pactcrm/supabase-client';

export default function NewContractTemplatePage() {
  return (
    <PermissionGuard 
      resource="contract_templates" 
      action="create"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на создание шаблонов договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Создание нового шаблона договора</h1>
        <p className="text-muted-foreground">
          Создайте новый шаблон договора для автоматической генерации документов.
        </p>
        <ContractTemplateForm />
      </div>
    </PermissionGuard>
  );
}
