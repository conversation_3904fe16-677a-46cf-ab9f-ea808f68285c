/**
 * @file: ContractTemplateVersionsList.tsx
 * @description: Компонент для отображения списка версий шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import { 
  FileText, 
  Download, 
  Eye, 
  FileEdit, 
  Trash2, 
  ChevronLeft, 
  ChevronRight,
  Loader2,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Pagination, PaginationContent, PaginationItem } from '@/components/ui/pagination';
import { 
  useContractTemplateVersionsList,
  useContractTemplateVersion
} from '@pactcrm/supabase-client';
import { formatDate } from '@/lib/utils';

interface ContractTemplateVersionsListProps {
  templateId: string;
  limit?: number;
}

/**
 * Компонент для отображения списка версий шаблона договора
 */
export function ContractTemplateVersionsList({ templateId, limit = 10 }: ContractTemplateVersionsListProps) {
  const router = useRouter();
  const { 
    versions, 
    count, 
    page, 
    totalPages,
    loading, 
    error,
    nextPage,
    prevPage,
    refresh
  } = useContractTemplateVersionsList(templateId, 1, limit);
  
  const { removeVersion } = useContractTemplateVersion();
  const [deletingVersionId, setDeletingVersionId] = React.useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);

  // Обработчик удаления версии
  const handleDeleteVersion = async () => {
    if (!deletingVersionId) return;
    
    const success = await removeVersion();
    
    if (success) {
      setIsDeleteDialogOpen(false);
      setDeletingVersionId(null);
      refresh();
    }
  };

  // Обработчик просмотра версии
  const handleViewVersion = (versionId: string) => {
    router.push(`/contract-templates/${templateId}/versions/${versionId}`);
  };

  // Обработчик редактирования версии
  const handleEditVersion = (versionId: string) => {
    router.push(`/contract-templates/${templateId}/versions/${versionId}/edit`);
  };

  // Обработчик скачивания файла версии
  const handleDownloadFile = (fileUrl: string) => {
    window.open(fileUrl, '_blank');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin text-primary" />
        <span className="ml-2">Загрузка версий...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-center">
        <AlertTriangle className="h-6 w-6 text-destructive mx-auto mb-2" />
        <p className="text-muted-foreground">{error.toString()}</p>
      </div>
    );
  }

  if (!versions || versions.length === 0) {
    return (
      <div className="p-4 text-center border rounded-md">
        <p className="text-muted-foreground">У этого шаблона еще нет версий.</p>
        <Button 
          className="mt-2"
          onClick={() => router.push(`/contract-templates/${templateId}/versions/new`)}
        >
          Создать первую версию
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Версия</TableHead>
              <TableHead>Статус</TableHead>
              <TableHead>Дата создания</TableHead>
              <TableHead>Файлы</TableHead>
              <TableHead className="text-right">Действия</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {versions.map((version) => (
              <TableRow key={version.id}>
                <TableCell className="font-medium">v{version.version_number}</TableCell>
                <TableCell>
                  <Badge variant={version.is_active ? "default" : "secondary"}>
                    {version.is_active ? "Активна" : "Неактивна"}
                  </Badge>
                </TableCell>
                <TableCell>{formatDate(version.created_at)}</TableCell>
                <TableCell>
                  {version.files && version.files.length > 0 ? (
                    <div className="flex space-x-2">
                      {version.files.map((file) => (
                        <Button
                          key={file.id}
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownloadFile(file.file_path)}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          {file.file_name}
                        </Button>
                      ))}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">Нет файлов</span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <span className="sr-only">Открыть меню</span>
                        <FileText className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Действия</DropdownMenuLabel>
                      <DropdownMenuGroup>
                        <DropdownMenuItem onClick={() => handleViewVersion(version.id)}>
                          <Eye className="mr-2 h-4 w-4" />
                          <span>Просмотреть</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditVersion(version.id)}>
                          <FileEdit className="mr-2 h-4 w-4" />
                          <span>Редактировать</span>
                        </DropdownMenuItem>
                      </DropdownMenuGroup>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        className="text-destructive focus:text-destructive"
                        onClick={() => {
                          setDeletingVersionId(version.id);
                          setIsDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        <span>Удалить</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Всего {count} версий
          </div>
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={prevPage}
                  disabled={page <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </PaginationItem>
              <PaginationItem className="flex items-center justify-center px-4">
                <span className="text-sm">
                  Страница {page} из {totalPages}
                </span>
              </PaginationItem>
              <PaginationItem>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={nextPage}
                  disabled={page >= totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Вы уверены?</AlertDialogTitle>
            <AlertDialogDescription>
              Это действие нельзя отменить. Версия шаблона и все связанные файлы будут удалены безвозвратно.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Отмена</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteVersion}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Удалить
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
