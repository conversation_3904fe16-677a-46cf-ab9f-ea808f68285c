/**
 * @file: ContractTemplateForm.tsx
 * @description: Компонент формы для создания и редактирования шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client, react-hook-form, zod
 * @created: 2024-05-11
 */

"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { 
  useContractTemplate, 
  useContractTemplateTypes,
  ContractTemplateInsert,
  ContractTemplateUpdate
} from "@pactcrm/supabase-client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

// Схема валидации формы
const formSchema = z.object({
  name: z.string().min(2, { message: "Название должно содержать минимум 2 символа" }),
  description: z.string().optional(),
  type_id: z.string({ required_error: "Выберите тип шаблона" }),
  is_active: z.boolean().default(true),
});

type FormValues = z.infer<typeof formSchema>;

interface ContractTemplateFormProps {
  id?: string;
}

/**
 * Компонент формы для создания и редактирования шаблона договора
 */
export function ContractTemplateForm({ id }: ContractTemplateFormProps) {
  const router = useRouter();
  const { template, loading, error, isSaving, saveTemplate } = useContractTemplate(id);
  const { types, loading: typesLoading } = useContractTemplateTypes();

  // Инициализация формы
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      type_id: "",
      is_active: true,
    },
  });

  // Заполняем форму данными шаблона при загрузке
  React.useEffect(() => {
    if (template) {
      form.reset({
        name: template.name,
        description: template.description || "",
        type_id: template.type_id || "",
        is_active: template.is_active,
      });
    }
  }, [template, form]);

  // Обработчик отправки формы
  const onSubmit = async (data: FormValues) => {
    // Преобразуем данные формы в формат для API
    const templateData: ContractTemplateInsert | ContractTemplateUpdate = {
      name: data.name,
      description: data.description || null,
      type_id: data.type_id || null,
      is_active: data.is_active,
    };

    const savedTemplate = await saveTemplate(templateData);
    
    if (savedTemplate) {
      router.push(`/contract-templates/${savedTemplate.id}`);
    }
  };

  // Обработчик отмены
  const handleCancel = () => {
    if (id) {
      router.push(`/contract-templates/${id}`);
    } else {
      router.push('/contract-templates');
    }
  };

  if (loading) {
    return <div>Загрузка...</div>;
  }

  if (error) {
    return <div>Ошибка: {error.toString()}</div>;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{id ? "Редактирование шаблона" : "Создание шаблона"}</CardTitle>
        <CardDescription>
          Заполните форму для {id ? "обновления" : "создания"} шаблона договора
        </CardDescription>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Название</FormLabel>
                  <FormControl>
                    <Input placeholder="Введите название шаблона" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Описание</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Введите описание шаблона"
                      className="resize-none"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormDescription>
                    Необязательное поле. Добавьте краткое описание шаблона.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Тип шаблона</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={typesLoading}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Выберите тип шаблона" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {types.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Статус</FormLabel>
                    <FormDescription>
                      Активировать или деактивировать шаблон
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
            >
              Отмена
            </Button>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? "Сохранение..." : id ? "Обновить" : "Создать"}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  );
}
