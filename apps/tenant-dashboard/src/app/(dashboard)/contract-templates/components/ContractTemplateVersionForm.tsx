/**
 * @file: ContractTemplateVersionForm.tsx
 * @description: Компонент формы для создания и редактирования версии шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client, react-hook-form, zod
 * @created: 2024-05-11
 */

"use client";

import { useState, useRef, ChangeEvent, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { 
  useContractTemplateVersion,
  ContractTemplateVersionInsert,
  ContractTemplateVersionUpdate
} from "@pactcrm/supabase-client";
import { 
  Paperclip, 
  X, 
  File as FileIcon, 
  Trash2, 
  Loader2, 
  CheckCircle 
} from "lucide-react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";

// File upload related interfaces
interface FileWithPreview {
  id: string;
  preview: string;
  progress: number;
  name: string;
  size: number;
  type: string;
  lastModified?: number;
  file?: File;
}

// Form schema
const formSchema = z.object({
  content: z.string().min(1, { message: "Содержимое версии обязательно" }),
  variables: z.string().refine(
    (val) => {
      try {
        if (val === '') return true;
        JSON.parse(val);
        return true;
      } catch (e) {
        return false;
      }
    },
    { message: "Некорректный JSON формат" }
  ),
  is_active: z.boolean().default(true),
});

type FormValues = z.infer<typeof formSchema>;

interface ContractTemplateVersionFormProps {
  templateId: string;
  versionId?: string;
}

/**
 * Компонент формы для создания и редактирования версии шаблона договора
 */
export function ContractTemplateVersionForm({
  templateId,
  versionId
}: ContractTemplateVersionFormProps) {
  const router = useRouter();
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { 
    version, 
    loading, 
    error, 
    isSaving, 
    isUploading,
    saveVersion, 
    uploadFile,
    refresh
  } = useContractTemplateVersion(versionId, templateId);

  // Инициализация формы
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      content: "",
      variables: "{}",
      is_active: true,
    },
  });

  // Заполняем форму данными версии при загрузке
  useEffect(() => {
    if (version) {
      form.reset({
        content: version.content || "",
        variables: version.variables ? JSON.stringify(version.variables, null, 2) : "{}",
        is_active: version.is_active,
      });
    }
  }, [version, form]);

  // Обработчик отправки формы
  const onSubmit = async (data: FormValues) => {
    try {
      let variables;
      try {
        variables = data.variables ? JSON.parse(data.variables) : null;
      } catch (e) {
        variables = null;
      }

      const versionData: ContractTemplateVersionInsert | ContractTemplateVersionUpdate = {
        content: data.content,
        variables,
        is_active: data.is_active,
      };

      const savedVersion = await saveVersion(versionData);
      
      if (savedVersion && files.length > 0) {
        for (const fileItem of files) {
          if (fileItem.file) {
            await uploadFile(fileItem.file);
          }
        }
      }

      router.push(`/contract-templates/${templateId}/versions`);
    } catch (error) {
      console.error("Ошибка при сохранении версии:", error);
    }
  };

  // Обработчик отмены
  const handleCancel = () => {
    router.push(`/contract-templates/${templateId}/versions`);
  };

  // Функции для работы с файлами
  const handleFiles = (fileList: FileList) => {
    const newFiles = Array.from(fileList).map((file) => ({
      id: `${URL.createObjectURL(file)}-${Date.now()}`,
      preview: URL.createObjectURL(file),
      progress: 0,
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
      file,
    }));
    setFiles((prev) => [...prev, ...newFiles]);
    newFiles.forEach((f) => simulateUpload(f.id));
  };

  const simulateUpload = (id: string) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 15;
      setFiles((prev) =>
        prev.map((f) =>
          f.id === id ? { ...f, progress: Math.min(progress, 100) } : f,
        ),
      );
      if (progress >= 100) {
        clearInterval(interval);
      }
    }, 300);
  };

  const onDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFiles(e.dataTransfer.files);
  };

  const onDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const onDragLeave = () => setIsDragging(false);

  const onSelect = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) handleFiles(e.target.files);
  };

  const formatFileSize = (bytes: number): string => {
    if (!bytes) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Загрузка...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <h2 className="text-xl font-semibold mb-2">Ошибка загрузки</h2>
        <p className="text-muted-foreground">{error.toString()}</p>
      </div>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>
          {versionId ? "Редактирование версии шаблона" : "Создание новой версии шаблона"}
        </CardTitle>
        <CardDescription>
          Заполните форму для {versionId ? "обновления" : "создания"} версии шаблона договора
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Содержимое версии</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Введите содержимое шаблона договора..."
                      className="min-h-[200px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Текст шаблона договора с переменными в формате {"{variable_name}"}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="variables"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Переменные (JSON)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='{"variable_name": "default_value"}'
                      className="min-h-[150px] font-mono text-sm"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Список переменных в формате JSON
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Статус версии</FormLabel>
                    <FormDescription>
                      Активная версия будет использоваться при создании договоров
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {/* Секция загрузки файлов */}
            <div className="space-y-4">
              <FormLabel>Файлы версии</FormLabel>
              <div
                onDragOver={onDragOver}
                onDragLeave={onDragLeave}
                onDrop={onDrop}
                onClick={() => fileInputRef.current?.click()}
                className={cn(
                  "relative rounded-lg p-6 text-center cursor-pointer border-2 border-dashed border-border hover:border-primary/50 transition-colors",
                  isDragging && "border-primary"
                )}
              >
                <div className="flex flex-col items-center gap-3">
                  <div className="relative">
                    <Paperclip className="w-10 h-10 text-muted-foreground" />
                  </div>

                  <div className="space-y-1">
                    <h3 className="text-lg font-medium">
                      {isDragging
                        ? "Отпустите файлы здесь"
                        : files.length
                          ? "Добавить еще файлы"
                          : "Загрузить файлы"}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {isDragging ? (
                        <span className="font-medium text-primary">
                          Отпустите для загрузки
                        </span>
                      ) : (
                        <>
                          Перетащите файлы сюда или{" "}
                          <span className="text-primary font-medium">выберите</span>
                        </>
                      )}
                    </p>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    hidden
                    onChange={onSelect}
                    accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                  />
                </div>
              </div>

              {/* Список файлов */}
              {files.length > 0 && (
                <div className="mt-4">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">
                      Загруженные файлы ({files.length})
                    </h4>
                    {files.length > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setFiles([])}
                      >
                        Очистить все
                      </Button>
                    )}
                  </div>

                  <div className="space-y-2 max-h-[300px] overflow-y-auto pr-2">
                    {files.map((file) => (
                      <div
                        key={file.id}
                        className="p-3 flex items-start gap-3 rounded-lg bg-muted/50"
                      >
                        {/* Иконка/превью файла */}
                        <div className="relative flex-shrink-0">
                          <FileIcon className="w-12 h-12 text-muted-foreground" />
                          {file.progress === 100 && (
                            <div className="absolute -right-1 -bottom-1 bg-background rounded-full">
                              <CheckCircle className="w-4 h-4 text-primary" />
                            </div>
                          )}
                        </div>

                        {/* Информация о файле */}
                        <div className="flex-1 min-w-0">
                          <div className="flex flex-col gap-1 w-full">
                            <div className="flex items-center gap-2 min-w-0">
                              <h4
                                className="font-medium truncate"
                                title={file.name}
                              >
                                {file.name}
                              </h4>
                            </div>

                            <div className="flex items-center justify-between gap-2 text-sm text-muted-foreground">
                              <span>{formatFileSize(file.size)}</span>
                              <span className="flex items-center gap-1.5">
                                <span className="font-medium">
                                  {Math.round(file.progress)}%
                                </span>
                                {file.progress < 100 ? (
                                  <Loader2 className="w-3 h-3 animate-spin" />
                                ) : (
                                  <Trash2
                                    className="w-3 h-3 cursor-pointer hover:text-destructive transition-colors"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setFiles((prev) =>
                                        prev.filter((f) => f.id !== file.id),
                                      );
                                    }}
                                    aria-label="Удалить файл"
                                  />
                                )}
                              </span>
                            </div>
                          </div>

                          {/* Индикатор прогресса */}
                          <div className="w-full h-1.5 bg-muted rounded-full overflow-hidden mt-2">
                            <div
                              style={{ width: `${file.progress}%` }}
                              className={cn(
                                "h-full rounded-full",
                                file.progress < 100 ? "bg-primary" : "bg-green-500",
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <CardFooter className="px-0 flex justify-between">
              <Button variant="outline" type="button" onClick={handleCancel}>
                Отмена
              </Button>
              <Button type="submit" disabled={isSaving || isUploading}>
                {isSaving || isUploading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Сохранение...
                  </>
                ) : (
                  "Сохранить"
                )}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
