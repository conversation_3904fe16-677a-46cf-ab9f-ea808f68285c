/**
 * @file: ContractTemplateDetails.tsx
 * @description: Компонент для отображения деталей шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Edit, 
  Trash2, 
  FileText, 
  Plus, 
  History,
  AlertTriangle,
  Loader2,
  Calendar,
  Clock,
  Tag,
  Info
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { 
  useContractTemplate,
  useContractTemplateVersionsList
} from '@pactcrm/supabase-client';
import { formatDate } from '@/lib/utils';
import { ContractTemplateVersionsList } from './ContractTemplateVersionsList';

interface ContractTemplateDetailsProps {
  id: string;
}

/**
 * Компонент для отображения деталей шаблона договора
 */
export function ContractTemplateDetails({ id }: ContractTemplateDetailsProps) {
  const router = useRouter();
  const { template, loading, error, isSaving, removeTemplate } = useContractTemplate(id);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Обработчик редактирования шаблона
  const handleEdit = () => {
    router.push(`/contract-templates/${id}/edit`);
  };

  // Обработчик удаления шаблона
  const handleDelete = async () => {
    const success = await removeTemplate();
    
    if (success) {
      setIsDeleteDialogOpen(false);
      router.push('/contract-templates');
    }
  };

  // Обработчик создания новой версии
  const handleCreateVersion = () => {
    router.push(`/contract-templates/${id}/versions/new`);
  };

  // Обработчик управления версиями
  const handleManageVersions = () => {
    router.push(`/contract-templates/${id}/versions`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Загрузка...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
        <h2 className="text-xl font-semibold mb-2">Ошибка загрузки</h2>
        <p className="text-muted-foreground">{error.toString()}</p>
      </div>
    );
  }

  if (!template) {
    return (
      <div className="p-8 text-center">
        <AlertTriangle className="h-8 w-8 text-warning mx-auto mb-2" />
        <h2 className="text-xl font-semibold mb-2">Шаблон не найден</h2>
        <p className="text-muted-foreground">Шаблон договора с указанным ID не существует.</p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={() => router.push('/contract-templates')}
        >
          Вернуться к списку шаблонов
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{template.name}</h1>
          <p className="text-muted-foreground">{template.description}</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            Редактировать
          </Button>
          <Button variant="destructive" onClick={() => setIsDeleteDialogOpen(true)}>
            <Trash2 className="mr-2 h-4 w-4" />
            Удалить
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Тип шаблона</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Tag className="h-4 w-4 mr-2 text-muted-foreground" />
              <span>{template.type_name || 'Не указан'}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Статус</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={template.is_active ? "default" : "secondary"}>
              {template.is_active ? "Активен" : "Неактивен"}
            </Badge>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Версии</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <History className="h-4 w-4 mr-2 text-muted-foreground" />
              <span>{template.versions_count || 0} версий</span>
              {template.latest_version ? (
                <span className="ml-2 text-muted-foreground">
                  (последняя: v{template.latest_version})
                </span>
              ) : null}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Дата создания</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
              <span>{formatDate(template.created_at)}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Последнее обновление</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
              <span>{formatDate(template.updated_at)}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="versions" className="w-full">
        <TabsList>
          <TabsTrigger value="versions">Версии шаблона</TabsTrigger>
          <TabsTrigger value="info">Дополнительная информация</TabsTrigger>
        </TabsList>
        <TabsContent value="versions" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Версии шаблона</h2>
            <div className="flex space-x-2">
              <Button onClick={handleCreateVersion}>
                <Plus className="mr-2 h-4 w-4" />
                Создать версию
              </Button>
              <Button variant="outline" onClick={handleManageVersions}>
                <History className="mr-2 h-4 w-4" />
                Управление версиями
              </Button>
            </div>
          </div>
          <ContractTemplateVersionsList templateId={id} limit={5} />
        </TabsContent>
        <TabsContent value="info">
          <Card>
            <CardHeader>
              <CardTitle>Дополнительная информация</CardTitle>
              <CardDescription>
                Подробная информация о шаблоне договора
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium mb-1">ID шаблона</h3>
                  <p className="text-sm text-muted-foreground">{template.id}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">ID арендатора</h3>
                  <p className="text-sm text-muted-foreground">{template.tenant_id}</p>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-1">Описание</h3>
                <p className="text-sm text-muted-foreground">
                  {template.description || 'Описание отсутствует'}
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Вы уверены?</AlertDialogTitle>
            <AlertDialogDescription>
              Это действие нельзя отменить. Шаблон договора и все его версии будут удалены безвозвратно.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Отмена</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              disabled={isSaving}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Удаление...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Удалить
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
