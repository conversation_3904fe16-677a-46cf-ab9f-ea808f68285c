/**
 * @file: TemplateVariablesEditor.tsx
 * @description: Компонент для редактирования переменных шаблона договора
 * @dependencies: react, @pactcrm/ui
 * @created: 2024-05-11
 */

"use client";

import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Save, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { TemplateVariable } from '@pactcrm/supabase-client';

interface TemplateVariablesEditorProps {
  variables: TemplateVariable[];
  onChange: (variables: TemplateVariable[]) => void;
  readOnly?: boolean;
}

/**
 * Компонент для редактирования переменных шаблона договора
 */
export function TemplateVariablesEditor({ 
  variables = [], 
  onChange,
  readOnly = false
}: TemplateVariablesEditorProps) {
  const [localVariables, setLocalVariables] = useState<TemplateVariable[]>(variables);
  const [hasErrors, setHasErrors] = useState(false);

  // Обновляем локальные переменные при изменении входных данных
  useEffect(() => {
    setLocalVariables(variables);
  }, [variables]);

  // Проверяем наличие ошибок в переменных
  useEffect(() => {
    const errors = localVariables.some(variable => !variable.name);
    setHasErrors(errors);
  }, [localVariables]);

  // Обработчик добавления новой переменной
  const handleAddVariable = () => {
    const newVariable: TemplateVariable = {
      name: '',
      description: '',
      type: 'string',
      required: false,
    };
    
    setLocalVariables([...localVariables, newVariable]);
  };

  // Обработчик удаления переменной
  const handleRemoveVariable = (index: number) => {
    const updatedVariables = [...localVariables];
    updatedVariables.splice(index, 1);
    setLocalVariables(updatedVariables);
    onChange(updatedVariables);
  };

  // Обработчик изменения переменной
  const handleVariableChange = (index: number, field: keyof TemplateVariable, value: any) => {
    const updatedVariables = [...localVariables];
    updatedVariables[index] = {
      ...updatedVariables[index],
      [field]: value,
    };
    setLocalVariables(updatedVariables);
  };

  // Обработчик сохранения переменных
  const handleSave = () => {
    if (!hasErrors) {
      onChange(localVariables);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Переменные шаблона</CardTitle>
        <CardDescription>
          Управление переменными, используемыми в шаблоне договора
        </CardDescription>
      </CardHeader>
      <CardContent>
        {hasErrors && (
          <div className="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md flex items-center text-destructive">
            <AlertTriangle className="h-5 w-5 mr-2" />
            <p>Пожалуйста, исправьте ошибки перед сохранением.</p>
          </div>
        )}

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Название</TableHead>
                <TableHead>Описание</TableHead>
                <TableHead>Тип</TableHead>
                <TableHead>Обязательная</TableHead>
                {!readOnly && <TableHead className="w-[100px]">Действия</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {localVariables.length > 0 ? (
                localVariables.map((variable, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Input
                        value={variable.name}
                        onChange={(e) => handleVariableChange(index, 'name', e.target.value)}
                        placeholder="Название переменной"
                        className={!variable.name ? 'border-destructive' : ''}
                        readOnly={readOnly}
                      />
                      {!variable.name && (
                        <p className="text-xs text-destructive mt-1">Обязательное поле</p>
                      )}
                    </TableCell>
                    <TableCell>
                      <Input
                        value={variable.description || ''}
                        onChange={(e) => handleVariableChange(index, 'description', e.target.value)}
                        placeholder="Описание переменной"
                        readOnly={readOnly}
                      />
                    </TableCell>
                    <TableCell>
                      <Select
                        value={variable.type}
                        onValueChange={(value) => handleVariableChange(index, 'type', value)}
                        disabled={readOnly}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Тип" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="string">Строка</SelectItem>
                          <SelectItem value="number">Число</SelectItem>
                          <SelectItem value="date">Дата</SelectItem>
                          <SelectItem value="boolean">Логический</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center justify-center">
                        <Switch
                          checked={variable.required}
                          onCheckedChange={(checked) => handleVariableChange(index, 'required', checked)}
                          disabled={readOnly}
                        />
                      </div>
                    </TableCell>
                    {!readOnly && (
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveVariable(index)}
                          className="text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    )}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={readOnly ? 4 : 5} className="h-24 text-center">
                    Переменные не определены
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      {!readOnly && (
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={handleAddVariable}
          >
            <Plus className="mr-2 h-4 w-4" />
            Добавить переменную
          </Button>
          <Button
            onClick={handleSave}
            disabled={hasErrors}
          >
            <Save className="mr-2 h-4 w-4" />
            Сохранить
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
