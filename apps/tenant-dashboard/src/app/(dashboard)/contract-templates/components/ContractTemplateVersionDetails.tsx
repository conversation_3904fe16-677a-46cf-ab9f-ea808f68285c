/**
 * @file: ContractTemplateVersionDetails.tsx
 * @description: Компонент для отображения деталей версии шаблона договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Edit, 
  Trash2, 
  Download, 
  FileText, 
  AlertTriangle,
  Loader2,
  Calendar,
  Clock,
  Code,
  User,
  FileUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { 
  useContractTemplateVersion
} from '@pactcrm/supabase-client';
import { formatDate } from '@/lib/utils';

interface ContractTemplateVersionDetailsProps {
  templateId: string;
  versionId: string;
}

/**
 * Компонент для отображения деталей версии шаблона договора
 */
export function ContractTemplateVersionDetails({ templateId, versionId }: ContractTemplateVersionDetailsProps) {
  const router = useRouter();
  const { version, loading, error, isSaving, removeVersion, removeFile } = useContractTemplateVersion(versionId, templateId);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isDeleteFileDialogOpen, setIsDeleteFileDialogOpen] = useState(false);
  const [deletingFileId, setDeletingFileId] = useState<string | null>(null);

  // Обработчик редактирования версии
  const handleEdit = () => {
    router.push(`/contract-templates/${templateId}/versions/${versionId}/edit`);
  };

  // Обработчик удаления версии
  const handleDelete = async () => {
    const success = await removeVersion();
    
    if (success) {
      setIsDeleteDialogOpen(false);
      router.push(`/contract-templates/${templateId}/versions`);
    }
  };

  // Обработчик удаления файла
  const handleDeleteFile = async () => {
    if (!deletingFileId) return;
    
    const success = await removeFile(deletingFileId);
    
    if (success) {
      setIsDeleteFileDialogOpen(false);
      setDeletingFileId(null);
    }
  };

  // Обработчик скачивания файла
  const handleDownloadFile = (fileUrl: string) => {
    window.open(fileUrl, '_blank');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Загрузка...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <AlertTriangle className="h-8 w-8 text-destructive mx-auto mb-2" />
        <h2 className="text-xl font-semibold mb-2">Ошибка загрузки</h2>
        <p className="text-muted-foreground">{error.toString()}</p>
      </div>
    );
  }

  if (!version) {
    return (
      <div className="p-8 text-center">
        <AlertTriangle className="h-8 w-8 text-warning mx-auto mb-2" />
        <h2 className="text-xl font-semibold mb-2">Версия не найдена</h2>
        <p className="text-muted-foreground">Версия шаблона договора с указанным ID не существует.</p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={() => router.push(`/contract-templates/${templateId}/versions`)}
        >
          Вернуться к списку версий
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Версия {version.version_number}</h1>
          <p className="text-muted-foreground">
            {version.template_name ? `Шаблон: ${version.template_name}` : 'Шаблон договора'}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="mr-2 h-4 w-4" />
            Редактировать
          </Button>
          <Button variant="destructive" onClick={() => setIsDeleteDialogOpen(true)}>
            <Trash2 className="mr-2 h-4 w-4" />
            Удалить
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Статус</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={version.is_active ? "default" : "secondary"}>
              {version.is_active ? "Активна" : "Неактивна"}
            </Badge>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Дата создания</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
              <span>{formatDate(version.created_at)}</span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Последнее обновление</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
              <span>{formatDate(version.updated_at)}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="content" className="w-full">
        <TabsList>
          <TabsTrigger value="content">Содержимое</TabsTrigger>
          <TabsTrigger value="variables">Переменные</TabsTrigger>
          <TabsTrigger value="files">Файлы</TabsTrigger>
        </TabsList>
        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Содержимое версии</CardTitle>
              <CardDescription>
                Текст шаблона договора с переменными
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-md bg-muted/50 whitespace-pre-wrap">
                {version.content || 'Содержимое отсутствует'}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="variables" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Переменные шаблона</CardTitle>
              <CardDescription>
                Список переменных, используемых в шаблоне
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-md bg-muted/50 font-mono text-sm">
                <pre>{version.variables ? JSON.stringify(version.variables, null, 2) : '{}'}</pre>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="files" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Файлы версии</CardTitle>
              <CardDescription>
                Файлы, прикрепленные к версии шаблона
              </CardDescription>
            </CardHeader>
            <CardContent>
              {version.files && version.files.length > 0 ? (
                <div className="space-y-4">
                  {version.files.map((file) => (
                    <div key={file.id} className="flex items-center justify-between p-4 border rounded-md">
                      <div className="flex items-center">
                        <FileText className="h-6 w-6 mr-3 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{file.file_name}</p>
                          <p className="text-sm text-muted-foreground">
                            Загружен: {formatDate(file.created_at)}
                          </p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDownloadFile(file.file_path)}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Скачать
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="text-destructive"
                          onClick={() => {
                            setDeletingFileId(file.id);
                            setIsDeleteFileDialogOpen(true);
                          }}
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Удалить
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-8 text-center border rounded-md">
                  <FileUp className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-muted-foreground">К этой версии не прикреплено файлов.</p>
                  <Button 
                    variant="outline" 
                    className="mt-4"
                    onClick={handleEdit}
                  >
                    Загрузить файлы
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Вы уверены?</AlertDialogTitle>
            <AlertDialogDescription>
              Это действие нельзя отменить. Версия шаблона договора и все связанные файлы будут удалены безвозвратно.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Отмена</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              disabled={isSaving}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Удаление...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Удалить
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={isDeleteFileDialogOpen} onOpenChange={setIsDeleteFileDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Удалить файл?</AlertDialogTitle>
            <AlertDialogDescription>
              Это действие нельзя отменить. Файл будет удален безвозвратно.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Отмена</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteFile}
              disabled={isSaving}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Удаление...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Удалить
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
