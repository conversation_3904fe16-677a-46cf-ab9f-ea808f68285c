/**
 * @file: ContractTemplatesFilters.tsx
 * @description: Компонент для фильтрации шаблонов договоров
 * @dependencies: react, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-11
 */

"use client";

import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';
import { 
  ContractTemplateFilters, 
  useContractTemplateTypes 
} from '@pactcrm/supabase-client';

interface ContractTemplatesFiltersProps {
  filters: ContractTemplateFilters;
  onFiltersChange: (filters: ContractTemplateFilters) => void;
}

/**
 * Компонент для фильтрации шаблонов договоров
 */
export function ContractTemplatesFilters({ filters, onFiltersChange }: ContractTemplatesFiltersProps) {
  const { types, loading: typesLoading } = useContractTemplateTypes();
  const [startDate, setStartDate] = useState<Date | undefined>(
    filters.start_date ? new Date(filters.start_date) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    filters.end_date ? new Date(filters.end_date) : undefined
  );

  // Обновляем фильтры при изменении дат
  useEffect(() => {
    const newFilters = { ...filters };
    
    if (startDate) {
      newFilters.start_date = format(startDate, 'yyyy-MM-dd');
    } else {
      delete newFilters.start_date;
    }
    
    if (endDate) {
      newFilters.end_date = format(endDate, 'yyyy-MM-dd');
    } else {
      delete newFilters.end_date;
    }
    
    onFiltersChange(newFilters);
  }, [startDate, endDate]);

  // Обработчик изменения типа шаблона
  const handleTypeChange = (value: string) => {
    const newFilters = { ...filters };
    
    if (value === 'all') {
      delete newFilters.type_id;
    } else {
      newFilters.type_id = value;
    }
    
    onFiltersChange(newFilters);
  };

  // Обработчик изменения статуса шаблона
  const handleStatusChange = (value: string) => {
    const newFilters = { ...filters };
    
    if (value === 'all') {
      delete newFilters.is_active;
    } else {
      newFilters.is_active = value === 'active';
    }
    
    onFiltersChange(newFilters);
  };

  // Обработчик сброса фильтров
  const handleResetFilters = () => {
    setStartDate(undefined);
    setEndDate(undefined);
    onFiltersChange({});
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Тип шаблона</label>
          <Select
            value={filters.type_id || 'all'}
            onValueChange={handleTypeChange}
            disabled={typesLoading}
          >
            <SelectTrigger>
              <SelectValue placeholder="Все типы" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Все типы</SelectItem>
              {types.map((type) => (
                <SelectItem key={type.id} value={type.id}>
                  {type.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Статус</label>
          <Select
            value={filters.is_active !== undefined ? (filters.is_active ? 'active' : 'inactive') : 'all'}
            onValueChange={handleStatusChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Все статусы" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Все статусы</SelectItem>
              <SelectItem value="active">Активен</SelectItem>
              <SelectItem value="inactive">Неактивен</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Дата создания</label>
          <div className="flex space-x-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  {startDate ? format(startDate, 'dd.MM.yyyy') : 'От'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={setStartDate}
                  initialFocus
                  locale={ru}
                />
              </PopoverContent>
            </Popover>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start text-left font-normal"
                >
                  {endDate ? format(endDate, 'dd.MM.yyyy') : 'До'}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={setEndDate}
                  initialFocus
                  locale={ru}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          variant="outline"
          onClick={handleResetFilters}
          className="flex items-center"
        >
          <X className="mr-2 h-4 w-4" />
          Сбросить фильтры
        </Button>
      </div>
    </div>
  );
}
