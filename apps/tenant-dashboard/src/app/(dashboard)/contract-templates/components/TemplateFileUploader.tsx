/**
 * @file: TemplateFileUploader.tsx
 * @description: Компонент для загрузки файлов шаблона договора
 * @dependencies: react, @pactcrm/ui
 * @created: 2024-05-11
 */

"use client";

import React, { useState, useRef, ChangeEvent } from 'react';
import { 
  Paperclip, 
  X, 
  File as FileIcon, 
  Trash2, 
  Loader2, 
  CheckCircle,
  Upload
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { cn } from '@/lib/utils';

// Интерфейс для файла с превью
interface FileWithPreview {
  id: string;
  preview: string;
  progress: number;
  name: string;
  size: number;
  type: string;
  lastModified?: number;
  file?: File;
}

interface TemplateFileUploaderProps {
  onUpload: (files: File[]) => Promise<void>;
  isUploading?: boolean;
  maxFiles?: number;
  acceptedFileTypes?: string;
}

/**
 * Компонент для загрузки файлов шаблона договора
 */
export function TemplateFileUploader({ 
  onUpload, 
  isUploading = false,
  maxFiles = 5,
  acceptedFileTypes = "application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
}: TemplateFileUploaderProps) {
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Обработчик загрузки файлов
  const handleFiles = (fileList: FileList) => {
    if (files.length >= maxFiles) {
      return;
    }

    const remainingSlots = maxFiles - files.length;
    const filesToAdd = Array.from(fileList).slice(0, remainingSlots);
    
    const newFiles = filesToAdd.map((file) => ({
      id: `${URL.createObjectURL(file)}-${Date.now()}`,
      preview: URL.createObjectURL(file),
      progress: 0,
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified,
      file,
    }));
    
    setFiles((prev) => [...prev, ...newFiles]);
    newFiles.forEach((f) => simulateUpload(f.id));
  };

  // Имитация загрузки файла
  const simulateUpload = (id: string) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 15;
      setFiles((prev) =>
        prev.map((f) =>
          f.id === id ? { ...f, progress: Math.min(progress, 100) } : f,
        ),
      );
      if (progress >= 100) {
        clearInterval(interval);
      }
    }, 300);
  };

  // Обработчики событий перетаскивания
  const onDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFiles(e.dataTransfer.files);
  };

  const onDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const onDragLeave = () => setIsDragging(false);

  // Обработчик выбора файлов через диалог
  const onSelect = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) handleFiles(e.target.files);
  };

  // Форматирование размера файла
  const formatFileSize = (bytes: number): string => {
    if (!bytes) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${(bytes / Math.pow(k, i)).toFixed(2)} ${sizes[i]}`;
  };

  // Обработчик удаления файла
  const handleRemoveFile = (id: string) => {
    setFiles((prev) => prev.filter((f) => f.id !== id));
  };

  // Обработчик очистки всех файлов
  const handleClearFiles = () => {
    setFiles([]);
  };

  // Обработчик загрузки файлов на сервер
  const handleUploadFiles = async () => {
    const filesToUpload = files.filter(f => f.file).map(f => f.file!);
    await onUpload(filesToUpload);
    setFiles([]);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Загрузка файлов</CardTitle>
        <CardDescription>
          Загрузите файлы шаблона договора (PDF, DOC, DOCX)
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div
          onDragOver={onDragOver}
          onDragLeave={onDragLeave}
          onDrop={onDrop}
          onClick={() => fileInputRef.current?.click()}
          className={cn(
            "relative rounded-lg p-6 text-center cursor-pointer border-2 border-dashed border-border hover:border-primary/50 transition-colors",
            isDragging && "border-primary"
          )}
        >
          <div className="flex flex-col items-center gap-3">
            <div className="relative">
              <Paperclip className="w-10 h-10 text-muted-foreground" />
            </div>

            <div className="space-y-1">
              <h3 className="text-lg font-medium">
                {isDragging
                  ? "Отпустите файлы здесь"
                  : files.length
                    ? "Добавить еще файлы"
                    : "Загрузить файлы"}
              </h3>
              <p className="text-sm text-muted-foreground">
                {isDragging ? (
                  <span className="font-medium text-primary">
                    Отпустите для загрузки
                  </span>
                ) : (
                  <>
                    Перетащите файлы сюда или{" "}
                    <span className="text-primary font-medium">выберите</span>
                  </>
                )}
              </p>
              <p className="text-xs text-muted-foreground">
                Максимум {maxFiles} файлов. Поддерживаемые форматы: PDF, DOC, DOCX
              </p>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              multiple
              hidden
              onChange={onSelect}
              accept={acceptedFileTypes}
            />
          </div>
        </div>

        {/* Список файлов */}
        {files.length > 0 && (
          <div>
            <div className="flex justify-between items-center mb-2">
              <h4 className="font-medium">
                Выбранные файлы ({files.length}/{maxFiles})
              </h4>
              {files.length > 1 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearFiles}
                >
                  Очистить все
                </Button>
              )}
            </div>

            <div className="space-y-2 max-h-[300px] overflow-y-auto pr-2">
              {files.map((file) => (
                <div
                  key={file.id}
                  className="p-3 flex items-start gap-3 rounded-lg bg-muted/50"
                >
                  {/* Иконка файла */}
                  <div className="relative flex-shrink-0">
                    <FileIcon className="w-12 h-12 text-muted-foreground" />
                    {file.progress === 100 && (
                      <div className="absolute -right-1 -bottom-1 bg-background rounded-full">
                        <CheckCircle className="w-4 h-4 text-primary" />
                      </div>
                    )}
                  </div>

                  {/* Информация о файле */}
                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col gap-1 w-full">
                      <div className="flex items-center gap-2 min-w-0">
                        <h4
                          className="font-medium truncate"
                          title={file.name}
                        >
                          {file.name}
                        </h4>
                      </div>

                      <div className="flex items-center justify-between gap-2 text-sm text-muted-foreground">
                        <span>{formatFileSize(file.size)}</span>
                        <span className="flex items-center gap-1.5">
                          <span className="font-medium">
                            {Math.round(file.progress)}%
                          </span>
                          {file.progress < 100 ? (
                            <Loader2 className="w-3 h-3 animate-spin" />
                          ) : (
                            <Trash2
                              className="w-3 h-3 cursor-pointer hover:text-destructive transition-colors"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveFile(file.id);
                              }}
                              aria-label="Удалить файл"
                            />
                          )}
                        </span>
                      </div>
                    </div>

                    {/* Индикатор прогресса */}
                    <div className="w-full h-1.5 bg-muted rounded-full overflow-hidden mt-2">
                      <div
                        style={{ width: `${file.progress}%` }}
                        className={cn(
                          "h-full rounded-full",
                          file.progress < 100 ? "bg-primary" : "bg-green-500",
                        )}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button
          className="w-full"
          onClick={handleUploadFiles}
          disabled={files.length === 0 || isUploading}
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Загрузка...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Загрузить файлы
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
