/**
 * @file: contracts/[id]/edit/page.tsx
 * @description: Страница редактирования договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-10
 */

import React from 'react';
import { ContractForm } from '../../components/ContractForm';
import { PermissionGuard } from '@pactcrm/supabase-client';

interface EditContractPageProps {
  params: {
    id: string;
  };
}

export default function EditContractPage({ params }: EditContractPageProps) {
  return (
    <PermissionGuard 
      resource="contracts" 
      action="update"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на редактирование договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Редактирование договора</h1>
        <ContractForm id={params.id} />
      </div>
    </PermissionGuard>
  );
}
