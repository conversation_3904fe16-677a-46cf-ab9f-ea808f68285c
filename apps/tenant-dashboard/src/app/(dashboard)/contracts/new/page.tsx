/**
 * @file: contracts/new/page.tsx
 * @description: Страница создания нового договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-10
 */

import React from 'react';
import { ContractForm } from '../components/ContractForm';
import { PermissionGuard } from '@pactcrm/supabase-client';

export default function NewContractPage() {
  return (
    <PermissionGuard 
      resource="contracts" 
      action="create"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на создание договоров.
          </p>
        </div>
      }
    >
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Создание нового договора</h1>
        <ContractForm />
      </div>
    </PermissionGuard>
  );
}
