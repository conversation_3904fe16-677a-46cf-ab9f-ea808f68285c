/**
 * @file: ContractDetails.tsx
 * @description: Компонент для отображения деталей договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-10
 */

"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Edit,
  Trash2,
  FileText,
  Download,
  AlertTriangle,
  Loader2
} from 'lucide-react';
import { DocumentGenerationDialog } from './DocumentGenerationDialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  useContract,
  ContractStatus,
  PermissionGuard
} from '@pactcrm/supabase-client';
import { formatCurrency, formatDate } from '@/lib/utils';
import { PaymentsList } from '../../payments/components/PaymentsList';

interface ContractDetailsProps {
  id: string;
}

/**
 * Компонент для отображения деталей договора
 */
export function ContractDetails({ id }: ContractDetailsProps) {
  const router = useRouter();
  const { contract, loading, error, isSaving, removeContract } = useContract(id);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false);

  // Обработчик редактирования договора
  const handleEdit = () => {
    router.push(`/contracts/${id}/edit`);
  };

  // Обработчик удаления договора
  const handleDelete = async () => {
    const success = await removeContract();

    if (success) {
      setIsDeleteDialogOpen(false);
      router.push('/contracts');
    }
  };

  // Обработчик генерации документа
  const handleGenerateDocument = () => {
    setIsGenerateDialogOpen(true);
  };

  // Обработчик успешной генерации документа
  const handleGenerateSuccess = (document: any) => {
    console.log('Документ успешно сгенерирован:', document);
    setIsGenerateDialogOpen(false);
  };

  // Если идет загрузка данных, показываем индикатор загрузки
  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Если произошла ошибка, показываем сообщение об ошибке
  if (error) {
    return (
      <div className="bg-destructive/10 text-destructive p-4 rounded-md">
        Ошибка при загрузке договора: {error.message}
      </div>
    );
  }

  // Если договор не найден, показываем сообщение
  if (!contract) {
    return (
      <div className="bg-muted p-8 rounded-md text-center">
        <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">Договор не найден</h3>
        <p className="text-muted-foreground mt-2">
          Договор с указанным ID не существует или был удален
        </p>
        <Button
          variant="outline"
          className="mt-4"
          onClick={() => router.push('/contracts')}
        >
          Вернуться к списку договоров
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h2 className="text-xl font-semibold">Договор №{contract.number}</h2>
          <ContractStatusBadge status={contract.status} />
        </div>
        <div className="flex space-x-2">
          <PermissionGuard resource="contracts" action="update">
            <Button variant="outline" onClick={handleEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Редактировать
            </Button>
          </PermissionGuard>

          <Button variant="outline" onClick={handleGenerateDocument}>
            <Download className="mr-2 h-4 w-4" />
            Скачать PDF
          </Button>

          <PermissionGuard resource="contracts" action="delete">
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Удалить
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Удаление договора</DialogTitle>
                  <DialogDescription>
                    Вы уверены, что хотите удалить договор №{contract.number}?
                    Это действие нельзя отменить.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsDeleteDialogOpen(false)}
                    disabled={isSaving}
                  >
                    Отмена
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDelete}
                    disabled={isSaving}
                  >
                    {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Удалить
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </PermissionGuard>
        </div>
      </div>

      <Tabs defaultValue="details">
        <TabsList>
          <TabsTrigger value="details">Детали договора</TabsTrigger>
          <TabsTrigger value="payments">Платежи</TabsTrigger>
          <TabsTrigger value="documents">Документы</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Информация о договоре</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="font-medium">Номер договора:</dt>
                    <dd>{contract.number}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Дата подписания:</dt>
                    <dd>{formatDate(contract.signed_date)}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Дата начала:</dt>
                    <dd>{formatDate(contract.start_date)}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Дата окончания:</dt>
                    <dd>{formatDate(contract.end_date)}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Статус:</dt>
                    <dd>
                      <ContractStatusBadge status={contract.status} />
                    </dd>
                  </div>
                </dl>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Финансовая информация</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="font-medium">Общая сумма:</dt>
                    <dd className="font-semibold">{formatCurrency(contract.total_amount)}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Первоначальный взнос:</dt>
                    <dd>{formatCurrency(contract.initial_payment)}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Ежемесячный платеж:</dt>
                    <dd>{formatCurrency(contract.monthly_payment)}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Оплачено:</dt>
                    <dd>{formatCurrency(0)}</dd> {/* TODO: Добавить расчет оплаченной суммы */}
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Осталось оплатить:</dt>
                    <dd>{formatCurrency(contract.total_amount)}</dd> {/* TODO: Добавить расчет оставшейся суммы */}
                  </div>
                </dl>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Информация о клиенте</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="font-medium">ФИО:</dt>
                    <dd>{contract.client_name || 'Не указано'}</dd>
                  </div>
                  {/* TODO: Добавить дополнительную информацию о клиенте */}
                </dl>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Информация об объекте</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="space-y-2">
                  <div className="flex justify-between">
                    <dt className="font-medium">Жилой комплекс:</dt>
                    <dd>{contract.complex_name || 'Не указано'}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Здание:</dt>
                    <dd>{contract.building_name || 'Не указано'}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="font-medium">Квартира:</dt>
                    <dd>{contract.apartment_number || 'Не указано'}</dd>
                  </div>
                  {/* TODO: Добавить дополнительную информацию об объекте */}
                </dl>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="payments" className="pt-4">
          <PaymentsList contractId={id} />
        </TabsContent>

        <TabsContent value="documents" className="pt-4">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium">Документы</h3>
                <p className="text-muted-foreground mt-2">
                  Здесь будут отображаться документы, связанные с договором
                </p>
                <Button className="mt-4">
                  Загрузить документ
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Диалог генерации документа */}
      <DocumentGenerationDialog
        contractId={id}
        isOpen={isGenerateDialogOpen}
        onClose={() => setIsGenerateDialogOpen(false)}
        onSuccess={handleGenerateSuccess}
      />
    </div>
  );
}

/**
 * Компонент для отображения статуса договора
 */
function ContractStatusBadge({ status }: { status: string }) {
  let variant: 'default' | 'secondary' | 'destructive' | 'outline';
  let label: string;

  switch (status) {
    case ContractStatus.DRAFT:
      variant = 'outline';
      label = 'Черновик';
      break;
    case ContractStatus.PENDING:
      variant = 'secondary';
      label = 'Ожидает подписания';
      break;
    case ContractStatus.ACTIVE:
      variant = 'default';
      label = 'Активен';
      break;
    case ContractStatus.COMPLETED:
      variant = 'default';
      label = 'Завершен';
      break;
    case ContractStatus.CANCELLED:
      variant = 'destructive';
      label = 'Отменен';
      break;
    default:
      variant = 'outline';
      label = status;
  }

  return <Badge variant={variant}>{label}</Badge>;
}
