/**
 * @file: ContractsFilters.tsx
 * @description: Компонент для фильтрации договоров
 * @dependencies: react, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-10
 */

"use client";

import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { ru } from 'date-fns/locale';
import { ContractFilters, ContractStatus } from '@pactcrm/supabase-client';

interface ContractsFiltersProps {
  filters: ContractFilters;
  onFiltersChange: (filters: ContractFilters) => void;
}

/**
 * Компонент для фильтрации договоров
 */
export function ContractsFilters({ filters, onFiltersChange }: ContractsFiltersProps) {
  const [startDate, setStartDate] = useState<Date | undefined>(
    filters.start_date ? new Date(filters.start_date) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    filters.end_date ? new Date(filters.end_date) : undefined
  );

  // Обновляем фильтры при изменении дат
  useEffect(() => {
    const newFilters = { ...filters };
    
    if (startDate) {
      newFilters.start_date = format(startDate, 'yyyy-MM-dd');
    } else {
      delete newFilters.start_date;
    }
    
    if (endDate) {
      newFilters.end_date = format(endDate, 'yyyy-MM-dd');
    } else {
      delete newFilters.end_date;
    }
    
    onFiltersChange(newFilters);
  }, [startDate, endDate]);

  // Обработчик изменения статуса
  const handleStatusChange = (value: string) => {
    const newFilters = { ...filters };
    
    if (value === 'all') {
      delete newFilters.status;
    } else {
      newFilters.status = value;
    }
    
    onFiltersChange(newFilters);
  };

  // Обработчик сброса фильтров
  const handleResetFilters = () => {
    setStartDate(undefined);
    setEndDate(undefined);
    onFiltersChange({});
  };

  // Проверяем, есть ли активные фильтры
  const hasActiveFilters = Object.keys(filters).length > 0 && 
    (filters.status || filters.start_date || filters.end_date);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Статус договора</label>
          <Select
            value={filters.status || 'all'}
            onValueChange={handleStatusChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Все статусы" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Все статусы</SelectItem>
              <SelectItem value={ContractStatus.DRAFT}>Черновик</SelectItem>
              <SelectItem value={ContractStatus.PENDING}>Ожидает подписания</SelectItem>
              <SelectItem value={ContractStatus.ACTIVE}>Активен</SelectItem>
              <SelectItem value={ContractStatus.COMPLETED}>Завершен</SelectItem>
              <SelectItem value={ContractStatus.CANCELLED}>Отменен</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Дата подписания (от)</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                {startDate ? (
                  format(startDate, 'dd.MM.yyyy')
                ) : (
                  <span className="text-muted-foreground">Выберите дату</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={setStartDate}
                initialFocus
                locale={ru}
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Дата подписания (до)</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                {endDate ? (
                  format(endDate, 'dd.MM.yyyy')
                ) : (
                  <span className="text-muted-foreground">Выберите дату</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={setEndDate}
                initialFocus
                locale={ru}
                disabled={(date) => 
                  startDate ? date < startDate : false
                }
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {hasActiveFilters && (
        <div className="flex justify-end">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleResetFilters}
            className="h-8 px-2 text-xs"
          >
            <X className="mr-1 h-3 w-3" />
            Сбросить фильтры
          </Button>
        </div>
      )}
    </div>
  );
}
