/**
 * @file: DocumentGenerationDialog.tsx
 * @description: Диалог для генерации документов договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-12-26
 */

import React, { useState } from 'react';
import { 
  FileText, 
  Download, 
  Loader2,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  useContractTemplates,
  useContractDocuments,
  useSupabaseClient,
  useUser,
  generateContractDocument
} from '@pactcrm/supabase-client';

interface DocumentGenerationDialogProps {
  contractId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (document: any) => void;
}

/**
 * Компонент диалога для генерации документов договора
 */
export function DocumentGenerationDialog({
  contractId,
  isOpen,
  onClose,
  onSuccess,
}: DocumentGenerationDialogProps) {
  const supabase = useSupabaseClient();
  const { user } = useUser();
  const { templates, loading: templatesLoading } = useContractTemplates();
  const { generateDocument, isGenerating, generateError } = useContractDocuments();
  
  const [selectedTemplateVersionId, setSelectedTemplateVersionId] = useState<string>('');
  const [generatedDocument, setGeneratedDocument] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Обработчик генерации документа
  const handleGenerate = async () => {
    if (!selectedTemplateVersionId || !user?.id) {
      setError('Выберите шаблон для генерации документа');
      return;
    }

    try {
      setError(null);
      
      const document = await generateDocument(contractId, selectedTemplateVersionId);
      
      if (document) {
        setGeneratedDocument(document);
        onSuccess?.(document);
      }
    } catch (err) {
      console.error('Ошибка при генерации документа:', err);
      setError(err instanceof Error ? err.message : 'Произошла ошибка при генерации документа');
    }
  };

  // Обработчик скачивания документа
  const handleDownload = () => {
    if (generatedDocument?.file_path) {
      window.open(generatedDocument.file_path, '_blank');
    }
  };

  // Обработчик закрытия диалога
  const handleClose = () => {
    setSelectedTemplateVersionId('');
    setGeneratedDocument(null);
    setError(null);
    onClose();
  };

  // Получаем доступные версии шаблонов
  const templateVersions = templates?.flatMap(template => 
    template.versions?.map(version => ({
      id: version.id,
      name: `${template.name} (v${version.version})`,
      templateName: template.name,
      version: version.version,
    })) || []
  ) || [];

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Генерация документа</span>
          </DialogTitle>
          <DialogDescription>
            Выберите шаблон для генерации PDF документа договора
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Выбор шаблона */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Шаблон документа</label>
            <Select
              value={selectedTemplateVersionId}
              onValueChange={setSelectedTemplateVersionId}
              disabled={templatesLoading || isGenerating}
            >
              <SelectTrigger>
                <SelectValue placeholder="Выберите шаблон..." />
              </SelectTrigger>
              <SelectContent>
                {templateVersions.map((version) => (
                  <SelectItem key={version.id} value={version.id}>
                    {version.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Ошибки */}
          {(error || generateError) && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {error || generateError?.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Успешная генерация */}
          {generatedDocument && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Документ успешно сгенерирован: {generatedDocument.file_name}
              </AlertDescription>
            </Alert>
          )}

          {/* Состояние загрузки */}
          {isGenerating && (
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Генерация документа...</span>
            </div>
          )}
        </div>

        <DialogFooter className="flex space-x-2">
          <Button variant="outline" onClick={handleClose}>
            Отмена
          </Button>
          
          {generatedDocument ? (
            <Button onClick={handleDownload}>
              <Download className="mr-2 h-4 w-4" />
              Скачать
            </Button>
          ) : (
            <Button 
              onClick={handleGenerate}
              disabled={!selectedTemplateVersionId || isGenerating || templatesLoading}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Генерация...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-4 w-4" />
                  Сгенерировать
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
