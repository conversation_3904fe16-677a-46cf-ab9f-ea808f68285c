/**
 * @file: ContractsList.tsx
 * @description: Компонент для отображения списка договоров
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2024-05-10
 */

"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { FileText, Plus, Search, Filter, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTable } from '@/components/ui-wrappers';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { useContractsList, ContractStatus } from '@pactcrm/supabase-client';
import { ContractsFilters } from './ContractsFilters';
import { formatCurrency, formatDate } from '@/lib/utils';

/**
 * Компонент для отображения списка договоров
 */
export function ContractsList() {
  const router = useRouter();
  const [showFilters, setShowFilters] = useState(false);
  
  const { 
    contracts, 
    count, 
    page, 
    limit, 
    totalPages,
    loading, 
    error,
    filters,
    updateFilters,
    nextPage,
    prevPage,
    updatePagination
  } = useContractsList();

  // Обработчик клика по строке таблицы
  const handleRowClick = (contract: any) => {
    router.push(`/contracts/${contract.id}`);
  };

  // Обработчик создания нового договора
  const handleCreateContract = () => {
    router.push('/contracts/new');
  };

  // Обработчик изменения поискового запроса
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateFilters({ ...filters, search: e.target.value });
  };

  // Обработчик изменения количества элементов на странице
  const handleLimitChange = (value: string) => {
    updatePagination({ limit: parseInt(value) });
  };

  // Колонки для таблицы договоров
  const columns = [
    {
      header: 'Номер',
      accessorKey: 'number',
    },
    {
      header: 'Клиент',
      accessorKey: 'client_name',
    },
    {
      header: 'Объект',
      cell: (item: any) => (
        <span>
          {item.complex_name && item.building_name && item.apartment_number
            ? `${item.complex_name}, ${item.building_name}, кв. ${item.apartment_number}`
            : 'Не указан'}
        </span>
      ),
    },
    {
      header: 'Дата подписания',
      accessorKey: 'signed_date',
      cell: (item: any) => formatDate(item.signed_date),
    },
    {
      header: 'Сумма',
      accessorKey: 'total_amount',
      cell: (item: any) => formatCurrency(item.total_amount),
    },
    {
      header: 'Статус',
      accessorKey: 'status',
      cell: (item: any) => (
        <ContractStatusBadge status={item.status} />
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Договоры</h1>
        <Button onClick={handleCreateContract}>
          <Plus className="mr-2 h-4 w-4" />
          Создать договор
        </Button>
      </div>

      <div className="flex flex-col space-y-4">
        <div className="flex items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Поиск договоров..."
              className="pl-8"
              value={filters.search || ''}
              onChange={handleSearchChange}
            />
          </div>
          <Button 
            variant="outline" 
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="mr-2 h-4 w-4" />
            Фильтры
          </Button>
        </div>

        {showFilters && (
          <Card>
            <CardContent className="pt-6">
              <ContractsFilters 
                filters={filters} 
                onFiltersChange={updateFilters} 
              />
            </CardContent>
          </Card>
        )}
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="bg-destructive/10 text-destructive p-4 rounded-md">
          Ошибка при загрузке договоров: {error.message}
        </div>
      ) : contracts.length === 0 ? (
        <div className="bg-muted p-8 rounded-md text-center">
          <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium">Договоры не найдены</h3>
          <p className="text-muted-foreground mt-2">
            {filters.search || Object.keys(filters).length > 1
              ? 'Попробуйте изменить параметры поиска или фильтры'
              : 'Создайте первый договор, нажав на кнопку "Создать договор"'}
          </p>
        </div>
      ) : (
        <>
          <DataTable
            title="Список договоров"
            subtitle={`Всего: ${count}`}
            columns={columns}
            data={contracts}
            onRowClick={handleRowClick}
          />

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                Показывать по:
              </span>
              <Select
                value={limit.toString()}
                onValueChange={handleLimitChange}
              >
                <SelectTrigger className="w-[80px]">
                  <SelectValue placeholder="10" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="icon"
                onClick={prevPage}
                disabled={page <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="text-sm">
                Страница {page} из {totalPages || 1}
              </span>
              <Button
                variant="outline"
                size="icon"
                onClick={nextPage}
                disabled={page >= totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

/**
 * Компонент для отображения статуса договора
 */
function ContractStatusBadge({ status }: { status: string }) {
  let variant: 'default' | 'secondary' | 'destructive' | 'outline';
  let label: string;

  switch (status) {
    case ContractStatus.DRAFT:
      variant = 'outline';
      label = 'Черновик';
      break;
    case ContractStatus.PENDING:
      variant = 'secondary';
      label = 'Ожидает подписания';
      break;
    case ContractStatus.ACTIVE:
      variant = 'default';
      label = 'Активен';
      break;
    case ContractStatus.COMPLETED:
      variant = 'default';
      label = 'Завершен';
      break;
    case ContractStatus.CANCELLED:
      variant = 'destructive';
      label = 'Отменен';
      break;
    default:
      variant = 'outline';
      label = status;
  }

  return <Badge variant={variant}>{label}</Badge>;
}
