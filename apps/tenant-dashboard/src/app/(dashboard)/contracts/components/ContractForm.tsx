/**
 * @file: ContractForm.tsx
 * @description: Компонент формы для создания и редактирования договора
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client, react-hook-form, zod
 * @created: 2024-05-10
 */

"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent } from '@/components/ui/card';
import { useContract, ContractStatus } from '@pactcrm/supabase-client';
import { cn } from '@/lib/utils';

// Схема валидации формы договора
const contractFormSchema = z.object({
  number: z.string().min(1, 'Номер договора обязателен'),
  client_id: z.string().min(1, 'Клиент обязателен'),
  apartment_id: z.string().min(1, 'Объект недвижимости обязателен'),
  signed_date: z.date({
    required_error: 'Дата подписания обязательна',
  }),
  start_date: z.date({
    required_error: 'Дата начала обязательна',
  }),
  end_date: z.date({
    required_error: 'Дата окончания обязательна',
  }),
  total_amount: z.coerce.number().min(1, 'Сумма договора должна быть больше 0'),
  initial_payment: z.coerce.number().min(0, 'Первоначальный взнос не может быть отрицательным'),
  monthly_payment: z.coerce.number().min(0, 'Ежемесячный платеж не может быть отрицательным'),
  status: z.string().min(1, 'Статус обязателен'),
});

type ContractFormValues = z.infer<typeof contractFormSchema>;

interface ContractFormProps {
  id?: string;
}

/**
 * Компонент формы для создания и редактирования договора
 */
export function ContractForm({ id }: ContractFormProps) {
  const router = useRouter();
  const { contract, loading, error, isSaving, saveContract } = useContract(id);

  // Инициализация формы
  const form = useForm<ContractFormValues>({
    resolver: zodResolver(contractFormSchema),
    defaultValues: {
      number: '',
      client_id: '',
      apartment_id: '',
      signed_date: new Date(),
      start_date: new Date(),
      end_date: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      total_amount: 0,
      initial_payment: 0,
      monthly_payment: 0,
      status: ContractStatus.DRAFT,
    },
  });

  // Заполнение формы данными договора при загрузке
  useEffect(() => {
    if (contract) {
      form.reset({
        number: contract.number,
        client_id: contract.client_id,
        apartment_id: contract.apartment_id,
        signed_date: new Date(contract.signed_date),
        start_date: new Date(contract.start_date),
        end_date: new Date(contract.end_date),
        total_amount: contract.total_amount,
        initial_payment: contract.initial_payment,
        monthly_payment: contract.monthly_payment,
        status: contract.status,
      });
    }
  }, [contract, form]);

  // Обработчик отправки формы
  const onSubmit = async (data: ContractFormValues) => {
    // Преобразуем даты в строки
    const contractData = {
      ...data,
      signed_date: format(data.signed_date, 'yyyy-MM-dd'),
      start_date: format(data.start_date, 'yyyy-MM-dd'),
      end_date: format(data.end_date, 'yyyy-MM-dd'),
    };

    const savedContract = await saveContract(contractData);
    
    if (savedContract) {
      router.push(`/contracts/${savedContract.id}`);
    }
  };

  // Обработчик отмены
  const handleCancel = () => {
    if (id) {
      router.push(`/contracts/${id}`);
    } else {
      router.push('/contracts');
    }
  };

  // Если идет загрузка данных, показываем индикатор загрузки
  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Если произошла ошибка, показываем сообщение об ошибке
  if (error) {
    return (
      <div className="bg-destructive/10 text-destructive p-4 rounded-md">
        Ошибка при загрузке договора: {error.message}
      </div>
    );
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Номер договора */}
              <FormField
                control={form.control}
                name="number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Номер договора</FormLabel>
                    <FormControl>
                      <Input placeholder="ДКП-2024-001" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Статус */}
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Статус</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Выберите статус" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value={ContractStatus.DRAFT}>Черновик</SelectItem>
                        <SelectItem value={ContractStatus.PENDING}>Ожидает подписания</SelectItem>
                        <SelectItem value={ContractStatus.ACTIVE}>Активен</SelectItem>
                        <SelectItem value={ContractStatus.COMPLETED}>Завершен</SelectItem>
                        <SelectItem value={ContractStatus.CANCELLED}>Отменен</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Клиент */}
              <FormField
                control={form.control}
                name="client_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Клиент</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Выберите клиента" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {/* TODO: Загрузить список клиентов */}
                        <SelectItem value="client-1">Иванов Иван Иванович</SelectItem>
                        <SelectItem value="client-2">Петров Петр Петрович</SelectItem>
                        <SelectItem value="client-3">Сидорова Анна Сергеевна</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Объект недвижимости */}
              <FormField
                control={form.control}
                name="apartment_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Объект недвижимости</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Выберите объект" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {/* TODO: Загрузить список объектов */}
                        <SelectItem value="apartment-1">ЖК Солнечный, корп. 1, кв. 42</SelectItem>
                        <SelectItem value="apartment-2">ЖК Морской, корп. 2, кв. 15</SelectItem>
                        <SelectItem value="apartment-3">ЖК Центральный, корп. 3, кв. 78</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Дата подписания */}
              <FormField
                control={form.control}
                name="signed_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Дата подписания</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "dd.MM.yyyy")
                            ) : (
                              <span>Выберите дату</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Дата начала */}
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Дата начала</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "dd.MM.yyyy")
                            ) : (
                              <span>Выберите дату</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Дата окончания */}
              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Дата окончания</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "dd.MM.yyyy")
                            ) : (
                              <span>Выберите дату</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                          disabled={(date) => 
                            form.getValues('start_date') ? date < form.getValues('start_date') : false
                          }
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Сумма договора */}
              <FormField
                control={form.control}
                name="total_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Сумма договора</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="0" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Первоначальный взнос */}
              <FormField
                control={form.control}
                name="initial_payment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Первоначальный взнос</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="0" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Ежемесячный платеж */}
              <FormField
                control={form.control}
                name="monthly_payment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ежемесячный платеж</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="0" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSaving}
              >
                Отмена
              </Button>
              <Button type="submit" disabled={isSaving}>
                {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {id ? 'Сохранить' : 'Создать'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
