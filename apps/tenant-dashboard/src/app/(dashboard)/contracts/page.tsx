/**
 * @file: contracts/page.tsx
 * @description: Страница управления договорами
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2023-12-01
 * @updated: 2024-05-10
 */

import React from 'react';
import { ContractsList } from './components';
import { PermissionGuard } from '@pactcrm/supabase-client';

export default function ContractsPage() {
  return (
    <PermissionGuard
      resource="contracts"
      action="read"
      fallback={
        <div className="p-8 text-center">
          <h2 className="text-xl font-semibold mb-2">Доступ запрещен</h2>
          <p className="text-muted-foreground">
            У вас нет разрешения на просмотр договоров.
          </p>
        </div>
      }
    >
      <ContractsList />
    </PermissionGuard>
  );
}
