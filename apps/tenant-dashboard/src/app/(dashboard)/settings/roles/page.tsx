"use client";

/**
 * @file: roles/page.tsx
 * @description: Страница управления ролями и разрешениями
 * @dependencies: RoleManager, UserRoleManager
 * @created: 2024-05-01
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle, 
  AlertDialogTrigger 
} from '@/components/ui/alert-dialog';
import { toast } from '@/components/ui/use-toast';
import { RoleManager, UserRoleManager } from '@pactcrm/supabase-client';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Edit, Trash2, Plus, UserPlus, Shield, Users } from 'lucide-react';

// Схема валидации для формы создания/редактирования роли
const roleFormSchema = z.object({
  name: z.string().min(3, {
    message: 'Название роли должно содержать не менее 3 символов',
  }),
  description: z.string().optional(),
});

// Схема валидации для формы назначения роли пользователю
const assignRoleFormSchema = z.object({
  userId: z.string({
    required_error: 'Выберите пользователя',
  }),
  roleId: z.string({
    required_error: 'Выберите роль',
  }),
});

export default function RolesPage() {
  // Используем хуки для управления ролями и пользователями
  const roleManager = RoleManager({});
  const userRoleManager = UserRoleManager({});
  
  // Состояния для диалогов
  const [isCreateRoleDialogOpen, setIsCreateRoleDialogOpen] = useState(false);
  const [isEditRoleDialogOpen, setIsEditRoleDialogOpen] = useState(false);
  const [isDeleteRoleDialogOpen, setIsDeleteRoleDialogOpen] = useState(false);
  const [isAssignRoleDialogOpen, setIsAssignRoleDialogOpen] = useState(false);
  
  // Состояние для выбранной роли
  const [selectedRole, setSelectedRole] = useState<any>(null);
  
  // Состояние для выбранных разрешений
  const [selectedPermissions, setSelectedPermissions] = useState<Record<string, boolean>>({});
  
  // Формы для создания/редактирования роли
  const createRoleForm = useForm<z.infer<typeof roleFormSchema>>({
    resolver: zodResolver(roleFormSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });
  
  const editRoleForm = useForm<z.infer<typeof roleFormSchema>>({
    resolver: zodResolver(roleFormSchema),
    defaultValues: {
      name: '',
      description: '',
    },
  });
  
  // Форма для назначения роли пользователю
  const assignRoleForm = useForm<z.infer<typeof assignRoleFormSchema>>({
    resolver: zodResolver(assignRoleFormSchema),
  });
  
  // Обработчик создания роли
  const handleCreateRole = async (values: z.infer<typeof roleFormSchema>) => {
    try {
      await roleManager.createRole(values.name, values.description || '');
      setIsCreateRoleDialogOpen(false);
      createRoleForm.reset();
      toast({
        title: 'Роль создана',
        description: `Роль "${values.name}" успешно создана`,
      });
    } catch (error) {
      toast({
        title: 'Ошибка',
        description: 'Не удалось создать роль',
        variant: 'destructive',
      });
    }
  };
  
  // Обработчик редактирования роли
  const handleEditRole = async (values: z.infer<typeof roleFormSchema>) => {
    if (!selectedRole) return;
    
    try {
      await roleManager.updateRole(selectedRole.id, values.name, values.description || '');
      setIsEditRoleDialogOpen(false);
      editRoleForm.reset();
      toast({
        title: 'Роль обновлена',
        description: `Роль "${values.name}" успешно обновлена`,
      });
    } catch (error) {
      toast({
        title: 'Ошибка',
        description: 'Не удалось обновить роль',
        variant: 'destructive',
      });
    }
  };
  
  // Обработчик удаления роли
  const handleDeleteRole = async () => {
    if (!selectedRole) return;
    
    try {
      await roleManager.deleteRole(selectedRole.id);
      setIsDeleteRoleDialogOpen(false);
      toast({
        title: 'Роль удалена',
        description: `Роль "${selectedRole.name}" успешно удалена`,
      });
      setSelectedRole(null);
    } catch (error) {
      toast({
        title: 'Ошибка',
        description: 'Не удалось удалить роль',
        variant: 'destructive',
      });
    }
  };
  
  // Обработчик назначения роли пользователю
  const handleAssignRole = async (values: z.infer<typeof assignRoleFormSchema>) => {
    try {
      await userRoleManager.assignRoleToUser(values.userId, values.roleId);
      setIsAssignRoleDialogOpen(false);
      assignRoleForm.reset();
      toast({
        title: 'Роль назначена',
        description: 'Роль успешно назначена пользователю',
      });
    } catch (error) {
      toast({
        title: 'Ошибка',
        description: 'Не удалось назначить роль пользователю',
        variant: 'destructive',
      });
    }
  };
  
  // Обработчик отзыва роли у пользователя
  const handleRevokeRole = async (userId: string, roleId: string) => {
    try {
      await userRoleManager.revokeRoleFromUser(userId, roleId);
      toast({
        title: 'Роль отозвана',
        description: 'Роль успешно отозвана у пользователя',
      });
    } catch (error) {
      toast({
        title: 'Ошибка',
        description: 'Не удалось отозвать роль у пользователя',
        variant: 'destructive',
      });
    }
  };
  
  // Обработчик изменения разрешений роли
  const handlePermissionChange = async (permissionId: string, checked: boolean) => {
    if (!selectedRole) return;
    
    try {
      if (checked) {
        await roleManager.assignPermissionToRole(selectedRole.id, permissionId);
      } else {
        await roleManager.revokePermissionFromRole(selectedRole.id, permissionId);
      }
      
      // Обновляем состояние выбранных разрешений
      setSelectedPermissions(prev => ({
        ...prev,
        [permissionId]: checked,
      }));
      
      toast({
        title: checked ? 'Разрешение добавлено' : 'Разрешение удалено',
        description: checked 
          ? 'Разрешение успешно добавлено к роли' 
          : 'Разрешение успешно удалено из роли',
      });
    } catch (error) {
      toast({
        title: 'Ошибка',
        description: checked 
          ? 'Не удалось добавить разрешение к роли' 
          : 'Не удалось удалить разрешение из роли',
        variant: 'destructive',
      });
    }
  };
  
  // Обработчик выбора роли
  const handleSelectRole = (role: any) => {
    setSelectedRole(role);
    
    // Заполняем форму редактирования
    editRoleForm.setValue('name', role.name);
    editRoleForm.setValue('description', role.description || '');
    
    // Заполняем выбранные разрешения
    const permissions: Record<string, boolean> = {};
    const rolePermissions = roleManager.rolePermissions[role.id] || [];
    
    roleManager.permissions.forEach(permission => {
      permissions[permission.id] = rolePermissions.includes(permission.id);
    });
    
    setSelectedPermissions(permissions);
  };
  
  // Группируем разрешения по ресурсам
  const permissionsByResource = roleManager.permissions.reduce((acc: Record<string, any[]>, permission) => {
    if (!acc[permission.resource]) {
      acc[permission.resource] = [];
    }
    acc[permission.resource].push(permission);
    return acc;
  }, {});
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Управление ролями и разрешениями</h1>
        <div className="flex gap-2">
          <Button onClick={() => setIsCreateRoleDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Создать роль
          </Button>
          <Button onClick={() => setIsAssignRoleDialogOpen(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Назначить роль
          </Button>
        </div>
      </div>
      
      <Tabs defaultValue="roles">
        <TabsList>
          <TabsTrigger value="roles">
            <Shield className="mr-2 h-4 w-4" />
            Роли
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="mr-2 h-4 w-4" />
            Пользователи
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="roles" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Список ролей */}
            <Card className="md:col-span-1">
              <CardHeader>
                <CardTitle>Роли</CardTitle>
                <CardDescription>Список доступных ролей</CardDescription>
              </CardHeader>
              <CardContent>
                {roleManager.loading ? (
                  <div className="flex justify-center p-4">Загрузка...</div>
                ) : (
                  <div className="space-y-2">
                    {roleManager.roles.map(role => (
                      <div
                        key={role.id}
                        className={`p-3 rounded-md cursor-pointer hover:bg-accent ${
                          selectedRole?.id === role.id ? 'bg-accent' : ''
                        }`}
                        onClick={() => handleSelectRole(role)}
                      >
                        <div className="font-medium">{role.name}</div>
                        {role.description && (
                          <div className="text-sm text-muted-foreground">{role.description}</div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
            
            {/* Детали роли и разрешения */}
            <Card className="md:col-span-2">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>
                      {selectedRole ? selectedRole.name : 'Выберите роль'}
                    </CardTitle>
                    <CardDescription>
                      {selectedRole 
                        ? selectedRole.description || 'Нет описания' 
                        : 'Выберите роль из списка слева для просмотра и редактирования разрешений'}
                    </CardDescription>
                  </div>
                  
                  {selectedRole && (
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsEditRoleDialogOpen(true)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setIsDeleteRoleDialogOpen(true)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {selectedRole ? (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Разрешения</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Выберите разрешения, которые будут доступны для этой роли
                      </p>
                      
                      {Object.entries(permissionsByResource).map(([resource, permissions]) => (
                        <div key={resource} className="mb-6">
                          <h4 className="text-md font-medium mb-2 capitalize">{resource}</h4>
                          <Separator className="mb-2" />
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            {permissions.map(permission => (
                              <div key={permission.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={permission.id}
                                  checked={selectedPermissions[permission.id] || false}
                                  onCheckedChange={(checked) => 
                                    handlePermissionChange(permission.id, checked as boolean)
                                  }
                                />
                                <label
                                  htmlFor={permission.id}
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                  <span className="capitalize">{permission.action}</span>
                                  {permission.description && (
                                    <p className="text-xs text-muted-foreground">
                                      {permission.description}
                                    </p>
                                  )}
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="flex justify-center items-center h-40 text-muted-foreground">
                    Выберите роль для просмотра и редактирования разрешений
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Пользователи и их роли</CardTitle>
              <CardDescription>Управление ролями пользователей</CardDescription>
            </CardHeader>
            <CardContent>
              {userRoleManager.loading ? (
                <div className="flex justify-center p-4">Загрузка...</div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Пользователь</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Основная роль</TableHead>
                      <TableHead>Дополнительные роли</TableHead>
                      <TableHead>Действия</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {userRoleManager.users.map(user => (
                      <TableRow key={user.id}>
                        <TableCell>{user.full_name || 'Нет имени'}</TableCell>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{user.role || 'Нет роли'}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {(userRoleManager.userRoles[user.id] || []).map(roleId => {
                              const role = roleManager.roles.find(r => r.id === roleId);
                              return role ? (
                                <Badge key={roleId} className="mr-1 mb-1">
                                  {role.name}
                                  <button
                                    className="ml-1 text-xs"
                                    onClick={() => handleRevokeRole(user.id, roleId)}
                                  >
                                    ×
                                  </button>
                                </Badge>
                              ) : null;
                            })}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              assignRoleForm.setValue('userId', user.id);
                              setIsAssignRoleDialogOpen(true);
                            }}
                          >
                            <UserPlus className="h-4 w-4 mr-1" />
                            Добавить роль
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Диалог создания роли */}
      <Dialog open={isCreateRoleDialogOpen} onOpenChange={setIsCreateRoleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Создать новую роль</DialogTitle>
            <DialogDescription>
              Заполните форму для создания новой роли
            </DialogDescription>
          </DialogHeader>
          
          <Form {...createRoleForm}>
            <form onSubmit={createRoleForm.handleSubmit(handleCreateRole)} className="space-y-4">
              <FormField
                control={createRoleForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Название</FormLabel>
                    <FormControl>
                      <Input placeholder="Введите название роли" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={createRoleForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Описание</FormLabel>
                    <FormControl>
                      <Input placeholder="Введите описание роли" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="submit">Создать</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Диалог редактирования роли */}
      <Dialog open={isEditRoleDialogOpen} onOpenChange={setIsEditRoleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Редактировать роль</DialogTitle>
            <DialogDescription>
              Измените информацию о роли
            </DialogDescription>
          </DialogHeader>
          
          <Form {...editRoleForm}>
            <form onSubmit={editRoleForm.handleSubmit(handleEditRole)} className="space-y-4">
              <FormField
                control={editRoleForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Название</FormLabel>
                    <FormControl>
                      <Input placeholder="Введите название роли" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={editRoleForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Описание</FormLabel>
                    <FormControl>
                      <Input placeholder="Введите описание роли" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="submit">Сохранить</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Диалог удаления роли */}
      <AlertDialog open={isDeleteRoleDialogOpen} onOpenChange={setIsDeleteRoleDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Вы уверены?</AlertDialogTitle>
            <AlertDialogDescription>
              Это действие нельзя отменить. Роль будет удалена, а все связанные с ней разрешения будут отозваны у пользователей.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Отмена</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRole}>Удалить</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Диалог назначения роли пользователю */}
      <Dialog open={isAssignRoleDialogOpen} onOpenChange={setIsAssignRoleDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Назначить роль пользователю</DialogTitle>
            <DialogDescription>
              Выберите пользователя и роль для назначения
            </DialogDescription>
          </DialogHeader>
          
          <Form {...assignRoleForm}>
            <form onSubmit={assignRoleForm.handleSubmit(handleAssignRole)} className="space-y-4">
              <FormField
                control={assignRoleForm.control}
                name="userId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Пользователь</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Выберите пользователя" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {userRoleManager.users.map(user => (
                          <SelectItem key={user.id} value={user.id}>
                            {user.full_name || user.email}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={assignRoleForm.control}
                name="roleId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Роль</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Выберите роль" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {roleManager.roles.map(role => (
                          <SelectItem key={role.id} value={role.id}>
                            {role.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="submit">Назначить</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
