/**
 * @file: clients/page.tsx
 * @description: Страница управления клиентами
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2023-12-01
 */

"use client";

import React, { useState } from 'react';
import { ClientsList } from '@/components/clients/ClientsList';
import { ClientForm } from '@/components/clients/ClientForm';
import { ClientDetails } from '@/components/clients/ClientDetails';
import { Client } from '@pactcrm/supabase-client';

// Типы для режимов отображения
type ViewMode = 'list' | 'create' | 'edit' | 'view';

export default function ClientsPage() {
  // Состояние для управления режимами отображения
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  // Обработчики событий
  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    setViewMode('view');
  };

  const handleClientCreate = () => {
    setSelectedClient(null);
    setViewMode('create');
  };

  const handleClientEdit = (client: Client) => {
    setSelectedClient(client);
    setViewMode('edit');
  };

  const handleClientView = (client: Client) => {
    setSelectedClient(client);
    setViewMode('view');
  };

  const handleClientDelete = async (client: Client) => {
    // TODO: Реализовать удаление с подтверждением
    console.log('Удаление клиента:', client);
  };

  const handleFormSubmit = async (data: any) => {
    // TODO: Обработка успешного сохранения
    console.log('Сохранение клиента:', data);
    setViewMode('list');
  };

  const handleBack = () => {
    setSelectedClient(null);
    setViewMode('list');
  };

  // Рендеринг в зависимости от режима
  const renderContent = () => {
    switch (viewMode) {
      case 'list':
        return (
          <ClientsList
            onClientSelect={handleClientSelect}
            onClientCreate={handleClientCreate}
            onClientEdit={handleClientEdit}
            onClientView={handleClientView}
            onClientDelete={handleClientDelete}
          />
        );

      case 'create':
        return (
          <ClientForm
            onSubmit={handleFormSubmit}
            onCancel={handleBack}
            title="Создание клиента"
            submitText="Создать клиента"
          />
        );

      case 'edit':
        return (
          <ClientForm
            clientId={selectedClient?.id}
            onSubmit={handleFormSubmit}
            onCancel={handleBack}
            title="Редактирование клиента"
            submitText="Сохранить изменения"
          />
        );

      case 'view':
        return (
          <ClientDetails
            clientId={selectedClient?.id || ''}
            onEdit={handleClientEdit}
            onBack={handleBack}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto py-6">
      {renderContent()}
    </div>
  );
}
