/**
 * @file: PaymentsList.tsx
 * @description: Компонент для отображения списка платежей
 * @dependencies: react, next, @pactcrm/ui
 * @created: 2024-05-10
 */

"use client";

import React from 'react';
import { Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { DataTable } from '@/components/ui-wrappers';
import { Badge } from '@/components/ui/badge';
import { formatCurrency, formatDate } from '@/lib/utils';

interface PaymentsListProps {
  contractId?: string;
}

/**
 * Компонент для отображения списка платежей
 */
export function PaymentsList({ contractId }: PaymentsListProps) {
  // Пример данных для таблицы платежей
  const payments = [
    {
      id: '1',
      amount: 1250000,
      due_date: '2023-06-15',
      payment_date: '2023-06-14',
      status: 'paid',
      payment_method: 'bank_transfer',
    },
    {
      id: '2',
      amount: 50000,
      due_date: '2023-07-15',
      payment_date: null,
      status: 'pending',
      payment_method: null,
    },
    {
      id: '3',
      amount: 50000,
      due_date: '2023-08-15',
      payment_date: null,
      status: 'pending',
      payment_method: null,
    },
    {
      id: '4',
      amount: 50000,
      due_date: '2023-09-15',
      payment_date: null,
      status: 'pending',
      payment_method: null,
    },
  ];

  // Колонки для таблицы платежей
  const columns = [
    {
      header: 'Сумма',
      accessorKey: 'amount',
      cell: (item: any) => formatCurrency(item.amount),
    },
    {
      header: 'Дата платежа',
      accessorKey: 'due_date',
      cell: (item: any) => formatDate(item.due_date),
    },
    {
      header: 'Дата оплаты',
      accessorKey: 'payment_date',
      cell: (item: any) => item.payment_date ? formatDate(item.payment_date) : '—',
    },
    {
      header: 'Способ оплаты',
      accessorKey: 'payment_method',
      cell: (item: any) => {
        if (!item.payment_method) return '—';
        
        const methods: Record<string, string> = {
          cash: 'Наличные',
          card: 'Банковская карта',
          bank_transfer: 'Банковский перевод',
        };
        
        return methods[item.payment_method] || item.payment_method;
      },
    },
    {
      header: 'Статус',
      accessorKey: 'status',
      cell: (item: any) => {
        let variant: 'default' | 'secondary' | 'destructive' | 'outline';
        let label: string;

        switch (item.status) {
          case 'paid':
            variant = 'default';
            label = 'Оплачен';
            break;
          case 'pending':
            variant = 'secondary';
            label = 'Ожидает оплаты';
            break;
          case 'overdue':
            variant = 'destructive';
            label = 'Просрочен';
            break;
          default:
            variant = 'outline';
            label = item.status;
        }

        return <Badge variant={variant}>{label}</Badge>;
      },
    },
  ];

  // Обработчик клика по строке таблицы
  const handleRowClick = (payment: any) => {
    console.log('Нажата строка платежа:', payment);
  };

  // Обработчик создания нового платежа
  const handleCreatePayment = () => {
    console.log('Создание нового платежа для договора:', contractId);
  };

  return (
    <Card>
      <CardContent className="p-0">
        <div className="p-4 flex justify-between items-center border-b">
          <h3 className="text-lg font-medium">Платежи</h3>
          <Button onClick={handleCreatePayment}>
            <Plus className="mr-2 h-4 w-4" />
            Добавить платеж
          </Button>
        </div>
        
        <DataTable
          columns={columns}
          data={payments}
          onRowClick={handleRowClick}
        />
      </CardContent>
    </Card>
  );
}
