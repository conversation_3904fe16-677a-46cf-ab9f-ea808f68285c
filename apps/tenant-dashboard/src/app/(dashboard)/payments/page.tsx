/**
 * @file: payments/page.tsx
 * @description: Страница управления платежами
 * @dependencies: react, next, @pactcrm/ui
 * @created: 2023-12-01
 */

"use client";

import React from 'react';
import { CreditCard, Plus, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DataTable } from '@/components/ui-wrappers';

export default function PaymentsPage() {
  // Пример данных для таблицы платежей
  const payments = [
    {
      id: '1',
      contract: 'ДКП-2023-001',
      client: 'А<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Смирнов',
      property: 'ЖК Солнечный, кв. 42',
      date: '2023-05-15',
      amount: '₽ 120,000',
      status: 'completed',
    },
    {
      id: '2',
      contract: 'ДКП-2023-002',
      client: 'Елена Иванова',
      property: 'ЖК Морской, кв. 15',
      date: '2023-05-20',
      amount: '₽ 85,000',
      status: 'pending',
    },
    {
      id: '3',
      contract: 'ДКП-2023-003',
      client: 'Дмитрий Петров',
      property: 'ЖК Центральный, кв. 78',
      date: '2023-06-01',
      amount: '₽ 150,000',
      status: 'completed',
    },
    {
      id: '4',
      contract: 'ДКП-2023-001',
      client: 'Алексей Смирнов',
      property: 'ЖК Солнечный, кв. 42',
      date: '2023-06-15',
      amount: '₽ 120,000',
      status: 'pending',
    },
    {
      id: '5',
      contract: 'ДКП-2023-005',
      client: 'Мария Кузнецова',
      property: 'ЖК Парковый, кв. 23',
      date: '2023-06-10',
      amount: '₽ 95,000',
      status: 'failed',
    },
  ];

  // Колонки для таблицы платежей
  const columns = [
    {
      header: 'Договор',
      accessorKey: 'contract' as keyof typeof payments[0],
    },
    {
      header: 'Клиент',
      accessorKey: 'client' as keyof typeof payments[0],
    },
    {
      header: 'Объект',
      accessorKey: 'property' as keyof typeof payments[0],
    },
    {
      header: 'Дата',
      accessorKey: 'date' as keyof typeof payments[0],
    },
    {
      header: 'Сумма',
      accessorKey: 'amount' as keyof typeof payments[0],
    },
    {
      header: 'Статус',
      accessorKey: 'status' as keyof typeof payments[0],
      cell: (item: typeof payments[0]) => (
        <span
          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
            item.status === 'completed'
              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
              : item.status === 'pending'
              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
              : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
          }`}
        >
          {item.status === 'completed'
            ? 'Выполнен'
            : item.status === 'pending'
            ? 'В обработке'
            : 'Ошибка'}
        </span>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Платежи</h1>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Добавить платеж
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Поиск платежей..."
            className="pl-8"
          />
        </div>
        <Button variant="outline">Фильтры</Button>
      </div>

      <DataTable
        columns={columns}
        data={payments}
        onRowClick={(item) => console.log('Нажата строка:', item)}
      />
    </div>
  );
}
