"use client";

import React from 'react';
import { StatCard, ChartCard, DataTable, TaskList } from '@/components/ui-wrappers';
import { Building, Users, FileText, CreditCard } from 'lucide-react';

export default function DashboardPage() {
  // Пример данных для статистики
  const stats = [
    {
      title: 'Всего объектов',
      value: '245',
      icon: <Building className="h-4 w-4" />,
      description: 'За последний месяц',
      trend: { value: 12, isPositive: true },
    },
    {
      title: 'Активные клиенты',
      value: '128',
      icon: <Users className="h-4 w-4" />,
      description: 'За последний месяц',
      trend: { value: 8, isPositive: true },
    },
    {
      title: 'Договоры',
      value: '86',
      icon: <FileText className="h-4 w-4" />,
      description: 'За последний месяц',
      trend: { value: 5, isPositive: true },
    },
    {
      title: 'Платежи',
      value: '₽ 12.4M',
      icon: <CreditCard className="h-4 w-4" />,
      description: 'За последний месяц',
      trend: { value: 3, isPositive: false },
    },
  ];

  // Пример данных для таблицы последних платежей
  const recentPayments = [
    {
      id: '1',
      client: 'Алексей Смирнов',
      amount: '₽ 120,000',
      date: '2023-05-15',
      status: 'completed',
      property: 'ЖК Солнечный, кв. 42',
    },
    {
      id: '2',
      client: 'Елена Иванова',
      amount: '₽ 85,000',
      date: '2023-05-14',
      status: 'pending',
      property: 'ЖК Морской, кв. 15',
    },
    {
      id: '3',
      client: 'Дмитрий Петров',
      amount: '₽ 150,000',
      date: '2023-05-12',
      status: 'completed',
      property: 'ЖК Центральный, кв. 78',
    },
    {
      id: '4',
      client: 'Ольга Козлова',
      amount: '₽ 95,000',
      date: '2023-05-10',
      status: 'failed',
      property: 'ЖК Парковый, кв. 23',
    },
    {
      id: '5',
      client: 'Сергей Николаев',
      amount: '₽ 110,000',
      date: '2023-05-08',
      status: 'completed',
      property: 'ЖК Солнечный, кв. 56',
    },
  ];

  // Пример данных для таблицы задач
  const tasks = [
    {
      id: '1',
      title: 'Подготовить отчет по продажам за май',
      description: 'Включить данные по всем жилым комплексам',
      dueDate: '2023-05-20',
      priority: 'high' as const,
      status: 'pending' as const,
      assignee: {
        name: 'Иван Петров',
      },
    },
    {
      id: '2',
      title: 'Связаться с клиентом по вопросу просрочки платежа',
      description: 'Клиент: Алексей Смирнов, ЖК Солнечный, кв. 42',
      dueDate: '2023-05-18',
      priority: 'high' as const,
      status: 'in-progress' as const,
      assignee: {
        name: 'Мария Сидорова',
      },
    },
    {
      id: '3',
      title: 'Обновить информацию о доступных квартирах',
      description: 'Добавить новые квартиры в ЖК Морской',
      dueDate: '2023-05-25',
      priority: 'medium' as const,
      status: 'pending' as const,
      assignee: {
        name: 'Иван Петров',
      },
    },
    {
      id: '4',
      title: 'Подготовить договор для нового клиента',
      description: 'Клиент: Елена Иванова, ЖК Центральный, кв. 34',
      dueDate: '2023-05-19',
      priority: 'medium' as const,
      status: 'pending' as const,
      assignee: {
        name: 'Анна Кузнецова',
      },
    },
    {
      id: '5',
      title: 'Провести инвентаризацию объектов',
      description: 'Проверить статусы всех квартир в ЖК Парковый',
      dueDate: '2023-05-30',
      priority: 'low' as const,
      status: 'pending' as const,
      assignee: {
        name: 'Дмитрий Соколов',
      },
    },
  ];

  // Колонки для таблицы платежей
  const paymentColumns = [
    {
      header: 'Клиент',
      accessorKey: 'client' as keyof typeof recentPayments[0],
    },
    {
      header: 'Сумма',
      accessorKey: 'amount' as keyof typeof recentPayments[0],
    },
    {
      header: 'Дата',
      accessorKey: 'date' as keyof typeof recentPayments[0],
    },
    {
      header: 'Статус',
      accessorKey: 'status' as keyof typeof recentPayments[0],
      cell: (item: typeof recentPayments[0]) => (
        <span
          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
            item.status === 'completed'
              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
              : item.status === 'pending'
              ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
              : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
          }`}
        >
          {item.status === 'completed'
            ? 'Выполнен'
            : item.status === 'pending'
            ? 'В обработке'
            : 'Ошибка'}
        </span>
      ),
    },
    {
      header: 'Объект',
      accessorKey: 'property' as keyof typeof recentPayments[0],
    },
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Дашборд</h1>

      {/* Статистика */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <StatCard
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            description={stat.description}
            trend={stat.trend}
          />
        ))}
      </div>

      {/* График и задачи */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Здесь будет график, пока просто заглушка */}
        <ChartCard
          title="Платежи за последние 30 дней"
          subtitle="Общая сумма: ₽ 12,450,000"
          chart={
            <div className="flex h-80 items-center justify-center bg-muted/20 p-6">
              <p className="text-muted-foreground">График платежей будет здесь</p>
            </div>
          }
        />

        {/* Список задач */}
        <TaskList
          title="Задачи"
          subtitle="Предстоящие задачи и напоминания"
          tasks={tasks}
          onTaskClick={(task) => console.log('Нажата задача:', task)}
          onStatusChange={(task, status) =>
            console.log(`Статус задачи ${task.id} изменен на ${status}`)
          }
        />
      </div>

      {/* Таблица последних платежей */}
      <DataTable
        title="Последние платежи"
        subtitle="Платежи за последние 30 дней"
        columns={paymentColumns}
        data={recentPayments}
        onRowClick={(item) => console.log('Нажата строка:', item)}
      />
    </div>
  );
}
