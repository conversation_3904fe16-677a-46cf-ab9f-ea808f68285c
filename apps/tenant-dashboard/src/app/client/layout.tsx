/**
 * @file: client/layout.tsx
 * @description: Макет для клиентского приложения
 * @dependencies: react, next, @pactcrm/ui
 * @created: 2023-12-01
 */

"use client";

import React from 'react';
import { MainLayout } from '@/components/ui-wrappers';
import { Home, FileText, CreditCard, MessageSquare, Settings, Bell, HelpCircle } from 'lucide-react';
import Image from 'next/image';

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const sidebarItems = [
    {
      title: 'Дашборд',
      href: '/client/dashboard',
      icon: <Home className="h-4 w-4" />,
      isActive: true,
    },
    {
      title: 'Договор',
      href: '/client/contract',
      icon: <FileText className="h-4 w-4" />,
    },
    {
      title: 'Платежи',
      href: '/client/payments',
      icon: <CreditCard className="h-4 w-4" />,
    },
    {
      title: 'Сообщения',
      href: '/client/messages',
      icon: <MessageSquare className="h-4 w-4" />,
    },
    {
      title: 'Настройки',
      href: '/client/settings',
      icon: <Settings className="h-4 w-4" />,
    },
  ];

  const userNavigation = {
    name: 'Алексей Смирнов',
    email: '<EMAIL>',
    initials: 'АС',
    items: [
      {
        label: 'Профиль',
        href: '/client/profile',
      },
      {
        label: 'Настройки',
        href: '/client/settings',
      },
      {
        label: 'Выйти',
        onClick: () => console.log('Выход из системы'),
      },
    ],
  };

  const headerActions = (
    <div className="flex items-center gap-2">
      <button className="rounded-full p-2 hover:bg-accent">
        <Bell className="h-5 w-5" />
      </button>
      <button className="rounded-full p-2 hover:bg-accent">
        <HelpCircle className="h-5 w-5" />
      </button>
    </div>
  );

  const logo = (
    <div className="flex items-center gap-2">
      <div className="flex h-8 w-8 items-center justify-center rounded-md bg-primary">
        <span className="text-lg font-bold text-primary-foreground">P</span>
      </div>
      <span className="font-semibold">PactCRM</span>
    </div>
  );

  return (
    <MainLayout
      sidebarItems={sidebarItems}
      logo={logo}
      userNavigation={userNavigation}
      headerActions={headerActions}
      onSearch={(value) => console.log('Поиск:', value)}
    >
      {children}
    </MainLayout>
  );
}
