/**
 * @file: client/dashboard/page.tsx
 * @description: Страница дашборда клиента
 * @dependencies: react, next, @pactcrm/ui
 * @created: 2023-12-01
 */

"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { FileText, Home, CreditCard, MessageSquare, Download } from 'lucide-react';

export default function ClientDashboardPage() {
  // Пример данных для договора клиента
  const contract = {
    number: 'ДКП-2023-001',
    date: '15.05.2023',
    property: 'ЖК Солнечный, кв. 42',
    totalAmount: '₽ 12,500,000',
    paidAmount: '₽ 3,750,000',
    paymentProgress: 30,
    nextPayment: {
      date: '15.07.2023',
      amount: '₽ 125,000',
    },
    documents: [
      { id: '1', name: 'Договор купли-продажи.pdf', date: '15.05.2023' },
      { id: '2', name: 'График платежей.pdf', date: '15.05.2023' },
      { id: '3', name: 'Акт приема-передачи.pdf', date: '20.05.2023' },
    ],
    payments: [
      { id: '1', date: '15.05.2023', amount: '₽ 1,250,000', status: 'completed' },
      { id: '2', date: '15.06.2023', amount: '₽ 125,000', status: 'completed' },
      { id: '3', date: '15.07.2023', amount: '₽ 125,000', status: 'pending' },
    ],
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="mb-6 text-2xl font-bold">Личный кабинет</h1>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Информация о договоре */}
        <Card>
          <CardHeader>
            <CardTitle>Информация о договоре</CardTitle>
            <CardDescription>Договор №{contract.number} от {contract.date}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Объект недвижимости</p>
              <p>{contract.property}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Общая сумма договора</p>
              <p>{contract.totalAmount}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Оплачено</p>
              <p>{contract.paidAmount} ({contract.paymentProgress}%)</p>
              <Progress value={contract.paymentProgress} className="mt-2" />
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Следующий платеж</p>
              <p>{contract.nextPayment.date} - {contract.nextPayment.amount}</p>
            </div>
            <Button className="w-full">
              <CreditCard className="mr-2 h-4 w-4" />
              Внести платеж
            </Button>
          </CardContent>
        </Card>

        {/* Информация о квартире */}
        <Card>
          <CardHeader>
            <CardTitle>Информация о квартире</CardTitle>
            <CardDescription>{contract.property}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="aspect-video overflow-hidden rounded-md bg-muted">
              <div className="flex h-full items-center justify-center">
                <Home className="h-10 w-10 text-muted-foreground" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Площадь</p>
                <p>72 м²</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Этаж</p>
                <p>5 из 12</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Комнаты</p>
                <p>2</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Статус</p>
                <p>Строительство</p>
              </div>
            </div>
            <Button variant="outline" className="w-full">
              <Home className="mr-2 h-4 w-4" />
              Подробнее о квартире
            </Button>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="payments" className="mt-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="payments">Платежи</TabsTrigger>
          <TabsTrigger value="documents">Документы</TabsTrigger>
          <TabsTrigger value="messages">Сообщения</TabsTrigger>
        </TabsList>
        <TabsContent value="payments" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>История платежей</CardTitle>
              <CardDescription>История ваших платежей по договору</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {contract.payments.map((payment) => (
                  <div key={payment.id} className="flex items-center justify-between rounded-lg border p-4">
                    <div>
                      <p className="font-medium">{payment.date}</p>
                      <p className="text-sm text-muted-foreground">{payment.amount}</p>
                    </div>
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                        payment.status === 'completed'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                      }`}
                    >
                      {payment.status === 'completed' ? 'Выполнен' : 'Ожидается'}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="documents" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Документы</CardTitle>
              <CardDescription>Документы по вашему договору</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {contract.documents.map((document) => (
                  <div key={document.id} className="flex items-center justify-between rounded-lg border p-4">
                    <div className="flex items-center">
                      <FileText className="mr-2 h-4 w-4" />
                      <div>
                        <p className="font-medium">{document.name}</p>
                        <p className="text-sm text-muted-foreground">{document.date}</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="icon">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="messages" className="mt-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Сообщения</CardTitle>
              <CardDescription>Общение с вашим менеджером</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-lg border p-4">
                <p className="text-center text-muted-foreground">
                  Здесь будет отображаться история сообщений с вашим менеджером
                </p>
                <Button className="mt-4 w-full">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Написать сообщение
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
