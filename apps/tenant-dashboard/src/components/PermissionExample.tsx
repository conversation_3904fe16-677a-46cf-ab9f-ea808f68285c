/**
 * Пример использования компонента PermissionGuard
 * 
 * @module components/PermissionExample
 */

import React from 'react';
import { PermissionGuard, RoleGuard } from '@pactcrm/supabase-client';
import { Button } from '@pactcrm/ui';

/**
 * Компонент с примером использования PermissionGuard и RoleGuard
 * 
 * @returns Компонент с примером использования PermissionGuard и RoleGuard
 * 
 * @example
 * ```tsx
 * <PermissionExample />
 * ```
 */
export default function PermissionExample() {
  return (
    <div className="space-y-6 p-4 bg-white rounded-lg shadow">
      <h2 className="text-xl font-semibold">Примеры проверки прав доступа</h2>
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Проверка по ролям</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium mb-2">Доступно для tenant_admin</h4>
            <RoleGuard 
              roles="tenant_admin"
              fallback={<p className="text-red-500">У вас нет роли tenant_admin</p>}
            >
              <p className="text-green-500">У вас есть роль tenant_admin</p>
              <Button variant="primary" className="mt-2">
                Управление настройками
              </Button>
            </RoleGuard>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium mb-2">Доступно для after_sales_manager</h4>
            <RoleGuard 
              roles="after_sales_manager"
              fallback={<p className="text-red-500">У вас нет роли after_sales_manager</p>}
            >
              <p className="text-green-500">У вас есть роль after_sales_manager</p>
              <Button variant="primary" className="mt-2">
                Управление клиентами
              </Button>
            </RoleGuard>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium mb-2">Доступно для superadmin или support</h4>
            <RoleGuard 
              roles={['superadmin', 'support']}
              fallback={<p className="text-red-500">У вас нет роли superadmin или support</p>}
            >
              <p className="text-green-500">У вас есть роль superadmin или support</p>
              <Button variant="primary" className="mt-2">
                Глобальные настройки
              </Button>
            </RoleGuard>
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Проверка по разрешениям</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium mb-2">Создание клиентов</h4>
            <PermissionGuard 
              resource="clients" 
              action="create"
              fallback={<p className="text-red-500">У вас нет прав на создание клиентов</p>}
            >
              <p className="text-green-500">У вас есть права на создание клиентов</p>
              <Button variant="primary" className="mt-2">
                Создать клиента
              </Button>
            </PermissionGuard>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium mb-2">Просмотр договоров</h4>
            <PermissionGuard 
              resource="contracts" 
              action="read"
              fallback={<p className="text-red-500">У вас нет прав на просмотр договоров</p>}
            >
              <p className="text-green-500">У вас есть права на просмотр договоров</p>
              <Button variant="primary" className="mt-2">
                Просмотреть договоры
              </Button>
            </PermissionGuard>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium mb-2">Удаление платежей</h4>
            <PermissionGuard 
              resource="payments" 
              action="delete"
              fallback={<p className="text-red-500">У вас нет прав на удаление платежей</p>}
            >
              <p className="text-green-500">У вас есть права на удаление платежей</p>
              <Button variant="primary" className="mt-2">
                Удалить платеж
              </Button>
            </PermissionGuard>
          </div>
        </div>
      </div>
    </div>
  );
}
