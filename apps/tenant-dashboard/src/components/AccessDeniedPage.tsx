/**
 * Компонент страницы с ошибкой доступа
 * 
 * @module components/AccessDeniedPage
 */

import React from 'react';
import { useRouter } from 'next/router';
import { Button } from '@pactcrm/ui';

/**
 * Компонент страницы с ошибкой доступа
 * 
 * Отображается, когда у пользователя нет прав для доступа к странице
 * 
 * @returns Компонент страницы с ошибкой доступа
 * 
 * @example
 * ```tsx
 * <AccessDeniedPage />
 * ```
 */
export default function AccessDeniedPage() {
  const router = useRouter();

  const handleGoBack = () => {
    router.back();
  };

  const handleGoHome = () => {
    router.push('/');
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 px-4">
      <div className="text-center max-w-md">
        <div className="mb-6">
          <div className="text-red-500 text-6xl mb-4">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-24 w-24 mx-auto" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 15v2m0 0v2m0-2h2m-2 0H9m3-10v4m6 0a9 9 0 11-18 0 9 9 0 0118 0z" 
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Доступ запрещен</h1>
          <p className="text-gray-600 mb-6">
            У вас нет прав для доступа к этой странице. Пожалуйста, свяжитесь с администратором, если вы считаете, что это ошибка.
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button 
            variant="outline" 
            onClick={handleGoBack}
          >
            Вернуться назад
          </Button>
          <Button 
            variant="primary" 
            onClick={handleGoHome}
          >
            На главную
          </Button>
        </div>
      </div>
    </div>
  );
}
