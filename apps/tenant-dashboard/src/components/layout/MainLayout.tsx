/**
 * Основной макет для страниц приложения
 * 
 * @module components/layout/MainLayout
 */

import React, { ReactNode } from 'react';
import { useAuth } from '@pactcrm/supabase-client';
import { Button } from '@pactcrm/ui';
import Link from 'next/link';

/**
 * Свойства компонента MainLayout
 */
interface MainLayoutProps {
  /** Дочерние элементы */
  children: ReactNode;
  /** Заголовок страницы */
  title?: string;
}

/**
 * Основной макет для страниц приложения
 * 
 * Включает верхнюю панель навигации и боковое меню
 * 
 * @param props Свойства компонента
 * @returns Компонент макета
 * 
 * @example
 * ```tsx
 * <MainLayout title="Дашборд">
 *   <DashboardContent />
 * </MainLayout>
 * ```
 */
export function MainLayout({ children, title = 'PactCRM' }: MainLayoutProps) {
  const { user, signOut } = useAuth();

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <div className="flex min-h-screen flex-col">
      {/* Верхняя панель */}
      <header className="bg-white border-b border-gray-200">
        <div className="mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 items-center justify-between">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-xl font-bold text-blue-600">PactCRM</span>
              </div>
              <div className="hidden md:block">
                <div className="ml-10 flex items-baseline space-x-4">
                  <Link href="/" className="px-3 py-2 rounded-md text-sm font-medium text-gray-900 hover:bg-gray-100">
                    Дашборд
                  </Link>
                  <Link href="/clients" className="px-3 py-2 rounded-md text-sm font-medium text-gray-500 hover:text-gray-900 hover:bg-gray-100">
                    Клиенты
                  </Link>
                  <Link href="/contracts" className="px-3 py-2 rounded-md text-sm font-medium text-gray-500 hover:text-gray-900 hover:bg-gray-100">
                    Договоры
                  </Link>
                  <Link href="/payments" className="px-3 py-2 rounded-md text-sm font-medium text-gray-500 hover:text-gray-900 hover:bg-gray-100">
                    Платежи
                  </Link>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-4 flex items-center md:ml-6">
                {user && (
                  <div className="flex items-center">
                    <span className="mr-4 text-sm text-gray-500">
                      {user.email}
                    </span>
                    <Button variant="outline" size="sm" onClick={handleSignOut}>
                      Выйти
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Основное содержимое */}
      <main className="flex-1 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
          {title && (
            <h1 className="text-2xl font-semibold text-gray-900 mb-6">{title}</h1>
          )}
          {children}
        </div>
      </main>

      {/* Подвал */}
      <footer className="bg-white border-t border-gray-200">
        <div className="mx-auto max-w-7xl px-4 py-4 sm:px-6 lg:px-8">
          <p className="text-center text-sm text-gray-500">
            &copy; {new Date().getFullYear()} PactCRM. Все права защищены.
          </p>
        </div>
      </footer>
    </div>
  );
}

export default MainLayout;
