/**
 * @file: ComplexDetails.tsx
 * @description: Компонент детального просмотра жилого комплекса
 * @dependencies: react, @pactcrm/supabase-client, @pactcrm/ui
 * @created: 2024-12-26
 */

"use client";

import React from 'react';
import { 
  Building, 
  MapPin, 
  Home, 
  TrendingUp, 
  Calendar,
  Edit,
  ArrowLeft,
  BarChart3,
  Users,
  DollarSign,
  Square
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { useComplex, useComplexStats } from '@pactcrm/supabase-client';
import { Complex } from '@pactcrm/supabase-client';

interface ComplexDetailsProps {
  complexId: string;
  onEdit?: (complex: Complex) => void;
  onBack?: () => void;
  showActions?: boolean;
}

/**
 * Компонент детального просмотра жилого комплекса
 */
export function ComplexDetails({
  complexId,
  onEdit,
  onBack,
  showActions = true,
}: ComplexDetailsProps) {
  // Получение данных о комплексе
  const {
    complex,
    loading: complexLoading,
    error: complexError,
  } = useComplex(complexId);

  // Получение статистики
  const {
    stats,
    isLoading: statsLoading,
    error: statsError,
  } = useComplexStats(complexId);

  // Функция для получения цвета статуса
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'construction':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'planning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'completed':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'suspended':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // Функция для получения текста статуса
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Активный';
      case 'construction':
        return 'Строительство';
      case 'planning':
        return 'Планирование';
      case 'completed':
        return 'Завершен';
      case 'suspended':
        return 'Приостановлен';
      default:
        return status;
    }
  };

  // Функция для форматирования цены
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Функция для форматирования площади
  const formatArea = (area: number) => {
    return `${area.toLocaleString('ru-RU')} м²`;
  };

  // Функция для форматирования даты
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (complexError) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Ошибка при загрузке жилого комплекса</p>
            <p className="text-sm text-muted-foreground mt-1">
              {complexError.message}
            </p>
            {onBack && (
              <Button variant="outline" onClick={onBack} className="mt-4">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Назад
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (complexLoading || !complex) {
    return (
      <div className="space-y-6">
        {/* Заголовок */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-[300px]" />
            <Skeleton className="h-4 w-[200px]" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-10 w-[100px]" />
            <Skeleton className="h-10 w-[120px]" />
          </div>
        </div>

        {/* Карточки статистики */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-[100px] mb-2" />
                <Skeleton className="h-8 w-[60px]" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Основная информация */}
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-[200px]" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-[80%]" />
            <Skeleton className="h-4 w-[60%]" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Заголовок и действия */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            {onBack && (
              <Button variant="ghost" size="sm" onClick={onBack}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            <Building className="h-6 w-6" />
            <h1 className="text-2xl font-bold">{complex.name}</h1>
            <Badge className={getStatusColor(complex.status)}>
              {getStatusText(complex.status)}
            </Badge>
          </div>
          <div className="flex items-center space-x-1 text-muted-foreground">
            <MapPin className="h-4 w-4" />
            <span>{complex.address}</span>
          </div>
        </div>

        {showActions && (
          <div className="flex space-x-2">
            {onEdit && (
              <Button onClick={() => onEdit(complex)}>
                <Edit className="mr-2 h-4 w-4" />
                Редактировать
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Карточки статистики */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Home className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Здания</span>
            </div>
            <div className="text-2xl font-bold">
              {statsLoading ? (
                <Skeleton className="h-8 w-[60px]" />
              ) : (
                stats?.total_buildings || complex.buildings_count || 0
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Square className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Квартиры</span>
            </div>
            <div className="text-2xl font-bold">
              {statsLoading ? (
                <Skeleton className="h-8 w-[60px]" />
              ) : (
                stats?.total_apartments || complex.apartments_count || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats?.available_apartments || complex.available_apartments || 0} свободно
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Средняя цена</span>
            </div>
            <div className="text-2xl font-bold">
              {statsLoading ? (
                <Skeleton className="h-8 w-[100px]" />
              ) : (
                stats?.average_price || complex.average_price 
                  ? formatPrice(stats?.average_price || complex.average_price || 0)
                  : '—'
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Общая площадь</span>
            </div>
            <div className="text-2xl font-bold">
              {statsLoading ? (
                <Skeleton className="h-8 w-[80px]" />
              ) : (
                stats?.total_area || complex.total_area 
                  ? formatArea(stats?.total_area || complex.total_area || 0)
                  : '—'
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Основная информация */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Основная информация</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Название</Label>
              <p className="text-sm">{complex.name}</p>
            </div>

            <div>
              <Label className="text-sm font-medium text-muted-foreground">Адрес</Label>
              <p className="text-sm">{complex.address}</p>
            </div>

            <div>
              <Label className="text-sm font-medium text-muted-foreground">Статус</Label>
              <div className="mt-1">
                <Badge className={getStatusColor(complex.status)}>
                  {getStatusText(complex.status)}
                </Badge>
              </div>
            </div>

            {complex.description && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Описание</Label>
                <p className="text-sm whitespace-pre-wrap">{complex.description}</p>
              </div>
            )}

            <Separator />

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Создан</Label>
                <p className="flex items-center space-x-1">
                  <Calendar className="h-3 w-3" />
                  <span>{formatDate(complex.created_at)}</span>
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Обновлен</Label>
                <p className="flex items-center space-x-1">
                  <Calendar className="h-3 w-3" />
                  <span>{formatDate(complex.updated_at)}</span>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Статистика продаж */}
        {stats && (
          <Card>
            <CardHeader>
              <CardTitle>Статистика продаж</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Доступно</Label>
                  <p className="text-2xl font-bold text-green-600">
                    {stats.available_apartments}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Продано</Label>
                  <p className="text-2xl font-bold text-blue-600">
                    {stats.sold_apartments}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Забронировано</Label>
                  <p className="text-2xl font-bold text-yellow-600">
                    {stats.reserved_apartments}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Прогресс</Label>
                  <p className="text-2xl font-bold">
                    {stats.completion_percentage}%
                  </p>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Минимальная цена</span>
                  <span className="font-medium">
                    {stats.min_price ? formatPrice(stats.min_price) : '—'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Максимальная цена</span>
                  <span className="font-medium">
                    {stats.max_price ? formatPrice(stats.max_price) : '—'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Средняя цена</span>
                  <span className="font-medium">
                    {stats.average_price ? formatPrice(stats.average_price) : '—'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Здания комплекса */}
      {complex.buildings && complex.buildings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Здания комплекса</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {complex.buildings.map((building) => (
                <Card key={building.id} className="border-muted">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Home className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{building.name}</span>
                    </div>
                    <div className="space-y-1 text-sm text-muted-foreground">
                      <p>Этажей: {building.floors}</p>
                      <p>Квартир: {building.apartments_count || 0}</p>
                      <p>Свободно: {building.available_apartments || 0}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
