/**
 * @file: ComplexesList.tsx
 * @description: Компонент для отображения списка жилых комплексов
 * @dependencies: react, @pactcrm/supabase-client, @pactcrm/ui
 * @created: 2024-12-26
 */

"use client";

import React, { useState, useMemo, useCallback } from 'react';
import { 
  Building, 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2,
  MapPin,
  Home,
  TrendingUp,
  MoreHorizontal
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useComplexes } from '@pactcrm/supabase-client';
import { Complex, ComplexStatus, ComplexFilters, PropertyPagination, ComplexSorting } from '@pactcrm/supabase-client';

interface ComplexesListProps {
  onComplexSelect?: (complex: Complex) => void;
  onComplexCreate?: () => void;
  onComplexEdit?: (complex: Complex) => void;
  onComplexDelete?: (complex: Complex) => void;
  onComplexView?: (complex: Complex) => void;
}

/**
 * Компонент для отображения списка жилых комплексов
 */
export function ComplexesList({
  onComplexSelect,
  onComplexCreate,
  onComplexEdit,
  onComplexDelete,
  onComplexView,
}: ComplexesListProps) {
  // Состояние фильтров и пагинации
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState<ComplexStatus | 'all'>('all');
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [sortField, setSortField] = useState<ComplexSorting['field']>('created_at');
  const [sortOrder, setSortOrder] = useState<ComplexSorting['order']>('desc');

  // Подготовка фильтров для API
  const filters = useMemo<ComplexFilters>(() => {
    const result: ComplexFilters = {};
    
    if (search.trim()) {
      result.search = search.trim();
    }
    
    if (statusFilter !== 'all') {
      result.status = statusFilter as ComplexStatus;
    }
    
    return result;
  }, [search, statusFilter]);

  const pagination = useMemo<PropertyPagination>(() => ({
    page,
    limit,
  }), [page, limit]);

  const sorting = useMemo<ComplexSorting>(() => ({
    field: sortField,
    order: sortOrder,
  }), [sortField, sortOrder]);

  // Получение данных
  const {
    complexes,
    count,
    total_pages,
    isLoading,
    error,
    refresh,
  } = useComplexes(filters, pagination, sorting);

  // Обработчики событий
  const handleSearch = useCallback((value: string) => {
    setSearch(value);
    setPage(1); // Сброс на первую страницу при поиске
  }, []);

  const handleStatusFilter = useCallback((value: string) => {
    setStatusFilter(value as ComplexStatus | 'all');
    setPage(1); // Сброс на первую страницу при фильтрации
  }, []);

  const handleSort = useCallback((field: ComplexSorting['field']) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
    setPage(1); // Сброс на первую страницу при сортировке
  }, [sortField, sortOrder]);

  // Функция для получения цвета статуса
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'construction':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'planning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'completed':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'suspended':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // Функция для получения текста статуса
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Активный';
      case 'construction':
        return 'Строительство';
      case 'planning':
        return 'Планирование';
      case 'completed':
        return 'Завершен';
      case 'suspended':
        return 'Приостановлен';
      default:
        return status;
    }
  };

  // Функция для форматирования цены
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Функция для форматирования площади
  const formatArea = (area: number) => {
    return `${area.toLocaleString('ru-RU')} м²`;
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Ошибка при загрузке жилых комплексов</p>
            <Button variant="outline" onClick={refresh} className="mt-2">
              Попробовать снова
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Заголовок и кнопка создания */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Building className="h-6 w-6" />
          <h1 className="text-2xl font-bold">Жилые комплексы</h1>
        </div>
        {onComplexCreate && (
          <Button onClick={onComplexCreate}>
            <Plus className="mr-2 h-4 w-4" />
            Добавить комплекс
          </Button>
        )}
      </div>

      {/* Фильтры и поиск */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4">
            {/* Поиск */}
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Поиск по названию, адресу или описанию..."
                className="pl-8"
                value={search}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>

            {/* Фильтр по статусу */}
            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-full md:w-[200px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Статус" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Все статусы</SelectItem>
                <SelectItem value="planning">Планирование</SelectItem>
                <SelectItem value="construction">Строительство</SelectItem>
                <SelectItem value="active">Активный</SelectItem>
                <SelectItem value="completed">Завершен</SelectItem>
                <SelectItem value="suspended">Приостановлен</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Таблица */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Название</span>
                    {sortField === 'name' && (
                      <span className="text-xs">
                        {sortOrder === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead>Адрес</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Статус</span>
                    {sortField === 'status' && (
                      <span className="text-xs">
                        {sortOrder === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead className="text-center">Здания</TableHead>
                <TableHead className="text-center">Квартиры</TableHead>
                <TableHead className="text-right">Средняя цена</TableHead>
                <TableHead className="text-right">Общая площадь</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                // Skeleton loading
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell><Skeleton className="h-4 w-[200px]" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-[250px]" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-[80px]" /></TableCell>
                    <TableCell className="text-center"><Skeleton className="h-4 w-[40px] mx-auto" /></TableCell>
                    <TableCell className="text-center"><Skeleton className="h-4 w-[40px] mx-auto" /></TableCell>
                    <TableCell className="text-right"><Skeleton className="h-4 w-[100px] ml-auto" /></TableCell>
                    <TableCell className="text-right"><Skeleton className="h-4 w-[80px] ml-auto" /></TableCell>
                    <TableCell><Skeleton className="h-8 w-8" /></TableCell>
                  </TableRow>
                ))
              ) : complexes.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="flex flex-col items-center space-y-2">
                      <Building className="h-8 w-8 text-muted-foreground" />
                      <p className="text-muted-foreground">
                        {search || statusFilter !== 'all' 
                          ? 'Жилые комплексы не найдены' 
                          : 'Пока нет жилых комплексов'
                        }
                      </p>
                      {onComplexCreate && !search && statusFilter === 'all' && (
                        <Button variant="outline" onClick={onComplexCreate}>
                          <Plus className="mr-2 h-4 w-4" />
                          Добавить первый комплекс
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                complexes.map((complex) => (
                  <TableRow 
                    key={complex.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => onComplexSelect?.(complex)}
                  >
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <span>{complex.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1 text-muted-foreground">
                        <MapPin className="h-3 w-3" />
                        <span className="truncate max-w-[200px]">{complex.address}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(complex.status)}>
                        {getStatusText(complex.status)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <Home className="h-3 w-3 text-muted-foreground" />
                        <span>{complex.buildings_count || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <span>{complex.apartments_count || 0}</span>
                        <span className="text-xs text-muted-foreground">
                          ({complex.available_apartments || 0} свободно)
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {complex.average_price ? formatPrice(complex.average_price) : '—'}
                    </TableCell>
                    <TableCell className="text-right">
                      {complex.total_area ? formatArea(complex.total_area) : '—'}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Открыть меню</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Действия</DropdownMenuLabel>
                          {onComplexView && (
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              onComplexView(complex);
                            }}>
                              <Eye className="mr-2 h-4 w-4" />
                              Просмотр
                            </DropdownMenuItem>
                          )}
                          {onComplexEdit && (
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              onComplexEdit(complex);
                            }}>
                              <Edit className="mr-2 h-4 w-4" />
                              Редактировать
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          {onComplexDelete && (
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={(e) => {
                                e.stopPropagation();
                                onComplexDelete(complex);
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Удалить
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Пагинация */}
      {total_pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Показано {complexes.length} из {count} комплексов
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
            >
              Назад
            </Button>
            <span className="text-sm">
              Страница {page} из {total_pages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page >= total_pages}
            >
              Далее
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
