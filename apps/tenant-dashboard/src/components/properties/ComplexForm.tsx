/**
 * @file: ComplexForm.tsx
 * @description: Форма для создания и редактирования жилого комплекса
 * @dependencies: react, react-hook-form, zod, @pactcrm/supabase-client, @pactcrm/ui
 * @created: 2024-12-26
 */

"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Building, Save, X, MapPin, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useComplex } from '@pactcrm/supabase-client';
import { Complex, ComplexStatus } from '@pactcrm/supabase-client';

// Схема валидации для формы жилого комплекса
const complexFormSchema = z.object({
  name: z
    .string()
    .min(1, 'Название обязательно')
    .min(2, 'Название должно содержать минимум 2 символа')
    .max(100, 'Название не должно превышать 100 символов'),
  address: z
    .string()
    .min(1, 'Адрес обязателен')
    .min(5, 'Адрес должен содержать минимум 5 символов')
    .max(200, 'Адрес не должен превышать 200 символов'),
  description: z
    .string()
    .max(1000, 'Описание не должно превышать 1000 символов')
    .optional(),
  status: z.enum(['planning', 'construction', 'active', 'completed', 'suspended'], {
    required_error: 'Статус обязателен',
  }),
});

type ComplexFormData = z.infer<typeof complexFormSchema>;

interface ComplexFormProps {
  complexId?: string;
  initialData?: Partial<Complex>;
  onSubmit?: (data: ComplexFormData) => void | Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  submitText?: string;
  title?: string;
}

/**
 * Компонент формы для создания и редактирования жилого комплекса
 */
export function ComplexForm({
  complexId,
  initialData,
  onSubmit,
  onCancel,
  isLoading: externalLoading = false,
  submitText,
  title,
}: ComplexFormProps) {
  // Хук для работы с жилым комплексом
  const {
    complex,
    loading: complexLoading,
    error: complexError,
    saveComplex,
    isSaving,
    saveError,
  } = useComplex(complexId);

  // Определяем режим работы формы
  const isEditMode = Boolean(complexId);
  const isCreateMode = !isEditMode;

  // Данные для формы
  const formData = initialData || complex;

  // Настройка формы
  const form = useForm<ComplexFormData>({
    resolver: zodResolver(complexFormSchema),
    defaultValues: {
      name: formData?.name || '',
      address: formData?.address || '',
      description: formData?.description || '',
      status: (formData?.status as ComplexStatus) || 'planning',
    },
  });

  // Обновляем значения формы при изменении данных
  React.useEffect(() => {
    if (formData) {
      form.reset({
        name: formData.name || '',
        address: formData.address || '',
        description: formData.description || '',
        status: (formData.status as ComplexStatus) || 'planning',
      });
    }
  }, [formData, form]);

  // Обработчик отправки формы
  const handleSubmit = async (data: ComplexFormData) => {
    try {
      if (onSubmit) {
        await onSubmit(data);
      } else if (saveComplex) {
        await saveComplex(data);
      }
    } catch (error) {
      console.error('Ошибка при сохранении жилого комплекса:', error);
    }
  };

  // Функция для получения текста статуса
  const getStatusText = (status: string) => {
    switch (status) {
      case 'planning':
        return 'Планирование';
      case 'construction':
        return 'Строительство';
      case 'active':
        return 'Активный';
      case 'completed':
        return 'Завершен';
      case 'suspended':
        return 'Приостановлен';
      default:
        return status;
    }
  };

  const isFormLoading = externalLoading || complexLoading || isSaving;
  const formError = complexError || saveError;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Building className="h-5 w-5" />
          <span>
            {title || (isEditMode ? 'Редактирование жилого комплекса' : 'Создание жилого комплекса')}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Основная информация */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Building className="h-4 w-4 text-muted-foreground" />
                <h3 className="text-lg font-medium">Основная информация</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Название */}
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Название комплекса *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="ЖК Солнечный"
                          {...field}
                          disabled={isFormLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        Название жилого комплекса для отображения клиентам
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Статус */}
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Статус *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={isFormLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Выберите статус" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="planning">
                            {getStatusText('planning')}
                          </SelectItem>
                          <SelectItem value="construction">
                            {getStatusText('construction')}
                          </SelectItem>
                          <SelectItem value="active">
                            {getStatusText('active')}
                          </SelectItem>
                          <SelectItem value="completed">
                            {getStatusText('completed')}
                          </SelectItem>
                          <SelectItem value="suspended">
                            {getStatusText('suspended')}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Текущий статус строительства комплекса
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Адрес */}
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span>Адрес *</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="г. Москва, ул. Солнечная, д. 10"
                        {...field}
                        disabled={isFormLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Полный адрес расположения жилого комплекса
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Описание */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center space-x-1">
                      <FileText className="h-3 w-3" />
                      <span>Описание</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Описание жилого комплекса, его особенности и преимущества..."
                        className="min-h-[100px]"
                        {...field}
                        disabled={isFormLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Дополнительная информация о комплексе (до 1000 символов)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Отображение ошибок */}
            {formError && (
              <div className="p-4 border border-red-200 bg-red-50 rounded-md">
                <p className="text-sm text-red-600">
                  {formError.message || 'Произошла ошибка при сохранении'}
                </p>
              </div>
            )}

            <Separator />

            {/* Кнопки действий */}
            <div className="flex items-center justify-end space-x-2">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isFormLoading}
                >
                  <X className="mr-2 h-4 w-4" />
                  Отмена
                </Button>
              )}
              <Button
                type="submit"
                disabled={isFormLoading}
              >
                {isFormLoading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Сохранение...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {submitText || (isEditMode ? 'Сохранить изменения' : 'Создать комплекс')}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
