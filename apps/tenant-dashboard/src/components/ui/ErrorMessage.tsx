/**
 * Компонент для отображения сообщений об ошибках
 * 
 * @module components/ui/ErrorMessage
 */

import React from 'react';

/**
 * Свойства компонента ErrorMessage
 */
export interface ErrorMessageProps {
  /** Текст сообщения об ошибке */
  message: string | null;
  /** Дополнительные CSS классы */
  className?: string;
  /** Обработчик закрытия сообщения */
  onClose?: () => void;
}

/**
 * Компонент для отображения сообщений об ошибках
 * 
 * @param props Свойства компонента
 * @returns Компонент сообщения об ошибке или null, если сообщение не указано
 * 
 * @example
 * ```tsx
 * // Базовое сообщение об ошибке
 * <ErrorMessage message="Произошла ошибка при загрузке данных" />
 * 
 * // Сообщение с обработчиком закрытия
 * <ErrorMessage 
 *   message="Неверный логин или пароль" 
 *   onClose={() => setError(null)} 
 * />
 * 
 * // Условное отображение ошибки
 * {error && <ErrorMessage message={error} />}
 * ```
 */
export function ErrorMessage({ 
  message, 
  className = '',
  onClose
}: ErrorMessageProps) {
  // Если сообщение не указано, не отображаем компонент
  if (!message) {
    return null;
  }

  return (
    <div className={`rounded-md bg-red-50 p-3 text-sm text-red-600 ${className}`} role="alert">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          {message}
        </div>
        {onClose && (
          <button
            type="button"
            className="ml-2 inline-flex rounded p-1 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-red-500"
            onClick={onClose}
            aria-label="Закрыть"
          >
            <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
}

export default ErrorMessage;
