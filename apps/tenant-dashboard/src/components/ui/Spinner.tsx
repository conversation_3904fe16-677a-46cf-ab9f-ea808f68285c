/**
 * Компонент индикатора загрузки
 * 
 * @module components/ui/Spinner
 */

import React from 'react';

/**
 * Свойства компонента Spinner
 */
export interface SpinnerProps {
  /** Размер спиннера (по умолчанию: md) */
  size?: 'sm' | 'md' | 'lg';
  /** Цвет спиннера (по умолчанию: primary) */
  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info';
  /** Дополнительные CSS классы */
  className?: string;
}

/**
 * Компонент индикатора загрузки (спиннер)
 * 
 * @param props Свойства компонента
 * @returns Компонент спиннера
 * 
 * @example
 * ```tsx
 * // Базовый спиннер
 * <Spinner />
 * 
 * // Маленький спиннер с цветом опасности
 * <Spinner size="sm" variant="danger" />
 * 
 * // Большой спиннер с дополнительными классами
 * <Spinner size="lg" className="mx-auto my-4" />
 * ```
 */
export function Spinner({ 
  size = 'md', 
  variant = 'primary',
  className = ''
}: SpinnerProps) {
  // Определяем размеры в зависимости от параметра size
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  // Определяем цвета в зависимости от параметра variant
  const variantClasses = {
    primary: 'border-blue-600',
    secondary: 'border-gray-600',
    success: 'border-green-600',
    danger: 'border-red-600',
    warning: 'border-yellow-600',
    info: 'border-cyan-600'
  };

  return (
    <div 
      className={`animate-spin rounded-full border-2 ${variantClasses[variant]} border-t-transparent ${sizeClasses[size]} ${className}`}
      role="status"
      aria-label="Загрузка"
    />
  );
}

/**
 * Компонент контейнера с индикатором загрузки на весь экран
 * 
 * @param props Свойства компонента Spinner
 * @returns Компонент контейнера с индикатором загрузки
 * 
 * @example
 * ```tsx
 * // Индикатор загрузки на весь экран
 * <FullScreenSpinner />
 * 
 * // Индикатор загрузки на весь экран с кастомными параметрами
 * <FullScreenSpinner size="lg" variant="success" />
 * ```
 */
export function FullScreenSpinner(props: SpinnerProps) {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <Spinner {...props} />
    </div>
  );
}

export default Spinner;
