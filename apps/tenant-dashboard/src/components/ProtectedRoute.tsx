/**
 * Компонент для защиты маршрутов, требующих аутентификации
 *
 * @module components/ProtectedRoute
 */

import { RoleBasedRoute } from '@pactcrm/supabase-client';
import { FullScreenSpinner } from './ui/Spinner';
import AccessDeniedPage from './AccessDeniedPage';

/**
 * Свойства компонента ProtectedRoute
 */
interface ProtectedRouteProps {
  /** Дочерние элементы, которые будут отображены только для аутентифицированных пользователей */
  children: React.ReactNode;
  /** Роли, которым разрешен доступ (если не указаны, доступ разрешен всем аутентифицированным пользователям) */
  allowedRoles?: string[];
  /** Требуемые разрешения для доступа */
  requiredPermissions?: Array<{ resource: string; action: string }>;
  /** URL для перенаправления неаутентифицированных пользователей (по умолчанию: /login) */
  redirectUrl?: string;
  /** Показывать страницу с ошибкой доступа вместо перенаправления */
  showAccessDenied?: boolean;
}

/**
 * Компонент для защиты маршрутов, требующих аутентификации
 *
 * Перенаправляет неаутентифицированных пользователей на страницу входа
 * и может ограничивать доступ по ролям и разрешениям
 *
 * @param props Свойства компонента
 * @returns Защищенный компонент
 *
 * @example
 * ```tsx
 * // Базовая защита маршрута
 * <ProtectedRoute>
 *   <DashboardPage />
 * </ProtectedRoute>
 *
 * // Защита с ограничением по ролям
 * <ProtectedRoute allowedRoles={['tenant_admin', 'after_sales_manager']}>
 *   <AdminPanel />
 * </ProtectedRoute>
 *
 * // Защита с ограничением по разрешениям
 * <ProtectedRoute
 *   requiredPermissions={[
 *     { resource: 'clients', action: 'read' }
 *   ]}
 * >
 *   <ClientsPage />
 * </ProtectedRoute>
 * ```
 */
export default function ProtectedRoute({
  children,
  allowedRoles,
  requiredPermissions,
  redirectUrl = '/login',
  showAccessDenied = false
}: ProtectedRouteProps) {
  return (
    <RoleBasedRoute
      allowedRoles={allowedRoles}
      requiredPermissions={requiredPermissions}
      redirectUrl={redirectUrl}
      fallback={showAccessDenied ? <AccessDeniedPage /> : null}
      loadingComponent={<FullScreenSpinner />}
    >
      {children}
    </RoleBasedRoute>
  );
}
