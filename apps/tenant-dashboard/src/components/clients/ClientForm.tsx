/**
 * @file: ClientForm.tsx
 * @description: Форма для создания и редактирования клиента
 * @dependencies: react, react-hook-form, zod, @pactcrm/supabase-client, @pactcrm/ui
 * @created: 2024-12-26
 */

"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Users, Save, X, Mail, Phone, MapPin, FileText, Calendar, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useClient } from '@pactcrm/supabase-client';
import { Client } from '@pactcrm/supabase-client';

// Схема валидации для формы клиента
const clientFormSchema = z.object({
  first_name: z
    .string()
    .min(1, 'Имя обязательно')
    .min(2, 'Имя должно содержать минимум 2 символа')
    .max(50, 'Имя не должно превышать 50 символов'),
  last_name: z
    .string()
    .min(1, 'Фамилия обязательна')
    .min(2, 'Фамилия должна содержать минимум 2 символа')
    .max(50, 'Фамилия не должна превышать 50 символов'),
  middle_name: z
    .string()
    .max(50, 'Отчество не должно превышать 50 символов')
    .optional(),
  email: z
    .string()
    .email('Некорректный email адрес')
    .optional()
    .or(z.literal('')),
  phone: z
    .string()
    .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Некорректный номер телефона')
    .optional()
    .or(z.literal('')),
  address: z
    .string()
    .max(200, 'Адрес не должен превышать 200 символов')
    .optional(),
  passport_number: z
    .string()
    .max(20, 'Номер паспорта не должен превышать 20 символов')
    .optional(),
  passport_issued_by: z
    .string()
    .max(200, 'Орган выдачи не должен превышать 200 символов')
    .optional(),
  passport_issued_date: z
    .string()
    .optional(),
});

type ClientFormData = z.infer<typeof clientFormSchema>;

interface ClientFormProps {
  clientId?: string;
  initialData?: Partial<Client>;
  onSubmit?: (data: ClientFormData) => void | Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  submitText?: string;
  title?: string;
}

/**
 * Компонент формы для создания и редактирования клиента
 */
export function ClientForm({
  clientId,
  initialData,
  onSubmit,
  onCancel,
  isLoading: externalLoading = false,
  submitText,
  title,
}: ClientFormProps) {
  // Хук для работы с клиентом
  const {
    client,
    loading: clientLoading,
    error: clientError,
    saveClient,
    isSaving,
    saveError,
  } = useClient(clientId);

  // Определяем режим работы формы
  const isEditMode = Boolean(clientId);
  const isCreateMode = !isEditMode;

  // Данные для формы
  const formData = initialData || client;

  // Настройка формы
  const form = useForm<ClientFormData>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: {
      first_name: formData?.first_name || '',
      last_name: formData?.last_name || '',
      middle_name: formData?.middle_name || '',
      email: formData?.email || '',
      phone: formData?.phone || '',
      address: formData?.address || '',
      passport_number: formData?.passport_number || '',
      passport_issued_by: formData?.passport_issued_by || '',
      passport_issued_date: formData?.passport_issued_date || '',
    },
  });

  // Обновляем значения формы при изменении данных
  React.useEffect(() => {
    if (formData) {
      form.reset({
        first_name: formData.first_name || '',
        last_name: formData.last_name || '',
        middle_name: formData.middle_name || '',
        email: formData.email || '',
        phone: formData.phone || '',
        address: formData.address || '',
        passport_number: formData.passport_number || '',
        passport_issued_by: formData.passport_issued_by || '',
        passport_issued_date: formData.passport_issued_date || '',
      });
    }
  }, [formData, form]);

  // Обработчик отправки формы
  const handleSubmit = async (data: ClientFormData) => {
    try {
      if (onSubmit) {
        await onSubmit(data);
      } else if (saveClient) {
        await saveClient(data);
      }
    } catch (error) {
      console.error('Ошибка при сохранении клиента:', error);
    }
  };

  const isFormLoading = externalLoading || clientLoading || isSaving;
  const formError = clientError || saveError;

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="h-5 w-5" />
          <span>
            {title || (isEditMode ? 'Редактирование клиента' : 'Создание клиента')}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Основная информация */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <h3 className="text-lg font-medium">Основная информация</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Фамилия */}
                <FormField
                  control={form.control}
                  name="last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Фамилия *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Иванов"
                          {...field}
                          disabled={isFormLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Имя */}
                <FormField
                  control={form.control}
                  name="first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Имя *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Иван"
                          {...field}
                          disabled={isFormLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Отчество */}
                <FormField
                  control={form.control}
                  name="middle_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Отчество</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Иванович"
                          {...field}
                          disabled={isFormLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Контактная информация */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <h3 className="text-lg font-medium">Контактная информация</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Email */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center space-x-1">
                        <Mail className="h-3 w-3" />
                        <span>Email</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          disabled={isFormLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        Email для связи с клиентом
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Телефон */}
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center space-x-1">
                        <Phone className="h-3 w-3" />
                        <span>Телефон</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          placeholder="+7 (999) 123-45-67"
                          {...field}
                          disabled={isFormLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        Основной номер телефона
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Адрес */}
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center space-x-1">
                      <MapPin className="h-3 w-3" />
                      <span>Адрес</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="г. Москва, ул. Примерная, д. 1, кв. 1"
                        className="min-h-[80px]"
                        {...field}
                        disabled={isFormLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Адрес проживания или регистрации
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Separator />

            {/* Паспортные данные */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4 text-muted-foreground" />
                <h3 className="text-lg font-medium">Паспортные данные</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Номер паспорта */}
                <FormField
                  control={form.control}
                  name="passport_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Серия и номер паспорта</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="1234 567890"
                          {...field}
                          disabled={isFormLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Дата выдачи */}
                <FormField
                  control={form.control}
                  name="passport_issued_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>Дата выдачи</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          disabled={isFormLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Кем выдан */}
              <FormField
                control={form.control}
                name="passport_issued_by"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Кем выдан</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="ОУФМС России по г. Москве в районе..."
                        className="min-h-[80px]"
                        {...field}
                        disabled={isFormLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Орган, выдавший паспорт
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Отображение ошибок */}
            {formError && (
              <div className="p-4 border border-red-200 bg-red-50 rounded-md">
                <p className="text-sm text-red-600">
                  {formError.message || 'Произошла ошибка при сохранении'}
                </p>
              </div>
            )}

            <Separator />

            {/* Кнопки действий */}
            <div className="flex items-center justify-end space-x-2">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isFormLoading}
                >
                  <X className="mr-2 h-4 w-4" />
                  Отмена
                </Button>
              )}
              <Button
                type="submit"
                disabled={isFormLoading}
              >
                {isFormLoading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Сохранение...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {submitText || (isEditMode ? 'Сохранить изменения' : 'Создать клиента')}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
