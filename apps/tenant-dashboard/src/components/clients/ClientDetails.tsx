/**
 * @file: ClientDetails.tsx
 * @description: Компонент детального просмотра клиента
 * @dependencies: react, @pactcrm/supabase-client, @pactcrm/ui
 * @created: 2024-12-26
 */

"use client";

import React from 'react';
import { 
  Users, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Edit,
  ArrowLeft,
  FileText,
  DollarSign,
  TrendingUp,
  CreditCard,
  Building,
  Home,
  UserCheck,
  UserX,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { useClient, useClientStats } from '@pactcrm/supabase-client';
import { Client } from '@pactcrm/supabase-client';

interface ClientDetailsProps {
  clientId: string;
  onEdit?: (client: Client) => void;
  onBack?: () => void;
  showActions?: boolean;
}

/**
 * Компонент детального просмотра клиента
 */
export function ClientDetails({
  clientId,
  onEdit,
  onBack,
  showActions = true,
}: ClientDetailsProps) {
  // Получение данных о клиенте
  const {
    client,
    loading: clientLoading,
    error: clientError,
  } = useClient(clientId);

  // Получение статистики
  const {
    stats,
    isLoading: statsLoading,
    error: statsError,
  } = useClientStats(clientId);

  // Функция для получения цвета статуса
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'potential':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'blocked':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // Функция для получения текста статуса
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Активный';
      case 'potential':
        return 'Потенциальный';
      case 'inactive':
        return 'Неактивный';
      case 'blocked':
        return 'Заблокирован';
      default:
        return status;
    }
  };

  // Функция для получения иконки статуса
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <UserCheck className="h-4 w-4" />;
      case 'potential':
        return <Users className="h-4 w-4" />;
      case 'inactive':
        return <UserX className="h-4 w-4" />;
      case 'blocked':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  // Функция для форматирования суммы
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Функция для форматирования даты
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (clientError) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Ошибка при загрузке клиента</p>
            <p className="text-sm text-muted-foreground mt-1">
              {clientError.message}
            </p>
            {onBack && (
              <Button variant="outline" onClick={onBack} className="mt-4">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Назад
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (clientLoading || !client) {
    return (
      <div className="space-y-6">
        {/* Заголовок */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-[300px]" />
            <Skeleton className="h-4 w-[200px]" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-10 w-[100px]" />
            <Skeleton className="h-10 w-[120px]" />
          </div>
        </div>

        {/* Карточки статистики */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-[100px] mb-2" />
                <Skeleton className="h-8 w-[60px]" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Основная информация */}
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-[200px]" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-[80%]" />
            <Skeleton className="h-4 w-[60%]" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Заголовок и действия */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            {onBack && (
              <Button variant="ghost" size="sm" onClick={onBack}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            <Users className="h-6 w-6" />
            <h1 className="text-2xl font-bold">{client.full_name}</h1>
            <Badge className={getStatusColor(client.status || 'potential')}>
              <div className="flex items-center space-x-1">
                {getStatusIcon(client.status || 'potential')}
                <span>{getStatusText(client.status || 'potential')}</span>
              </div>
            </Badge>
          </div>
          <div className="flex items-center space-x-4 text-muted-foreground">
            {client.email && (
              <div className="flex items-center space-x-1">
                <Mail className="h-4 w-4" />
                <span>{client.email}</span>
              </div>
            )}
            {client.phone && (
              <div className="flex items-center space-x-1">
                <Phone className="h-4 w-4" />
                <span>{client.phone}</span>
              </div>
            )}
          </div>
        </div>

        {showActions && (
          <div className="flex space-x-2">
            {onEdit && (
              <Button onClick={() => onEdit(client)}>
                <Edit className="mr-2 h-4 w-4" />
                Редактировать
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Карточки статистики */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Договоры</span>
            </div>
            <div className="text-2xl font-bold">
              {statsLoading ? (
                <Skeleton className="h-8 w-[60px]" />
              ) : (
                stats?.total_contracts || client.contracts_count || 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats?.active_contracts || client.active_contracts || 0} активных
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Общая сумма</span>
            </div>
            <div className="text-2xl font-bold">
              {statsLoading ? (
                <Skeleton className="h-8 w-[100px]" />
              ) : (
                stats?.total_contract_amount || client.total_contract_amount 
                  ? formatAmount(stats?.total_contract_amount || client.total_contract_amount || 0)
                  : '—'
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Риск-скор</span>
            </div>
            <div className="text-2xl font-bold">
              {statsLoading ? (
                <Skeleton className="h-8 w-[60px]" />
              ) : (
                `${stats?.risk_score || 0}%`
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              {(stats?.risk_score || 0) < 30 ? 'Низкий риск' : 
               (stats?.risk_score || 0) < 70 ? 'Средний риск' : 'Высокий риск'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-muted-foreground">Регистрация</span>
            </div>
            <div className="text-2xl font-bold">
              {formatDate(client.created_at).split(' ')[0]}
            </div>
            <p className="text-xs text-muted-foreground">
              {formatDate(client.created_at)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Основная информация */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Личная информация</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Фамилия</Label>
                <p className="text-sm">{client.last_name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Имя</Label>
                <p className="text-sm">{client.first_name}</p>
              </div>
            </div>

            {client.middle_name && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Отчество</Label>
                <p className="text-sm">{client.middle_name}</p>
              </div>
            )}

            <Separator />

            <div className="space-y-3">
              {client.email && (
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Email</Label>
                    <p className="text-sm">{client.email}</p>
                  </div>
                </div>
              )}

              {client.phone && (
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Телефон</Label>
                    <p className="text-sm">{client.phone}</p>
                  </div>
                </div>
              )}

              {client.address && (
                <div className="flex items-start space-x-2">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Адрес</Label>
                    <p className="text-sm">{client.address}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Паспортные данные */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4" />
              <span>Паспортные данные</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {client.passport_number && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Серия и номер</Label>
                <p className="text-sm font-mono">{client.passport_number}</p>
              </div>
            )}

            {client.passport_issued_date && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Дата выдачи</Label>
                <p className="text-sm">{formatDate(client.passport_issued_date)}</p>
              </div>
            )}

            {client.passport_issued_by && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Кем выдан</Label>
                <p className="text-sm">{client.passport_issued_by}</p>
              </div>
            )}

            {!client.passport_number && !client.passport_issued_date && !client.passport_issued_by && (
              <p className="text-sm text-muted-foreground">Паспортные данные не указаны</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Договоры клиента */}
      {client.contracts && client.contracts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Договоры клиента</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {client.contracts.map((contract) => (
                <div key={contract.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">Договор №{contract.number}</span>
                      <Badge variant={contract.status === 'active' ? 'default' : 'secondary'}>
                        {contract.status === 'active' ? 'Активный' : contract.status}
                      </Badge>
                    </div>
                    <span className="font-medium">{formatAmount(contract.total_amount)}</span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
                    <div>
                      <Label className="text-xs">Объект</Label>
                      <p>{contract.complex_name} - {contract.building_name}, кв. {contract.apartment_name}</p>
                    </div>
                    <div>
                      <Label className="text-xs">Дата подписания</Label>
                      <p>{formatDate(contract.signed_date)}</p>
                    </div>
                    <div>
                      <Label className="text-xs">Ежемесячный платеж</Label>
                      <p>{formatAmount(contract.monthly_payment)}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
