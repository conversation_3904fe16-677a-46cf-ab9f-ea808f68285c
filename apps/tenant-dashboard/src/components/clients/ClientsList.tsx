/**
 * @file: ClientsList.tsx
 * @description: Компонент для отображения списка клиентов
 * @dependencies: react, @pactcrm/supabase-client, @pactcrm/ui
 * @created: 2024-12-26
 */

"use client";

import React, { useState, useMemo, useCallback } from 'react';
import { 
  Users, 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2,
  Mail,
  Phone,
  FileText,
  TrendingUp,
  MoreHorizontal,
  UserCheck,
  UserX,
  AlertTriangle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useClients } from '@pactcrm/supabase-client';
import { Client, ClientStatus, ClientFilters, ClientPagination, ClientSorting } from '@pactcrm/supabase-client';

interface ClientsListProps {
  onClientSelect?: (client: Client) => void;
  onClientCreate?: () => void;
  onClientEdit?: (client: Client) => void;
  onClientDelete?: (client: Client) => void;
  onClientView?: (client: Client) => void;
}

/**
 * Компонент для отображения списка клиентов
 */
export function ClientsList({
  onClientSelect,
  onClientCreate,
  onClientEdit,
  onClientDelete,
  onClientView,
}: ClientsListProps) {
  // Состояние фильтров и пагинации
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState<ClientStatus | 'all'>('all');
  const [contractsFilter, setContractsFilter] = useState<'all' | 'with_contracts' | 'without_contracts'>('all');
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [sortField, setSortField] = useState<ClientSorting['field']>('created_at');
  const [sortOrder, setSortOrder] = useState<ClientSorting['order']>('desc');

  // Подготовка фильтров для API
  const filters = useMemo<ClientFilters>(() => {
    const result: ClientFilters = {};
    
    if (search.trim()) {
      result.search = search.trim();
    }
    
    if (statusFilter !== 'all') {
      result.status = statusFilter as ClientStatus;
    }

    if (contractsFilter === 'with_contracts') {
      result.has_contracts = true;
    } else if (contractsFilter === 'without_contracts') {
      result.has_contracts = false;
    }
    
    return result;
  }, [search, statusFilter, contractsFilter]);

  const pagination = useMemo<ClientPagination>(() => ({
    page,
    limit,
  }), [page, limit]);

  const sorting = useMemo<ClientSorting>(() => ({
    field: sortField,
    order: sortOrder,
  }), [sortField, sortOrder]);

  // Получение данных
  const {
    clients,
    count,
    total_pages,
    isLoading,
    error,
    refresh,
  } = useClients(filters, pagination, sorting);

  // Обработчики событий
  const handleSearch = useCallback((value: string) => {
    setSearch(value);
    setPage(1); // Сброс на первую страницу при поиске
  }, []);

  const handleStatusFilter = useCallback((value: string) => {
    setStatusFilter(value as ClientStatus | 'all');
    setPage(1); // Сброс на первую страницу при фильтрации
  }, []);

  const handleContractsFilter = useCallback((value: string) => {
    setContractsFilter(value as 'all' | 'with_contracts' | 'without_contracts');
    setPage(1); // Сброс на первую страницу при фильтрации
  }, []);

  const handleSort = useCallback((field: ClientSorting['field']) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
    setPage(1); // Сброс на первую страницу при сортировке
  }, [sortField, sortOrder]);

  // Функция для получения цвета статуса
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'potential':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'blocked':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // Функция для получения текста статуса
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Активный';
      case 'potential':
        return 'Потенциальный';
      case 'inactive':
        return 'Неактивный';
      case 'blocked':
        return 'Заблокирован';
      default:
        return status;
    }
  };

  // Функция для получения иконки статуса
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <UserCheck className="h-3 w-3" />;
      case 'potential':
        return <Users className="h-3 w-3" />;
      case 'inactive':
        return <UserX className="h-3 w-3" />;
      case 'blocked':
        return <AlertTriangle className="h-3 w-3" />;
      default:
        return <Users className="h-3 w-3" />;
    }
  };

  // Функция для форматирования суммы
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Функция для форматирования даты
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Ошибка при загрузке клиентов</p>
            <Button variant="outline" onClick={refresh} className="mt-2">
              Попробовать снова
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Заголовок и кнопка создания */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Users className="h-6 w-6" />
          <h1 className="text-2xl font-bold">Клиенты</h1>
        </div>
        {onClientCreate && (
          <Button onClick={onClientCreate}>
            <Plus className="mr-2 h-4 w-4" />
            Добавить клиента
          </Button>
        )}
      </div>

      {/* Фильтры и поиск */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4">
            {/* Поиск */}
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Поиск по имени, email или телефону..."
                className="pl-8"
                value={search}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>

            {/* Фильтр по статусу */}
            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Статус" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Все статусы</SelectItem>
                <SelectItem value="active">Активные</SelectItem>
                <SelectItem value="potential">Потенциальные</SelectItem>
                <SelectItem value="inactive">Неактивные</SelectItem>
                <SelectItem value="blocked">Заблокированные</SelectItem>
              </SelectContent>
            </Select>

            {/* Фильтр по договорам */}
            <Select value={contractsFilter} onValueChange={handleContractsFilter}>
              <SelectTrigger className="w-full md:w-[200px]">
                <FileText className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Договоры" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Все клиенты</SelectItem>
                <SelectItem value="with_contracts">С договорами</SelectItem>
                <SelectItem value="without_contracts">Без договоров</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Таблица */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('first_name')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Клиент</span>
                    {(sortField === 'first_name' || sortField === 'last_name') && (
                      <span className="text-xs">
                        {sortOrder === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead>Контакты</TableHead>
                <TableHead>Статус</TableHead>
                <TableHead className="text-center">Договоры</TableHead>
                <TableHead className="text-right">Общая сумма</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50 text-right"
                  onClick={() => handleSort('created_at')}
                >
                  <div className="flex items-center justify-end space-x-1">
                    <span>Регистрация</span>
                    {sortField === 'created_at' && (
                      <span className="text-xs">
                        {sortOrder === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                // Skeleton loading
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    <TableCell><Skeleton className="h-4 w-[200px]" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-[100px]" /></TableCell>
                    <TableCell className="text-center"><Skeleton className="h-4 w-[40px] mx-auto" /></TableCell>
                    <TableCell className="text-right"><Skeleton className="h-4 w-[100px] ml-auto" /></TableCell>
                    <TableCell className="text-right"><Skeleton className="h-4 w-[80px] ml-auto" /></TableCell>
                    <TableCell><Skeleton className="h-8 w-8" /></TableCell>
                  </TableRow>
                ))
              ) : clients.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex flex-col items-center space-y-2">
                      <Users className="h-8 w-8 text-muted-foreground" />
                      <p className="text-muted-foreground">
                        {search || statusFilter !== 'all' || contractsFilter !== 'all'
                          ? 'Клиенты не найдены' 
                          : 'Пока нет клиентов'
                        }
                      </p>
                      {onClientCreate && !search && statusFilter === 'all' && contractsFilter === 'all' && (
                        <Button variant="outline" onClick={onClientCreate}>
                          <Plus className="mr-2 h-4 w-4" />
                          Добавить первого клиента
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                clients.map((client) => (
                  <TableRow 
                    key={client.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => onClientSelect?.(client)}
                  >
                    <TableCell className="font-medium">
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{client.full_name}</p>
                          {client.address && (
                            <p className="text-xs text-muted-foreground truncate max-w-[200px]">
                              {client.address}
                            </p>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {client.email && (
                          <div className="flex items-center space-x-1 text-sm">
                            <Mail className="h-3 w-3 text-muted-foreground" />
                            <span className="truncate max-w-[150px]">{client.email}</span>
                          </div>
                        )}
                        {client.phone && (
                          <div className="flex items-center space-x-1 text-sm">
                            <Phone className="h-3 w-3 text-muted-foreground" />
                            <span>{client.phone}</span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(client.status || 'potential')}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(client.status || 'potential')}
                          <span>{getStatusText(client.status || 'potential')}</span>
                        </div>
                      </Badge>
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <FileText className="h-3 w-3 text-muted-foreground" />
                        <span>{client.contracts_count || 0}</span>
                        {(client.active_contracts || 0) > 0 && (
                          <span className="text-xs text-green-600">
                            ({client.active_contracts} активных)
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {client.total_contract_amount ? formatAmount(client.total_contract_amount) : '—'}
                    </TableCell>
                    <TableCell className="text-right text-sm text-muted-foreground">
                      {formatDate(client.created_at)}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Открыть меню</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Действия</DropdownMenuLabel>
                          {onClientView && (
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              onClientView(client);
                            }}>
                              <Eye className="mr-2 h-4 w-4" />
                              Просмотр
                            </DropdownMenuItem>
                          )}
                          {onClientEdit && (
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              onClientEdit(client);
                            }}>
                              <Edit className="mr-2 h-4 w-4" />
                              Редактировать
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          {onClientDelete && (
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={(e) => {
                                e.stopPropagation();
                                onClientDelete(client);
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Удалить
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Пагинация */}
      {total_pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Показано {clients.length} из {count} клиентов
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
            >
              Назад
            </Button>
            <span className="text-sm">
              Страница {page} из {total_pages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page >= total_pages}
            >
              Далее
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
