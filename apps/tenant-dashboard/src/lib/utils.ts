/**
 * @file: utils.ts
 * @description: Утилиты для работы с данными
 * @dependencies: clsx, tailwind-merge, date-fns
 * @created: 2024-05-10
 */

import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { format, parseISO } from "date-fns";
import { ru } from "date-fns/locale";

/**
 * Объединяет классы Tailwind CSS
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Форматирует дату в локализованный формат
 * 
 * @param date Дата в формате строки или объекта Date
 * @param formatStr Строка формата (по умолчанию 'dd.MM.yyyy')
 * @returns Отформатированная дата
 */
export function formatDate(date: string | Date, formatStr = 'dd.MM.yyyy'): string {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, formatStr, { locale: ru });
  } catch (error) {
    console.error('Ошибка при форматировании даты:', error);
    return String(date);
  }
}

/**
 * Форматирует число в денежный формат
 * 
 * @param amount Сумма
 * @param currency Валюта (по умолчанию '₽')
 * @returns Отформатированная сумма
 */
export function formatCurrency(amount: number, currency = '₽'): string {
  if (amount === undefined || amount === null) return '';
  
  try {
    const formatter = new Intl.NumberFormat('ru-RU', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
    
    return `${currency} ${formatter.format(amount)}`;
  } catch (error) {
    console.error('Ошибка при форматировании суммы:', error);
    return `${currency} ${amount}`;
  }
}
