@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .button {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .button-default {
    @apply bg-gray-900 text-white hover:bg-gray-800;
  }
  
  .button-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700;
  }
  
  .button-secondary {
    @apply bg-gray-100 text-gray-900 hover:bg-gray-200;
  }
  
  .button-outline {
    @apply border border-gray-300 bg-transparent hover:bg-gray-100;
  }
  
  .button-ghost {
    @apply bg-transparent hover:bg-gray-100;
  }
  
  .button-link {
    @apply bg-transparent underline-offset-4 hover:underline;
  }
  
  .button-destructive {
    @apply bg-red-600 text-white hover:bg-red-700;
  }
  
  .button-default {
    @apply h-10 py-2 px-4;
  }
  
  .button-sm {
    @apply h-8 px-3 text-xs;
  }
  
  .button-lg {
    @apply h-12 px-8 text-lg;
  }
  
  .button-icon {
    @apply h-10 w-10;
  }
  
  .loading-spinner {
    @apply mr-2 h-4 w-4 animate-spin;
  }
}
