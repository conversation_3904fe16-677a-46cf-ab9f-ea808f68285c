{"name": "client-app", "version": "0.0.0", "private": true, "scripts": {"dev": "next dev --port 3002", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@pactcrm/supabase-client": "workspace:*", "@pactcrm/ui": "workspace:*", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/supabase-js": "^2.39.3", "next": "^15.0.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-custom": "workspace:*", "postcss": "^8.4.32", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "tsconfig": "workspace:*", "typescript": "^5.3.3"}}