name: Deploy to Vercel

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID_TENANT: ${{ secrets.VERCEL_PROJECT_ID_TENANT }}
  VERCEL_PROJECT_ID_CLIENT: ${{ secrets.VERCEL_PROJECT_ID_CLIENT }}
  VERCEL_PROJECT_ID_ADMIN: ${{ secrets.VERCEL_PROJECT_ID_ADMIN }}

jobs:
  deploy:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [tenant-dashboard, client-app, super-admin]
        include:
          - app: tenant-dashboard
            project_id: VERCEL_PROJECT_ID_TENANT
            directory: apps/tenant-dashboard
          - app: client-app
            project_id: VERCEL_PROJECT_ID_CLIENT
            directory: apps/client-app
          - app: super-admin
            project_id: VERCEL_PROJECT_ID_ADMIN
            directory: apps/super-admin

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8.15.1

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v3
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build packages
        run: pnpm run build --filter=@pactcrm/ui --filter=@pactcrm/supabase-client

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ${{ matrix.directory }}
        env:
          VERCEL_PROJECT_ID: ${{ secrets[matrix.project_id] }}

      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ${{ matrix.directory }}
        env:
          VERCEL_PROJECT_ID: ${{ secrets[matrix.project_id] }}

      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ${{ matrix.directory }}
        env:
          VERCEL_PROJECT_ID: ${{ secrets[matrix.project_id] }}
