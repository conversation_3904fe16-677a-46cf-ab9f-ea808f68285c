import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

/**
 * Middleware для проверки авторизации и ролей пользователей
 * 
 * Перехватывает запросы к защищенным маршрутам и проверяет, имеет ли пользователь
 * соответствующую роль для доступа. Если пользователь не авторизован или не имеет
 * нужной роли, он будет перенаправлен на соответствующую страницу авторизации.
 * 
 * @param req Запрос Next.js
 * @returns Ответ Next.js
 */
export async function middleware(req: NextRequest) {
  // Создаем ответ, который будет передан клиенту Supabase для модификации заголовков
  const res = NextResponse.next()
  
  // Создаем клиент Supabase для middleware
  const supabase = createMiddlewareClient({ req, res })
  
  // Получаем сессию пользователя
  const {
    data: { session },
  } = await supabase.auth.getSession()
  
  // Если пользователь не авторизован и пытается получить доступ к защищенным маршрутам
  if (!session) {
    // Определяем, к какому типу интерфейса пытается получить доступ пользователь
    if (req.nextUrl.pathname.startsWith('/admin')) {
      return NextResponse.redirect(new URL('/admin/login', req.url))
    } else if (req.nextUrl.pathname.startsWith('/client')) {
      return NextResponse.redirect(new URL('/client/login', req.url))
    } else if (req.nextUrl.pathname.startsWith('/dashboard')) {
      return NextResponse.redirect(new URL('/login', req.url))
    }
  }
  
  // Если пользователь авторизован, проверяем его роль
  if (session) {
    const role = session.user?.user_metadata?.role
    
    // Проверка доступа к админ-панели
    if (req.nextUrl.pathname.startsWith('/admin') && 
        !(role === 'superadmin' || role === 'support')) {
      return NextResponse.redirect(new URL('/', req.url))
    }
    
    // Проверка доступа к панели застройщика
    if (req.nextUrl.pathname.startsWith('/dashboard') && 
        !(role === 'tenant_admin' || role === 'after_sales_manager')) {
      return NextResponse.redirect(new URL('/', req.url))
    }
    
    // Проверка доступа к клиентскому приложению
    if (req.nextUrl.pathname.startsWith('/client') && 
        !(role === 'client')) {
      return NextResponse.redirect(new URL('/', req.url))
    }
  }
  
  return res
}

// Указываем, для каких маршрутов применять middleware
export const config = {
  matcher: ['/admin/:path*', '/dashboard/:path*', '/client/:path*'],
}
