/**
 * @file: update-docs.js
 * @description: Скрипт для автоматического обновления документации проекта
 * @dependencies: fs, path
 * @created: 2023-11-15
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Пути к файлам документации
const DOCS_DIR = path.join(__dirname, 'docs');
const PROJECT_MD = path.join(DOCS_DIR, 'Project.md');
const CHANGELOG_MD = path.join(DOCS_DIR, 'changelog.md');
const TASKTRACKER_MD = path.join(DOCS_DIR, 'Tasktracker.md');

/**
 * Обновляет файл changelog.md с новыми изменениями
 * @param {string} type - Тип изменения: 'added', 'changed', 'fixed'
 * @param {string} description - Описание изменения
 */
function updateChangelog(type, description) {
  if (!description) return;
  
  // Чтение текущего содержимого файла
  let content = fs.readFileSync(CHANGELOG_MD, 'utf8');
  
  // Получение текущей даты в формате YYYY-MM-DD
  const today = new Date().toISOString().split('T')[0];
  
  // Проверка, есть ли уже запись для сегодняшней даты
  const todayHeader = `## [${today}]`;
  const unreleasedHeader = '## [Неопубликовано]';
  
  let updatedContent;
  
  if (content.includes(todayHeader)) {
    // Если запись для сегодняшней даты уже существует, добавляем изменение в соответствующий раздел
    const typeHeaders = {
      'added': '### Добавлено',
      'changed': '### Изменено',
      'fixed': '### Исправлено'
    };
    
    const typeHeader = typeHeaders[type];
    
    if (content.includes(`${todayHeader}${content.split(todayHeader)[1].includes(typeHeader)}`)) {
      // Если раздел типа уже существует, добавляем изменение в него
      const parts = content.split(typeHeader);
      const beforeType = parts[0];
      const afterType = parts[1];
      
      // Находим конец списка изменений этого типа
      const endOfList = afterType.indexOf('###') > -1 ? afterType.indexOf('###') : afterType.indexOf('##');
      const listContent = endOfList > -1 ? afterType.substring(0, endOfList) : afterType;
      
      // Добавляем новое изменение в список
      const newListContent = listContent + `- ${description}\n`;
      
      // Собираем обновленное содержимое
      updatedContent = beforeType + typeHeader + newListContent + (endOfList > -1 ? afterType.substring(endOfList) : '');
    } else {
      // Если раздел типа не существует, создаем его
      const parts = content.split(todayHeader);
      const beforeToday = parts[0];
      const afterToday = parts[1];
      
      // Добавляем новый раздел типа после заголовка даты
      updatedContent = beforeToday + todayHeader + '\n\n' + typeHeader + '\n- ' + description + '\n\n' + afterToday;
    }
  } else if (content.includes(unreleasedHeader)) {
    // Если нет записи для сегодняшней даты, но есть раздел "Неопубликовано",
    // перемещаем содержимое из "Неопубликовано" в новую запись для сегодняшней даты
    const parts = content.split(unreleasedHeader);
    const beforeUnreleased = parts[0];
    const afterUnreleased = parts[1];
    
    // Находим конец раздела "Неопубликовано"
    const endOfUnreleased = afterUnreleased.indexOf('##');
    const unreleasedContent = endOfUnreleased > -1 ? afterUnreleased.substring(0, endOfUnreleased) : afterUnreleased;
    
    // Создаем новый раздел для сегодняшней даты с содержимым из "Неопубликовано"
    updatedContent = beforeUnreleased + unreleasedHeader + '\n\n' + 
                    `## [${today}]` + unreleasedContent + 
                    (endOfUnreleased > -1 ? afterUnreleased.substring(endOfUnreleased) : '');
    
    // Теперь добавляем новое изменение в раздел "Неопубликовано"
    return updateChangelog(type, description);
  } else {
    // Если нет ни записи для сегодняшней даты, ни раздела "Неопубликовано",
    // создаем новую запись для сегодняшней даты
    const typeHeaders = {
      'added': '### Добавлено',
      'changed': '### Изменено',
      'fixed': '### Исправлено'
    };
    
    const newEntry = `\n## [${today}] - Обновление проекта\n\n${typeHeaders[type]}\n- ${description}\n\n`;
    
    // Находим место для вставки новой записи (после заголовка и описания)
    const insertIndex = content.indexOf('## [');
    if (insertIndex > -1) {
      updatedContent = content.substring(0, insertIndex) + newEntry + content.substring(insertIndex);
    } else {
      updatedContent = content + newEntry;
    }
  }
  
  // Записываем обновленное содержимое в файл
  fs.writeFileSync(CHANGELOG_MD, updatedContent, 'utf8');
  console.log(`Changelog обновлен: ${type} - ${description}`);
}

/**
 * Обновляет статус задачи в файле tasktracker.md
 * @param {string} taskName - Название задачи
 * @param {string} status - Новый статус задачи: 'not-started', 'in-progress', 'blocked', 'review', 'completed'
 */
function updateTaskStatus(taskName, status) {
  if (!taskName || !status) return;
  
  // Чтение текущего содержимого файла
  let content = fs.readFileSync(TASKTRACKER_MD, 'utf8');
  
  // Маппинг статусов на эмодзи
  const statusEmojis = {
    'not-started': '⬜',
    'in-progress': '🔄',
    'blocked': '🚫',
    'review': '👁️',
    'completed': '✅'
  };
  
  // Маппинг статусов на текстовые описания
  const statusTexts = {
    'not-started': 'Не начато',
    'in-progress': 'В процессе',
    'blocked': 'Заблокировано',
    'review': 'На проверке',
    'completed': 'Завершено'
  };
  
  // Поиск задачи в файле
  const taskRegex = new RegExp(`### Задача: ${taskName}[\\s\\S]*?- \\*\\*Статус\\*\\*: [^\\n]*`, 'i');
  const match = content.match(taskRegex);
  
  if (match) {
    // Замена статуса задачи
    const statusRegex = /- \*\*Статус\*\*: [^\n]*/;
    const newStatusLine = `- **Статус**: ${statusEmojis[status]} ${statusTexts[status]}`;
    const updatedContent = content.replace(statusRegex, newStatusLine);
    
    // Записываем обновленное содержимое в файл
    fs.writeFileSync(TASKTRACKER_MD, updatedContent, 'utf8');
    console.log(`Статус задачи "${taskName}" обновлен на "${statusTexts[status]}"`);
  } else {
    console.error(`Задача "${taskName}" не найдена в файле tasktracker.md`);
  }
}

/**
 * Обновляет файл project.md с информацией о новых компонентах или изменениях в архитектуре
 * @param {string} section - Раздел документации для обновления
 * @param {string} content - Новое содержимое раздела
 */
function updateProjectDoc(section, content) {
  if (!section || !content) return;
  
  // Чтение текущего содержимого файла
  let projectContent = fs.readFileSync(PROJECT_MD, 'utf8');
  
  // Поиск раздела в файле
  const sectionRegex = new RegExp(`## ${section}[\\s\\S]*?(##|$)`, 'i');
  const match = projectContent.match(sectionRegex);
  
  if (match) {
    // Определяем, заканчивается ли раздел другим заголовком или концом файла
    const endsWithHeader = match[0].endsWith('##');
    
    // Заменяем содержимое раздела, сохраняя заголовок и следующий заголовок (если есть)
    const sectionHeader = `## ${section}`;
    const nextHeader = endsWithHeader ? '##' : '';
    
    const updatedSection = `${sectionHeader}\n\n${content}\n\n${nextHeader}`;
    const updatedContent = projectContent.replace(sectionRegex, updatedSection);
    
    // Записываем обновленное содержимое в файл
    fs.writeFileSync(PROJECT_MD, updatedContent, 'utf8');
    console.log(`Раздел "${section}" в project.md обновлен`);
  } else {
    // Если раздел не найден, добавляем его в конец файла
    const newSection = `\n## ${section}\n\n${content}\n`;
    fs.writeFileSync(PROJECT_MD, projectContent + newSection, 'utf8');
    console.log(`Добавлен новый раздел "${section}" в project.md`);
  }
}

// Экспортируем функции для использования в других скриптах
module.exports = {
  updateChangelog,
  updateTaskStatus,
  updateProjectDoc
};

// Если скрипт запущен напрямую, выводим инструкцию по использованию
if (require.main === module) {
  console.log(`
Скрипт для обновления документации проекта PactCRM

Использование:
  node update-docs.js changelog <type> <description>
  node update-docs.js task <taskName> <status>
  node update-docs.js project <section> <contentFile>

Примеры:
  node update-docs.js changelog added "Добавлена функция авторизации через OAuth"
  node update-docs.js task "Начальная настройка проекта" completed
  node update-docs.js project "Технологический стек" ./tech-stack-update.md
  `);
}
