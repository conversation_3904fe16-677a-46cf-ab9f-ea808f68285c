/**
 * @file: update-docs.js
 * @description: Скрипт для обновления документации проекта через командную строку
 * @dependencies: ../update-docs.js
 * @created: 2023-11-15
 */

const { updateChangelog, updateTaskStatus, updateProjectDoc } = require('../update-docs');
const fs = require('fs');
const path = require('path');

// Получаем аргументы командной строки
const args = process.argv.slice(2);

if (args.length < 2) {
  console.error('Недостаточно аргументов');
  showHelp();
  process.exit(1);
}

const command = args[0];

switch (command) {
  case 'changelog':
    if (args.length < 3) {
      console.error('Недостаточно аргументов для команды changelog');
      showHelp();
      process.exit(1);
    }
    
    const type = args[1];
    const description = args[2];
    
    if (!['added', 'changed', 'fixed'].includes(type)) {
      console.error('Неверный тип изменения. Допустимые значения: added, changed, fixed');
      process.exit(1);
    }
    
    updateChangelog(type, description);
    break;
    
  case 'task':
    if (args.length < 3) {
      console.error('Недостаточно аргументов для команды task');
      showHelp();
      process.exit(1);
    }
    
    const taskName = args[1];
    const status = args[2];
    
    if (!['not-started', 'in-progress', 'blocked', 'review', 'completed'].includes(status)) {
      console.error('Неверный статус задачи. Допустимые значения: not-started, in-progress, blocked, review, completed');
      process.exit(1);
    }
    
    updateTaskStatus(taskName, status);
    break;
    
  case 'project':
    if (args.length < 3) {
      console.error('Недостаточно аргументов для команды project');
      showHelp();
      process.exit(1);
    }
    
    const section = args[1];
    const contentFile = args[2];
    
    if (!fs.existsSync(contentFile)) {
      console.error(`Файл ${contentFile} не найден`);
      process.exit(1);
    }
    
    const content = fs.readFileSync(contentFile, 'utf8');
    updateProjectDoc(section, content);
    break;
    
  default:
    console.error(`Неизвестная команда: ${command}`);
    showHelp();
    process.exit(1);
}

function showHelp() {
  console.log(`
Скрипт для обновления документации проекта PactCRM

Использование:
  node scripts/update-docs.js changelog <type> <description>
  node scripts/update-docs.js task <taskName> <status>
  node scripts/update-docs.js project <section> <contentFile>

Примеры:
  node scripts/update-docs.js changelog added "Добавлена функция авторизации через OAuth"
  node scripts/update-docs.js task "Начальная настройка проекта" completed
  node scripts/update-docs.js project "Технологический стек" ./tech-stack-update.md
  `);
}
