/**
 * @file: apartments.ts
 * @description: API функции для работы с квартирами
 * @dependencies: @supabase/supabase-js, types/properties
 * @created: 2024-12-26
 */

import { SupabaseClient } from '@supabase/supabase-js';
import {
  Apartment,
  ApartmentInsert,
  ApartmentUpdate,
  ApartmentFilters,
  PropertyPagination,
  ApartmentSorting,
  ApartmentsResult,
  BulkApartmentCreate,
  BulkApartmentResult,
} from '../types/properties';

/**
 * Получение списка квартир с фильтрацией, пагинацией и сортировкой
 *
 * @param supabase Клиент Supabase
 * @param filters Фильтры для квартир
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Результат запроса с квартирами и метаданными
 */
export async function getApartments(
  supabase: SupabaseClient,
  filters?: ApartmentFilters,
  pagination?: PropertyPagination,
  sorting?: ApartmentSorting
): Promise<ApartmentsResult> {
  try {
    // Базовый запрос с информацией о здании и комплексе
    let query = supabase
      .from('apartments')
      .select(`
        *,
        buildings!apartments_building_id_fkey (
          id,
          name,
          address,
          complexes!buildings_complex_id_fkey (
            id,
            name
          )
        ),
        contracts!apartments_contracts_apartment_id_fkey (
          id,
          clients!contracts_client_id_fkey (
            id,
            first_name,
            last_name,
            middle_name
          )
        )
      `, { count: 'exact' });

    // Применение фильтров
    if (filters) {
      if (filters.search) {
        query = query.or(`number.ilike.%${filters.search}%`);
      }

      if (filters.complex_id) {
        query = query.eq('buildings.complex_id', filters.complex_id);
      }

      if (filters.building_id) {
        query = query.eq('building_id', filters.building_id);
      }

      if (filters.status) {
        if (Array.isArray(filters.status)) {
          query = query.in('status', filters.status);
        } else {
          query = query.eq('status', filters.status);
        }
      }

      if (filters.min_rooms !== undefined) {
        query = query.gte('rooms', filters.min_rooms);
      }

      if (filters.max_rooms !== undefined) {
        query = query.lte('rooms', filters.max_rooms);
      }

      if (filters.min_area !== undefined) {
        query = query.gte('area', filters.min_area);
      }

      if (filters.max_area !== undefined) {
        query = query.lte('area', filters.max_area);
      }

      if (filters.min_price !== undefined) {
        query = query.gte('price', filters.min_price);
      }

      if (filters.max_price !== undefined) {
        query = query.lte('price', filters.max_price);
      }

      if (filters.min_floor !== undefined) {
        query = query.gte('floor', filters.min_floor);
      }

      if (filters.max_floor !== undefined) {
        query = query.lte('floor', filters.max_floor);
      }

      if (filters.created_after) {
        query = query.gte('created_at', filters.created_after);
      }

      if (filters.created_before) {
        query = query.lte('created_at', filters.created_before);
      }
    }

    // Применение сортировки
    if (sorting?.field && sorting?.order) {
      query = query.order(sorting.field, { ascending: sorting.order === 'asc' });
    } else {
      // Сортировка по умолчанию
      query = query.order('created_at', { ascending: false });
    }

    // Применение пагинации
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 20;
    const offset = pagination?.offset || (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    if (!data) {
      return {
        data: [],
        count: 0,
        page,
        limit,
        total_pages: 0,
      };
    }

    // Обработка данных и добавление вычисляемых полей
    const apartments: Apartment[] = data.map((apartment) => {
      const building = apartment.buildings;
      const complex = building?.complexes;
      const contracts = apartment.contracts || [];
      const activeContract = contracts.find((contract: any) => contract.id);
      const client = activeContract?.clients;

      // Удаляем вложенные объекты для чистоты ответа
      const { buildings: _, contracts: __, ...apartmentData } = apartment;

      return {
        ...apartmentData,
        building_name: building?.name,
        complex_name: complex?.name,
        is_sold: apartment.status === 'sold',
        contract_id: activeContract?.id,
        client_name: client ? `${client.last_name} ${client.first_name} ${client.middle_name || ''}`.trim() : undefined,
      };
    });

    const total_pages = Math.ceil((count || 0) / limit);

    return {
      data: apartments,
      count: count || 0,
      page,
      limit,
      total_pages,
    };
  } catch (error) {
    console.error('Ошибка при получении квартир:', error);
    throw error;
  }
}

/**
 * Получение квартиры по ID
 *
 * @param supabase Клиент Supabase
 * @param id ID квартиры
 * @returns Квартира с дополнительной информацией
 */
export async function getApartmentById(
  supabase: SupabaseClient,
  id: string
): Promise<Apartment | null> {
  try {
    const { data, error } = await supabase
      .from('apartments')
      .select(`
        *,
        buildings!apartments_building_id_fkey (
          id,
          name,
          address,
          floors,
          status,
          complexes!buildings_complex_id_fkey (
            id,
            name,
            address,
            description,
            status
          )
        ),
        contracts!apartments_contracts_apartment_id_fkey (
          id,
          number,
          signed_date,
          status,
          total_amount,
          clients!contracts_client_id_fkey (
            id,
            first_name,
            last_name,
            middle_name,
            email,
            phone
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return null;
    }

    const building = data.buildings;
    const complex = building?.complexes;
    const contracts = data.contracts || [];
    const activeContract = contracts.find((contract: any) => contract.id);
    const client = activeContract?.clients;

    const apartment: Apartment = {
      ...data,
      building_name: building?.name,
      complex_name: complex?.name,
      is_sold: data.status === 'sold',
      contract_id: activeContract?.id,
      client_name: client ? `${client.last_name} ${client.first_name} ${client.middle_name || ''}`.trim() : undefined,
      building,
      complex,
    };

    // Удаляем вложенные объекты из корня
    delete (apartment as any).buildings;
    delete (apartment as any).contracts;

    return apartment;
  } catch (error) {
    console.error('Ошибка при получении квартиры:', error);
    throw error;
  }
}

/**
 * Создание новой квартиры
 *
 * @param supabase Клиент Supabase
 * @param apartmentData Данные для создания квартиры
 * @returns Созданная квартира
 */
export async function createApartment(
  supabase: SupabaseClient,
  apartmentData: ApartmentInsert
): Promise<Apartment> {
  try {
    const { data, error } = await supabase
      .from('apartments')
      .insert(apartmentData)
      .select(`
        *,
        buildings!apartments_building_id_fkey (
          id,
          name,
          complexes!buildings_complex_id_fkey (
            id,
            name
          )
        )
      `)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('Не удалось создать квартиру');
    }

    const building = data.buildings;
    const complex = building?.complexes;

    return {
      ...data,
      building_name: building?.name,
      complex_name: complex?.name,
      is_sold: data.status === 'sold',
    };
  } catch (error) {
    console.error('Ошибка при создании квартиры:', error);
    throw error;
  }
}

/**
 * Массовое создание квартир
 *
 * @param supabase Клиент Supabase
 * @param bulkData Данные для массового создания квартир
 * @returns Результат массового создания
 */
export async function createApartmentsBulk(
  supabase: SupabaseClient,
  bulkData: BulkApartmentCreate
): Promise<BulkApartmentResult> {
  try {
    const { building_id, apartments } = bulkData;

    // Получаем информацию о здании для tenant_id
    const { data: building, error: buildingError } = await supabase
      .from('buildings')
      .select('tenant_id')
      .eq('id', building_id)
      .single();

    if (buildingError || !building) {
      throw new Error('Здание не найдено');
    }

    // Подготавливаем данные для вставки
    const apartmentsToInsert = apartments.map(apartment => ({
      ...apartment,
      building_id,
      tenant_id: building.tenant_id,
    }));

    const { data, error } = await supabase
      .from('apartments')
      .insert(apartmentsToInsert)
      .select(`
        *,
        buildings!apartments_building_id_fkey (
          id,
          name,
          complexes!buildings_complex_id_fkey (
            id,
            name
          )
        )
      `);

    if (error) {
      throw error;
    }

    const created: Apartment[] = (data || []).map(apartment => {
      const building = apartment.buildings;
      const complex = building?.complexes;

      return {
        ...apartment,
        building_name: building?.name,
        complex_name: complex?.name,
        is_sold: apartment.status === 'sold',
      };
    });

    return {
      created,
      errors: [], // В случае успеха ошибок нет
    };
  } catch (error) {
    console.error('Ошибка при массовом создании квартир:', error);

    // Возвращаем ошибку для всех квартир
    return {
      created: [],
      errors: bulkData.apartments.map(apartment => ({
        apartment,
        error: error instanceof Error ? error.message : 'Неизвестная ошибка',
      })),
    };
  }
}

/**
 * Обновление квартиры
 *
 * @param supabase Клиент Supabase
 * @param id ID квартиры
 * @param updates Данные для обновления
 * @returns Обновленная квартира
 */
export async function updateApartment(
  supabase: SupabaseClient,
  id: string,
  updates: ApartmentUpdate
): Promise<Apartment> {
  try {
    const { data, error } = await supabase
      .from('apartments')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('Не удалось обновить квартиру');
    }

    // Получаем полную информацию о квартире
    return await getApartmentById(supabase, id) || data;
  } catch (error) {
    console.error('Ошибка при обновлении квартиры:', error);
    throw error;
  }
}

/**
 * Удаление квартиры
 *
 * @param supabase Клиент Supabase
 * @param id ID квартиры
 * @returns Результат удаления
 */
export async function deleteApartment(
  supabase: SupabaseClient,
  id: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('apartments')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при удалении квартиры:', error);
    throw error;
  }
}

/**
 * Изменение статуса квартиры
 *
 * @param supabase Клиент Supabase
 * @param id ID квартиры
 * @param status Новый статус
 * @returns Обновленная квартира
 */
export async function updateApartmentStatus(
  supabase: SupabaseClient,
  id: string,
  status: 'available' | 'reserved' | 'sold' | 'unavailable'
): Promise<Apartment> {
  try {
    return await updateApartment(supabase, id, { status });
  } catch (error) {
    console.error('Ошибка при изменении статуса квартиры:', error);
    throw error;
  }
}
