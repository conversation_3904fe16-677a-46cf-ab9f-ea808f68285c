/**
 * @file: buildings.ts
 * @description: API функции для работы со зданиями
 * @dependencies: @supabase/supabase-js, types/properties
 * @created: 2024-12-26
 */

import { SupabaseClient } from '@supabase/supabase-js';
import {
  Building,
  BuildingInsert,
  BuildingUpdate,
  BuildingFilters,
  PropertyPagination,
  BuildingSorting,
  BuildingsResult,
  BuildingStats,
} from '../types/properties';

/**
 * Получение списка зданий с фильтрацией, пагинацией и сортировкой
 *
 * @param supabase Клиент Supabase
 * @param filters Фильтры для зданий
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Результат запроса со зданиями и метаданными
 */
export async function getBuildings(
  supabase: SupabaseClient,
  filters?: BuildingFilters,
  pagination?: PropertyPagination,
  sorting?: BuildingSorting
): Promise<BuildingsResult> {
  try {
    // Базовый запрос с подсчетом квартир и информацией о комплексе
    let query = supabase
      .from('buildings')
      .select(`
        *,
        complexes!buildings_complex_id_fkey (
          id,
          name
        ),
        apartments!buildings_apartments_building_id_fkey (
          id,
          status,
          area,
          price
        )
      `, { count: 'exact' });

    // Применение фильтров
    if (filters) {
      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,address.ilike.%${filters.search}%`);
      }

      if (filters.complex_id) {
        query = query.eq('complex_id', filters.complex_id);
      }

      if (filters.status) {
        if (Array.isArray(filters.status)) {
          query = query.in('status', filters.status);
        } else {
          query = query.eq('status', filters.status);
        }
      }

      if (filters.min_floors !== undefined) {
        query = query.gte('floors', filters.min_floors);
      }

      if (filters.max_floors !== undefined) {
        query = query.lte('floors', filters.max_floors);
      }

      if (filters.created_after) {
        query = query.gte('created_at', filters.created_after);
      }

      if (filters.created_before) {
        query = query.lte('created_at', filters.created_before);
      }
    }

    // Применение сортировки
    if (sorting?.field && sorting?.order) {
      query = query.order(sorting.field, { ascending: sorting.order === 'asc' });
    } else {
      // Сортировка по умолчанию
      query = query.order('created_at', { ascending: false });
    }

    // Применение пагинации
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 20;
    const offset = pagination?.offset || (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    if (!data) {
      return {
        data: [],
        count: 0,
        page,
        limit,
        total_pages: 0,
      };
    }

    // Обработка данных и добавление вычисляемых полей
    const buildings: Building[] = data.map((building) => {
      const apartments = building.apartments || [];
      const complex = building.complexes;

      const apartments_count = apartments.length;
      const available_apartments = apartments.filter((apt: any) => apt.status === 'available').length;
      const sold_apartments = apartments.filter((apt: any) => apt.status === 'sold').length;
      const total_area = apartments.reduce((sum: number, apt: any) => sum + (apt.area || 0), 0);
      const average_price = apartments_count > 0
        ? apartments.reduce((sum: number, apt: any) => sum + (apt.price || 0), 0) / apartments_count
        : 0;

      // Удаляем вложенные объекты для чистоты ответа
      const { apartments: _, complexes: __, ...buildingData } = building;

      return {
        ...buildingData,
        complex_name: complex?.name,
        apartments_count,
        available_apartments,
        sold_apartments,
        total_area,
        average_price,
      };
    });

    // Применение дополнительных фильтров после обработки данных
    let filteredBuildings = buildings;

    if (filters) {
      if (filters.min_apartments !== undefined) {
        filteredBuildings = filteredBuildings.filter(b => (b.apartments_count || 0) >= filters.min_apartments!);
      }

      if (filters.max_apartments !== undefined) {
        filteredBuildings = filteredBuildings.filter(b => (b.apartments_count || 0) <= filters.max_apartments!);
      }
    }

    const total_pages = Math.ceil((count || 0) / limit);

    return {
      data: filteredBuildings,
      count: count || 0,
      page,
      limit,
      total_pages,
    };
  } catch (error) {
    console.error('Ошибка при получении зданий:', error);
    throw error;
  }
}

/**
 * Получение здания по ID
 *
 * @param supabase Клиент Supabase
 * @param id ID здания
 * @returns Здание с дополнительной информацией
 */
export async function getBuildingById(
  supabase: SupabaseClient,
  id: string
): Promise<Building | null> {
  try {
    const { data, error } = await supabase
      .from('buildings')
      .select(`
        *,
        complexes!buildings_complex_id_fkey (
          id,
          name,
          address,
          description,
          status
        ),
        apartments!buildings_apartments_building_id_fkey (
          id,
          number,
          floor,
          rooms,
          area,
          price,
          status,
          created_at
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return null;
    }

    const apartments = data.apartments || [];
    const complex = data.complexes;

    const apartments_count = apartments.length;
    const available_apartments = apartments.filter((apt: any) => apt.status === 'available').length;
    const sold_apartments = apartments.filter((apt: any) => apt.status === 'sold').length;
    const total_area = apartments.reduce((sum: number, apt: any) => sum + (apt.area || 0), 0);
    const average_price = apartments_count > 0
      ? apartments.reduce((sum: number, apt: any) => sum + (apt.price || 0), 0) / apartments_count
      : 0;

    const building: Building = {
      ...data,
      complex_name: complex?.name,
      apartments_count,
      available_apartments,
      sold_apartments,
      total_area,
      average_price,
      apartments: apartments.map((apartment: any) => ({
        ...apartment,
        building_name: data.name,
        complex_name: complex?.name,
      })),
      complex,
    };

    // Удаляем вложенные объекты из корня
    delete (building as any).complexes;

    return building;
  } catch (error) {
    console.error('Ошибка при получении здания:', error);
    throw error;
  }
}

/**
 * Создание нового здания
 *
 * @param supabase Клиент Supabase
 * @param buildingData Данные для создания здания
 * @returns Созданное здание
 */
export async function createBuilding(
  supabase: SupabaseClient,
  buildingData: BuildingInsert
): Promise<Building> {
  try {
    const { data, error } = await supabase
      .from('buildings')
      .insert(buildingData)
      .select(`
        *,
        complexes!buildings_complex_id_fkey (
          id,
          name
        )
      `)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('Не удалось создать здание');
    }

    const complex = data.complexes;

    return {
      ...data,
      complex_name: complex?.name,
      apartments_count: 0,
      available_apartments: 0,
      sold_apartments: 0,
      total_area: 0,
      average_price: 0,
    };
  } catch (error) {
    console.error('Ошибка при создании здания:', error);
    throw error;
  }
}

/**
 * Обновление здания
 *
 * @param supabase Клиент Supabase
 * @param id ID здания
 * @param updates Данные для обновления
 * @returns Обновленное здание
 */
export async function updateBuilding(
  supabase: SupabaseClient,
  id: string,
  updates: BuildingUpdate
): Promise<Building> {
  try {
    const { data, error } = await supabase
      .from('buildings')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('Не удалось обновить здание');
    }

    // Получаем полную информацию о здании
    return await getBuildingById(supabase, id) || data;
  } catch (error) {
    console.error('Ошибка при обновлении здания:', error);
    throw error;
  }
}

/**
 * Удаление здания
 *
 * @param supabase Клиент Supabase
 * @param id ID здания
 * @returns Результат удаления
 */
export async function deleteBuilding(
  supabase: SupabaseClient,
  id: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('buildings')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при удалении здания:', error);
    throw error;
  }
}

/**
 * Получение статистики по зданию
 *
 * @param supabase Клиент Supabase
 * @param id ID здания
 * @returns Статистика по зданию
 */
export async function getBuildingStats(
  supabase: SupabaseClient,
  id: string
): Promise<BuildingStats> {
  try {
    const building = await getBuildingById(supabase, id);

    if (!building) {
      throw new Error('Здание не найдено');
    }

    const apartments = building.apartments || [];

    const total_apartments = apartments.length;
    const available_apartments = apartments.filter(apt => apt.status === 'available').length;
    const reserved_apartments = apartments.filter(apt => apt.status === 'reserved').length;
    const sold_apartments = apartments.filter(apt => apt.status === 'sold').length;
    const total_area = apartments.reduce((sum, apt) => sum + (apt.area || 0), 0);

    const prices = apartments.map(apt => apt.price || 0).filter(price => price > 0);
    const average_price = prices.length > 0 ? prices.reduce((sum, price) => sum + price, 0) / prices.length : 0;
    const min_price = prices.length > 0 ? Math.min(...prices) : 0;
    const max_price = prices.length > 0 ? Math.max(...prices) : 0;

    // Примерный расчет завершенных этажей (можно улучшить)
    const floors_completed = building.floors || 0;

    return {
      total_apartments,
      available_apartments,
      reserved_apartments,
      sold_apartments,
      total_area,
      average_price,
      min_price,
      max_price,
      floors_completed,
    };
  } catch (error) {
    console.error('Ошибка при получении статистики здания:', error);
    throw error;
  }
}
