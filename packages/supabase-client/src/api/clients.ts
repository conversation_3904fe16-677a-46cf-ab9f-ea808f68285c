/**
 * @file: clients.ts
 * @description: API функции для работы с клиентами
 * @dependencies: @supabase/supabase-js, types/clients
 * @created: 2024-12-26
 */

import { SupabaseClient } from '@supabase/supabase-js';
import {
  Client,
  ClientInsert,
  ClientUpdate,
  ClientFilters,
  ClientPagination,
  ClientSorting,
  ClientsResult,
  ClientStats,
  ClientsOverallStats,
  ClientCommunication,
  ClientCommunicationInsert,
  ClientNote,
  ClientNoteInsert,
  ClientDocument,
  ClientDocumentInsert,
  ClientRiskAnalysis,
  BulkClientUpdate,
  BulkClientUpdateResult,
} from '../types/clients';

/**
 * Получение списка клиентов с фильтрацией, пагинацией и сортировкой
 * 
 * @param supabase Клиент Supabase
 * @param filters Фильтры для клиентов
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Результат запроса с клиентами и метаданными
 */
export async function getClients(
  supabase: SupabaseClient,
  filters?: ClientFilters,
  pagination?: ClientPagination,
  sorting?: ClientSorting
): Promise<ClientsResult> {
  try {
    // Базовый запрос с подсчетом договоров
    let query = supabase
      .from('clients')
      .select(`
        *,
        contracts!clients_contracts_client_id_fkey (
          id,
          number,
          total_amount,
          status,
          signed_date
        )
      `, { count: 'exact' });

    // Применение фильтров
    if (filters) {
      if (filters.search) {
        query = query.or(`first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%,middle_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,phone.ilike.%${filters.search}%`);
      }

      if (filters.registration_date_from) {
        query = query.gte('created_at', filters.registration_date_from);
      }

      if (filters.registration_date_to) {
        query = query.lte('created_at', filters.registration_date_to);
      }
    }

    // Применение сортировки
    if (sorting?.field && sorting?.order) {
      query = query.order(sorting.field, { ascending: sorting.order === 'asc' });
    } else {
      // Сортировка по умолчанию
      query = query.order('created_at', { ascending: false });
    }

    // Применение пагинации
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 20;
    const offset = pagination?.offset || (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    if (!data) {
      return {
        data: [],
        count: 0,
        page,
        limit,
        total_pages: 0,
      };
    }

    // Обработка данных и добавление вычисляемых полей
    const clients: Client[] = data.map((client) => {
      const contracts = client.contracts || [];
      const activeContracts = contracts.filter(contract => contract.status === 'active');
      
      const full_name = `${client.last_name} ${client.first_name} ${client.middle_name || ''}`.trim();
      const contracts_count = contracts.length;
      const active_contracts = activeContracts.length;
      const total_contract_amount = contracts.reduce((sum, contract) => sum + (contract.total_amount || 0), 0);
      
      // Определяем статус клиента на основе договоров
      let status = 'potential';
      if (active_contracts > 0) {
        status = 'active';
      } else if (contracts_count > 0) {
        status = 'inactive';
      }

      // Удаляем вложенные объекты для чистоты ответа
      const { contracts: _, ...clientData } = client;

      return {
        ...clientData,
        full_name,
        contracts_count,
        active_contracts,
        total_contract_amount,
        status: status as any,
      };
    });

    // Применение дополнительных фильтров после обработки данных
    let filteredClients = clients;

    if (filters) {
      if (filters.status) {
        const statusArray = Array.isArray(filters.status) ? filters.status : [filters.status];
        filteredClients = filteredClients.filter(c => statusArray.includes(c.status as any));
      }

      if (filters.has_contracts !== undefined) {
        filteredClients = filteredClients.filter(c => 
          filters.has_contracts ? (c.contracts_count || 0) > 0 : (c.contracts_count || 0) === 0
        );
      }

      if (filters.has_active_contracts !== undefined) {
        filteredClients = filteredClients.filter(c => 
          filters.has_active_contracts ? (c.active_contracts || 0) > 0 : (c.active_contracts || 0) === 0
        );
      }

      if (filters.min_contract_amount !== undefined) {
        filteredClients = filteredClients.filter(c => (c.total_contract_amount || 0) >= filters.min_contract_amount!);
      }

      if (filters.max_contract_amount !== undefined) {
        filteredClients = filteredClients.filter(c => (c.total_contract_amount || 0) <= filters.max_contract_amount!);
      }
    }

    const total_pages = Math.ceil((count || 0) / limit);

    return {
      data: filteredClients,
      count: count || 0,
      page,
      limit,
      total_pages,
    };
  } catch (error) {
    console.error('Ошибка при получении клиентов:', error);
    throw error;
  }
}

/**
 * Получение клиента по ID
 * 
 * @param supabase Клиент Supabase
 * @param id ID клиента
 * @returns Клиент с дополнительной информацией
 */
export async function getClientById(
  supabase: SupabaseClient,
  id: string
): Promise<Client | null> {
  try {
    const { data, error } = await supabase
      .from('clients')
      .select(`
        *,
        contracts!clients_contracts_client_id_fkey (
          id,
          number,
          signed_date,
          start_date,
          end_date,
          total_amount,
          initial_payment,
          monthly_payment,
          status,
          created_at,
          apartments!contracts_apartment_id_fkey (
            id,
            number,
            floor,
            rooms,
            area,
            price,
            buildings!apartments_building_id_fkey (
              id,
              name,
              complexes!buildings_complex_id_fkey (
                id,
                name
              )
            )
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return null;
    }

    const contracts = data.contracts || [];
    const activeContracts = contracts.filter(contract => contract.status === 'active');
    
    const full_name = `${data.last_name} ${data.first_name} ${data.middle_name || ''}`.trim();
    const contracts_count = contracts.length;
    const active_contracts = activeContracts.length;
    const total_contract_amount = contracts.reduce((sum, contract) => sum + (contract.total_amount || 0), 0);
    
    // Определяем статус клиента
    let status = 'potential';
    if (active_contracts > 0) {
      status = 'active';
    } else if (contracts_count > 0) {
      status = 'inactive';
    }

    const client: Client = {
      ...data,
      full_name,
      contracts_count,
      active_contracts,
      total_contract_amount,
      status: status as any,
      contracts: contracts.map(contract => ({
        ...contract,
        apartment_name: contract.apartments?.number,
        building_name: contract.apartments?.buildings?.name,
        complex_name: contract.apartments?.buildings?.complexes?.name,
      })),
    };

    return client;
  } catch (error) {
    console.error('Ошибка при получении клиента:', error);
    throw error;
  }
}

/**
 * Создание нового клиента
 * 
 * @param supabase Клиент Supabase
 * @param clientData Данные для создания клиента
 * @returns Созданный клиент
 */
export async function createClient(
  supabase: SupabaseClient,
  clientData: ClientInsert
): Promise<Client> {
  try {
    const { data, error } = await supabase
      .from('clients')
      .insert(clientData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('Не удалось создать клиента');
    }

    const full_name = `${data.last_name} ${data.first_name} ${data.middle_name || ''}`.trim();

    return {
      ...data,
      full_name,
      contracts_count: 0,
      active_contracts: 0,
      total_contract_amount: 0,
      status: 'potential' as any,
    };
  } catch (error) {
    console.error('Ошибка при создании клиента:', error);
    throw error;
  }
}

/**
 * Обновление клиента
 * 
 * @param supabase Клиент Supabase
 * @param id ID клиента
 * @param updates Данные для обновления
 * @returns Обновленный клиент
 */
export async function updateClient(
  supabase: SupabaseClient,
  id: string,
  updates: ClientUpdate
): Promise<Client> {
  try {
    const { data, error } = await supabase
      .from('clients')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('Не удалось обновить клиента');
    }

    // Получаем полную информацию о клиенте
    return await getClientById(supabase, id) || data;
  } catch (error) {
    console.error('Ошибка при обновлении клиента:', error);
    throw error;
  }
}

/**
 * Удаление клиента
 * 
 * @param supabase Клиент Supabase
 * @param id ID клиента
 * @returns Результат удаления
 */
export async function deleteClient(
  supabase: SupabaseClient,
  id: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при удалении клиента:', error);
    throw error;
  }
}

/**
 * Получение статистики по клиенту
 * 
 * @param supabase Клиент Supabase
 * @param id ID клиента
 * @returns Статистика по клиенту
 */
export async function getClientStats(
  supabase: SupabaseClient,
  id: string
): Promise<ClientStats> {
  try {
    const client = await getClientById(supabase, id);
    
    if (!client) {
      throw new Error('Клиент не найден');
    }

    const contracts = client.contracts || [];
    
    const total_contracts = contracts.length;
    const active_contracts = contracts.filter(c => c.status === 'active').length;
    const completed_contracts = contracts.filter(c => c.status === 'completed').length;
    const cancelled_contracts = contracts.filter(c => c.status === 'cancelled').length;
    const total_contract_amount = contracts.reduce((sum, c) => sum + (c.total_amount || 0), 0);
    
    // TODO: Получить данные о платежах
    const paid_amount = 0;
    const outstanding_amount = total_contract_amount - paid_amount;
    
    // TODO: Получить данные о коммуникациях
    const communication_count = 0;
    
    // TODO: Реализовать расчет риск-скора
    const risk_score = Math.floor(Math.random() * 100); // Временная заглушка

    return {
      total_contracts,
      active_contracts,
      completed_contracts,
      cancelled_contracts,
      total_contract_amount,
      paid_amount,
      outstanding_amount,
      risk_score,
      communication_count,
    };
  } catch (error) {
    console.error('Ошибка при получении статистики клиента:', error);
    throw error;
  }
}

/**
 * Получение общей статистики по клиентам
 * 
 * @param supabase Клиент Supabase
 * @param tenantId ID арендатора
 * @returns Общая статистика по клиентам
 */
export async function getClientsOverallStats(
  supabase: SupabaseClient,
  tenantId: string
): Promise<ClientsOverallStats> {
  try {
    // Получаем всех клиентов с договорами
    const { data: clients, error } = await supabase
      .from('clients')
      .select(`
        id,
        created_at,
        contracts!clients_contracts_client_id_fkey (
          id,
          total_amount,
          status
        )
      `)
      .eq('tenant_id', tenantId);

    if (error) {
      throw error;
    }

    if (!clients) {
      return {
        total_clients: 0,
        active_clients: 0,
        potential_clients: 0,
        blocked_clients: 0,
        clients_with_contracts: 0,
        average_contract_amount: 0,
        total_contract_amount: 0,
        high_risk_clients: 0,
        new_clients_this_month: 0,
      };
    }

    const total_clients = clients.length;
    let active_clients = 0;
    let potential_clients = 0;
    let clients_with_contracts = 0;
    let total_contract_amount = 0;
    let contract_count = 0;

    // Дата начала текущего месяца
    const currentMonth = new Date();
    currentMonth.setDate(1);
    currentMonth.setHours(0, 0, 0, 0);

    let new_clients_this_month = 0;

    clients.forEach(client => {
      const contracts = client.contracts || [];
      const activeContracts = contracts.filter(c => c.status === 'active');
      
      if (contracts.length > 0) {
        clients_with_contracts++;
        
        if (activeContracts.length > 0) {
          active_clients++;
        } else {
          potential_clients++;
        }
        
        contracts.forEach(contract => {
          total_contract_amount += contract.total_amount || 0;
          contract_count++;
        });
      } else {
        potential_clients++;
      }

      // Проверяем новых клиентов за текущий месяц
      if (new Date(client.created_at) >= currentMonth) {
        new_clients_this_month++;
      }
    });

    const average_contract_amount = contract_count > 0 ? total_contract_amount / contract_count : 0;
    
    // TODO: Реализовать подсчет клиентов с высоким риском
    const high_risk_clients = 0;
    
    // TODO: Реализовать подсчет заблокированных клиентов
    const blocked_clients = 0;

    return {
      total_clients,
      active_clients,
      potential_clients,
      blocked_clients,
      clients_with_contracts,
      average_contract_amount,
      total_contract_amount,
      high_risk_clients,
      new_clients_this_month,
    };
  } catch (error) {
    console.error('Ошибка при получении общей статистики клиентов:', error);
    throw error;
  }
}
