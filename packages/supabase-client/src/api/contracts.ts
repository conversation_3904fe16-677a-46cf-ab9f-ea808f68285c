/**
 * @file: contracts.ts
 * @description: API функции для работы с договорами
 * @dependencies: @supabase/supabase-js
 * @created: 2024-05-10
 */

import { SupabaseClient } from '@supabase/supabase-js';
import {
  Contract,
  ContractInsert,
  ContractUpdate,
  ContractFilters,
  ContractPagination,
  ContractSorting,
  ContractsResult
} from '../types/contracts';

/**
 * Получение списка договоров с фильтрацией, пагинацией и сортировкой
 *
 * @param supabase Клиент Supabase
 * @param filters Фильтры для договоров
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Результат запроса с договорами и метаданными
 */
export async function getContracts(
  supabase: SupabaseClient,
  filters?: ContractFilters,
  pagination?: ContractPagination,
  sorting?: ContractSorting
): Promise<ContractsResult> {
  try {
    // Базовый запрос с джойнами для получения дополнительной информации
    let query = supabase
      .from('contracts')
      .select(`
        *,
        clients!contracts_client_id_fkey (
          id,
          first_name,
          last_name,
          middle_name
        ),
        apartments!contracts_apartment_id_fkey (
          id,
          number,
          buildings!apartments_building_id_fkey (
            id,
            name,
            complexes!buildings_complex_id_fkey (
              id,
              name
            )
          )
        )
      `);

    // Применяем фильтры
    if (filters) {
      if (filters.client_id) {
        query = query.eq('client_id', filters.client_id);
      }

      if (filters.apartment_id) {
        query = query.eq('apartment_id', filters.apartment_id);
      }

      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.search) {
        query = query.or(`number.ilike.%${filters.search}%, clients.first_name.ilike.%${filters.search}%, clients.last_name.ilike.%${filters.search}%`);
      }

      if (filters.start_date) {
        query = query.gte('signed_date', filters.start_date);
      }

      if (filters.end_date) {
        query = query.lte('signed_date', filters.end_date);
      }
    }

    // Получаем общее количество записей для пагинации
    const { count } = await supabase
      .from('contracts')
      .select('*', { count: 'exact', head: true });

    // Применяем сортировку
    if (sorting) {
      query = query.order(sorting.column, { ascending: sorting.direction === 'asc' });
    } else {
      // По умолчанию сортируем по дате создания (от новых к старым)
      query = query.order('created_at', { ascending: false });
    }

    // Применяем пагинацию
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 10;
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query.range(from, to);

    // Выполняем запрос
    const { data, error } = await query;

    if (error) {
      throw error;
    }

    // Преобразуем данные в нужный формат
    const contracts = data.map(item => {
      const client = item.clients;
      const apartment = item.apartments;
      const building = apartment?.buildings;
      const complex = building?.complexes;

      const contract: Contract = {
        ...item,
        client_name: client ? `${client.last_name} ${client.first_name} ${client.middle_name || ''}`.trim() : undefined,
        apartment_number: apartment?.number,
        building_name: building?.name,
        complex_name: complex?.name
      };

      // Удаляем вложенные объекты, так как мы уже извлекли нужные данные
      delete (contract as any).clients;
      delete (contract as any).apartments;

      return contract;
    });

    return {
      data: contracts,
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  } catch (error) {
    console.error('Ошибка при получении списка договоров:', error);
    throw error;
  }
}

/**
 * Получение договора по ID
 *
 * @param supabase Клиент Supabase
 * @param id ID договора
 * @returns Договор с дополнительной информацией
 */
export async function getContractById(
  supabase: SupabaseClient,
  id: string
): Promise<Contract | null> {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .select(`
        *,
        clients!contracts_client_id_fkey (
          id,
          first_name,
          last_name,
          middle_name,
          email,
          phone
        ),
        apartments!contracts_apartment_id_fkey (
          id,
          number,
          floor,
          rooms,
          area,
          price,
          buildings!apartments_building_id_fkey (
            id,
            name,
            complexes!buildings_complex_id_fkey (
              id,
              name
            )
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return null;
    }

    const client = data.clients;
    const apartment = data.apartments;
    const building = apartment?.buildings;
    const complex = building?.complexes;

    const contract: Contract = {
      ...data,
      client_name: client ? `${client.last_name} ${client.first_name} ${client.middle_name || ''}`.trim() : undefined,
      apartment_number: apartment?.number,
      building_name: building?.name,
      complex_name: complex?.name
    };

    // Удаляем вложенные объекты, так как мы уже извлекли нужные данные
    delete (contract as any).clients;
    delete (contract as any).apartments;

    return contract;
  } catch (error) {
    console.error('Ошибка при получении договора:', error);
    throw error;
  }
}

/**
 * Создание нового договора
 *
 * @param supabase Клиент Supabase
 * @param contract Данные нового договора
 * @returns Созданный договор
 */
export async function createContract(
  supabase: SupabaseClient,
  contract: ContractInsert
): Promise<Contract> {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .insert(contract)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data as Contract;
  } catch (error) {
    console.error('Ошибка при создании договора:', error);
    throw error;
  }
}

/**
 * Обновление договора
 *
 * @param supabase Клиент Supabase
 * @param id ID договора
 * @param contract Данные для обновления
 * @returns Обновленный договор
 */
export async function updateContract(
  supabase: SupabaseClient,
  id: string,
  contract: ContractUpdate
): Promise<Contract> {
  try {
    const { data, error } = await supabase
      .from('contracts')
      .update(contract)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data as Contract;
  } catch (error) {
    console.error('Ошибка при обновлении договора:', error);
    throw error;
  }
}

/**
 * Удаление договора
 *
 * @param supabase Клиент Supabase
 * @param id ID договора
 * @returns Успешность операции
 */
export async function deleteContract(
  supabase: SupabaseClient,
  id: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('contracts')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при удалении договора:', error);
    throw error;
  }
}
