/**
 * @file: contract-template-versions.ts
 * @description: API функции для работы с версиями шаблонов договоров
 * @dependencies: @supabase/supabase-js
 * @created: 2024-05-10
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { 
  ContractTemplateVersion, 
  ContractTemplateVersionInsert, 
  ContractTemplateVersionUpdate, 
  ContractTemplateVersionsResult,
  DbContractTemplateFile,
  ContractTemplateFileInsert
} from '../types/contract-templates';

/**
 * Получение списка версий шаблона договора
 * 
 * @param supabase Клиент Supabase
 * @param templateId ID шаблона
 * @param page Номер страницы
 * @param limit Количество записей на странице
 * @returns Результат запроса с версиями и метаданными
 */
export async function getContractTemplateVersions(
  supabase: SupabaseClient,
  templateId: string,
  page: number = 1,
  limit: number = 10
): Promise<ContractTemplateVersionsResult> {
  try {
    // Базовый запрос
    let query = supabase
      .from('contract_template_versions')
      .select(`
        *,
        contract_templates!contract_template_versions_template_id_fkey (
          id,
          name
        )
      `)
      .eq('template_id', templateId);

    // Получаем общее количество записей для пагинации
    const { count } = await supabase
      .from('contract_template_versions')
      .select('*', { count: 'exact', head: true })
      .eq('template_id', templateId);

    // По умолчанию сортируем по номеру версии (от новых к старым)
    query = query.order('version_number', { ascending: false });

    // Применяем пагинацию
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    
    query = query.range(from, to);

    // Выполняем запрос
    const { data, error } = await query;

    if (error) {
      throw error;
    }

    // Преобразуем данные в нужный формат
    const versions = await Promise.all(data.map(async (item) => {
      const template = item.contract_templates;
      
      // Получаем файлы для версии
      const { data: files, error: filesError } = await supabase
        .from('contract_template_files')
        .select('*')
        .eq('template_version_id', item.id);
      
      if (filesError) {
        throw filesError;
      }
      
      const version: ContractTemplateVersion = {
        ...item,
        template_name: template?.name,
        files: files
      };

      // Удаляем вложенные объекты, так как мы уже извлекли нужные данные
      delete version.contract_templates;

      return version;
    }));

    return {
      data: versions,
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  } catch (error) {
    console.error('Ошибка при получении списка версий шаблона договора:', error);
    throw error;
  }
}

/**
 * Получение версии шаблона договора по ID
 * 
 * @param supabase Клиент Supabase
 * @param id ID версии шаблона
 * @returns Версия шаблона с дополнительной информацией
 */
export async function getContractTemplateVersionById(
  supabase: SupabaseClient,
  id: string
): Promise<ContractTemplateVersion | null> {
  try {
    const { data, error } = await supabase
      .from('contract_template_versions')
      .select(`
        *,
        contract_templates!contract_template_versions_template_id_fkey (
          id,
          name
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return null;
    }

    // Получаем файлы для версии
    const { data: files, error: filesError } = await supabase
      .from('contract_template_files')
      .select('*')
      .eq('template_version_id', id);
    
    if (filesError) {
      throw filesError;
    }

    const template = data.contract_templates;
    
    const version: ContractTemplateVersion = {
      ...data,
      template_name: template?.name,
      files: files
    };

    // Удаляем вложенные объекты, так как мы уже извлекли нужные данные
    delete version.contract_templates;

    return version;
  } catch (error) {
    console.error('Ошибка при получении версии шаблона договора:', error);
    throw error;
  }
}

/**
 * Создание новой версии шаблона договора
 * 
 * @param supabase Клиент Supabase
 * @param version Данные новой версии шаблона
 * @returns Созданная версия шаблона
 */
export async function createContractTemplateVersion(
  supabase: SupabaseClient,
  version: ContractTemplateVersionInsert
): Promise<ContractTemplateVersion> {
  try {
    // Получаем последний номер версии для шаблона
    const { data: latestVersion, error: latestVersionError } = await supabase
      .from('contract_template_versions')
      .select('version_number')
      .eq('template_id', version.template_id)
      .order('version_number', { ascending: false })
      .limit(1)
      .single();

    if (latestVersionError && latestVersionError.code !== 'PGRST116') {
      throw latestVersionError;
    }

    // Устанавливаем номер версии
    const versionNumber = latestVersion ? latestVersion.version_number + 1 : 1;
    const versionData = { ...version, version_number: versionNumber };

    // Создаем новую версию
    const { data, error } = await supabase
      .from('contract_template_versions')
      .insert(versionData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data as ContractTemplateVersion;
  } catch (error) {
    console.error('Ошибка при создании версии шаблона договора:', error);
    throw error;
  }
}

/**
 * Обновление версии шаблона договора
 * 
 * @param supabase Клиент Supabase
 * @param id ID версии шаблона
 * @param version Данные для обновления
 * @returns Обновленная версия шаблона
 */
export async function updateContractTemplateVersion(
  supabase: SupabaseClient,
  id: string,
  version: ContractTemplateVersionUpdate
): Promise<ContractTemplateVersion> {
  try {
    const { data, error } = await supabase
      .from('contract_template_versions')
      .update(version)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data as ContractTemplateVersion;
  } catch (error) {
    console.error('Ошибка при обновлении версии шаблона договора:', error);
    throw error;
  }
}

/**
 * Удаление версии шаблона договора
 * 
 * @param supabase Клиент Supabase
 * @param id ID версии шаблона
 * @returns Успешность операции
 */
export async function deleteContractTemplateVersion(
  supabase: SupabaseClient,
  id: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('contract_template_versions')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при удалении версии шаблона договора:', error);
    throw error;
  }
}

/**
 * Загрузка файла для версии шаблона договора
 * 
 * @param supabase Клиент Supabase
 * @param versionId ID версии шаблона
 * @param tenantId ID арендатора
 * @param file Файл для загрузки
 * @returns Информация о загруженном файле
 */
export async function uploadContractTemplateFile(
  supabase: SupabaseClient,
  versionId: string,
  tenantId: string,
  file: File
): Promise<DbContractTemplateFile> {
  try {
    // Загружаем файл в хранилище
    const filePath = `templates/${tenantId}/${versionId}/${file.name}`;
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('contracts')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (uploadError) {
      throw uploadError;
    }

    // Получаем публичную ссылку на файл
    const { data: urlData } = await supabase.storage
      .from('contracts')
      .getPublicUrl(filePath);

    // Создаем запись о файле в базе данных
    const fileData: ContractTemplateFileInsert = {
      template_version_id: versionId,
      tenant_id: tenantId,
      file_name: file.name,
      file_path: urlData.publicUrl,
      file_type: file.type,
      file_size: file.size
    };

    const { data, error } = await supabase
      .from('contract_template_files')
      .insert(fileData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Ошибка при загрузке файла для версии шаблона договора:', error);
    throw error;
  }
}

/**
 * Удаление файла версии шаблона договора
 * 
 * @param supabase Клиент Supabase
 * @param fileId ID файла
 * @returns Успешность операции
 */
export async function deleteContractTemplateFile(
  supabase: SupabaseClient,
  fileId: string
): Promise<boolean> {
  try {
    // Получаем информацию о файле
    const { data: fileData, error: fileError } = await supabase
      .from('contract_template_files')
      .select('*')
      .eq('id', fileId)
      .single();

    if (fileError) {
      throw fileError;
    }

    // Удаляем файл из хранилища
    const filePath = fileData.file_path.split('/').slice(-3).join('/');
    const { error: storageError } = await supabase.storage
      .from('contracts')
      .remove([filePath]);

    if (storageError) {
      console.warn('Ошибка при удалении файла из хранилища:', storageError);
    }

    // Удаляем запись о файле из базы данных
    const { error } = await supabase
      .from('contract_template_files')
      .delete()
      .eq('id', fileId);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при удалении файла версии шаблона договора:', error);
    throw error;
  }
}
