/**
 * @file: contract-templates.ts
 * @description: API функции для работы с шаблонами договоров
 * @dependencies: @supabase/supabase-js
 * @created: 2024-05-10
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { 
  ContractTemplate, 
  ContractTemplateInsert, 
  ContractTemplateUpdate, 
  ContractTemplateFilters, 
  ContractTemplatePagination, 
  ContractTemplateSorting,
  ContractTemplatesResult,
  ContractTemplateVersion,
  ContractTemplateVersionInsert,
  ContractTemplateVersionUpdate,
  ContractTemplateVersionsResult,
  ContractDocument,
  ContractDocumentInsert,
  ContractDocumentsResult,
  DbContractTemplateType
} from '../types/contract-templates';

/**
 * Получение списка шаблонов договоров с фильтрацией, пагинацией и сортировкой
 * 
 * @param supabase Клиент Supabase
 * @param filters Фильтры для шаблонов
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Результат запроса с шаблонами и метаданными
 */
export async function getContractTemplates(
  supabase: SupabaseClient,
  filters?: ContractTemplateFilters,
  pagination?: ContractTemplatePagination,
  sorting?: ContractTemplateSorting
): Promise<ContractTemplatesResult> {
  try {
    // Базовый запрос с джойнами для получения дополнительной информации
    let query = supabase
      .from('contract_templates')
      .select(`
        *,
        contract_template_types!contract_templates_type_id_fkey (
          id,
          name
        ),
        contract_template_versions!inner (
          id,
          version_number,
          is_active
        )
      `);

    // Применяем фильтры
    if (filters) {
      if (filters.type_id) {
        query = query.eq('type_id', filters.type_id);
      }
      
      if (filters.is_active !== undefined) {
        query = query.eq('is_active', filters.is_active);
      }
      
      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%, description.ilike.%${filters.search}%`);
      }
    }

    // Получаем общее количество записей для пагинации
    const { count } = await supabase
      .from('contract_templates')
      .select('*', { count: 'exact', head: true });

    // Применяем сортировку
    if (sorting) {
      query = query.order(sorting.column, { ascending: sorting.direction === 'asc' });
    } else {
      // По умолчанию сортируем по дате создания (от новых к старым)
      query = query.order('created_at', { ascending: false });
    }

    // Применяем пагинацию
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 10;
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    
    query = query.range(from, to);

    // Выполняем запрос
    const { data, error } = await query;

    if (error) {
      throw error;
    }

    // Преобразуем данные в нужный формат
    const templates = data.map(item => {
      const type = item.contract_template_types;
      const versions = Array.isArray(item.contract_template_versions) ? item.contract_template_versions : [];
      
      // Находим последнюю версию и количество версий
      const latestVersion = versions.length > 0 
        ? Math.max(...versions.map(v => v.version_number)) 
        : 0;
      
      const template: ContractTemplate = {
        ...item,
        type_name: type?.name,
        latest_version: latestVersion,
        versions_count: versions.length
      };

      // Удаляем вложенные объекты, так как мы уже извлекли нужные данные
      delete template.contract_template_types;
      delete template.contract_template_versions;

      return template;
    });

    return {
      data: templates,
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  } catch (error) {
    console.error('Ошибка при получении списка шаблонов договоров:', error);
    throw error;
  }
}

/**
 * Получение шаблона договора по ID
 * 
 * @param supabase Клиент Supabase
 * @param id ID шаблона
 * @returns Шаблон с дополнительной информацией
 */
export async function getContractTemplateById(
  supabase: SupabaseClient,
  id: string
): Promise<ContractTemplate | null> {
  try {
    const { data, error } = await supabase
      .from('contract_templates')
      .select(`
        *,
        contract_template_types!contract_templates_type_id_fkey (
          id,
          name
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return null;
    }

    // Получаем версии шаблона
    const { data: versions, error: versionsError } = await supabase
      .from('contract_template_versions')
      .select('id, version_number, is_active')
      .eq('template_id', id)
      .order('version_number', { ascending: false });

    if (versionsError) {
      throw versionsError;
    }

    const type = data.contract_template_types;
    
    const template: ContractTemplate = {
      ...data,
      type_name: type?.name,
      latest_version: versions.length > 0 ? versions[0].version_number : 0,
      versions_count: versions.length
    };

    // Удаляем вложенные объекты, так как мы уже извлекли нужные данные
    delete template.contract_template_types;

    return template;
  } catch (error) {
    console.error('Ошибка при получении шаблона договора:', error);
    throw error;
  }
}

/**
 * Создание нового шаблона договора
 * 
 * @param supabase Клиент Supabase
 * @param template Данные нового шаблона
 * @returns Созданный шаблон
 */
export async function createContractTemplate(
  supabase: SupabaseClient,
  template: ContractTemplateInsert
): Promise<ContractTemplate> {
  try {
    const { data, error } = await supabase
      .from('contract_templates')
      .insert(template)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data as ContractTemplate;
  } catch (error) {
    console.error('Ошибка при создании шаблона договора:', error);
    throw error;
  }
}

/**
 * Обновление шаблона договора
 * 
 * @param supabase Клиент Supabase
 * @param id ID шаблона
 * @param template Данные для обновления
 * @returns Обновленный шаблон
 */
export async function updateContractTemplate(
  supabase: SupabaseClient,
  id: string,
  template: ContractTemplateUpdate
): Promise<ContractTemplate> {
  try {
    const { data, error } = await supabase
      .from('contract_templates')
      .update(template)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data as ContractTemplate;
  } catch (error) {
    console.error('Ошибка при обновлении шаблона договора:', error);
    throw error;
  }
}

/**
 * Удаление шаблона договора
 * 
 * @param supabase Клиент Supabase
 * @param id ID шаблона
 * @returns Успешность операции
 */
export async function deleteContractTemplate(
  supabase: SupabaseClient,
  id: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('contract_templates')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при удалении шаблона договора:', error);
    throw error;
  }
}

/**
 * Получение списка типов шаблонов договоров
 * 
 * @param supabase Клиент Supabase
 * @returns Список типов шаблонов
 */
export async function getContractTemplateTypes(
  supabase: SupabaseClient
): Promise<DbContractTemplateType[]> {
  try {
    const { data, error } = await supabase
      .from('contract_template_types')
      .select('*')
      .order('name');

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Ошибка при получении типов шаблонов договоров:', error);
    throw error;
  }
}
