/**
 * @file: contract-documents.ts
 * @description: API функции для работы с документами договоров
 * @dependencies: @supabase/supabase-js
 * @created: 2024-05-10
 */

import { SupabaseClient } from '@supabase/supabase-js';
import {
  ContractDocument,
  ContractDocumentInsert,
  ContractDocumentsResult
} from '../types/contract-templates';
// Contract type будет импортирован динамически в функции generateContractDocument

/**
 * Получение списка документов договора
 *
 * @param supabase Клиент Supabase
 * @param contractId ID договора
 * @param page Номер страницы
 * @param limit Количество записей на странице
 * @returns Результат запроса с документами и метаданными
 */
export async function getContractDocuments(
  supabase: SupabaseClient,
  contractId: string,
  page: number = 1,
  limit: number = 10
): Promise<ContractDocumentsResult> {
  try {
    // Базовый запрос
    let query = supabase
      .from('contract_documents')
      .select(`
        *,
        contracts!contract_documents_contract_id_fkey (
          id,
          number
        ),
        contract_template_versions!contract_documents_template_version_id_fkey (
          id,
          version_number,
          template_id,
          contract_templates!contract_template_versions_template_id_fkey (
            id,
            name
          )
        )
      `)
      .eq('contract_id', contractId);

    // Получаем общее количество записей для пагинации
    const { count } = await supabase
      .from('contract_documents')
      .select('*', { count: 'exact', head: true })
      .eq('contract_id', contractId);

    // По умолчанию сортируем по дате создания (от новых к старым)
    query = query.order('created_at', { ascending: false });

    // Применяем пагинацию
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    query = query.range(from, to);

    // Выполняем запрос
    const { data, error } = await query;

    if (error) {
      throw error;
    }

    // Преобразуем данные в нужный формат
    const documents = data.map(item => {
      const contract = item.contracts;
      const version = item.contract_template_versions;
      const template = version?.contract_templates;

      // Создаем новый объект без вложенных объектов
      const { contracts, contract_template_versions, ...documentWithoutNested } = item;

      return {
        ...documentWithoutNested,
        contract_number: contract?.number,
        template_name: template?.name,
        template_version: version?.version_number
      };
    });

    return {
      data: documents,
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  } catch (error) {
    console.error('Ошибка при получении списка документов договора:', error);
    throw error;
  }
}

/**
 * Получение документа договора по ID
 *
 * @param supabase Клиент Supabase
 * @param id ID документа
 * @returns Документ с дополнительной информацией
 */
export async function getContractDocumentById(
  supabase: SupabaseClient,
  id: string
): Promise<ContractDocument | null> {
  try {
    const { data, error } = await supabase
      .from('contract_documents')
      .select(`
        *,
        contracts!contract_documents_contract_id_fkey (
          id,
          number
        ),
        contract_template_versions!contract_documents_template_version_id_fkey (
          id,
          version_number,
          template_id,
          contract_templates!contract_template_versions_template_id_fkey (
            id,
            name
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return null;
    }

    const contract = data.contracts;
    const version = data.contract_template_versions;
    const template = version?.contract_templates;

    // Создаем новый объект без вложенных объектов
    const { contracts, contract_template_versions, ...documentWithoutNested } = data;

    return {
      ...documentWithoutNested,
      contract_number: contract?.number,
      template_name: template?.name,
      template_version: version?.version_number
    };
  } catch (error) {
    console.error('Ошибка при получении документа договора:', error);
    throw error;
  }
}

/**
 * Генерация документа договора на основе шаблона
 *
 * @param supabase Клиент Supabase
 * @param contractId ID договора
 * @param templateVersionId ID версии шаблона
 * @param userId ID пользователя, создающего документ
 * @returns Созданный документ
 */
export async function generateContractDocument(
  supabase: SupabaseClient,
  contractId: string,
  templateVersionId: string,
  userId: string
): Promise<ContractDocument> {
  try {
    // Получаем данные договора
    const { data: contractData, error: contractError } = await supabase
      .from('contracts')
      .select(`
        *,
        clients!contracts_client_id_fkey (
          id,
          first_name,
          last_name,
          middle_name,
          passport_number,
          passport_issued_by,
          passport_issued_date,
          address
        ),
        apartments!contracts_apartment_id_fkey (
          id,
          number,
          floor,
          rooms,
          area,
          price,
          buildings!apartments_building_id_fkey (
            id,
            name,
            address,
            complexes!buildings_complex_id_fkey (
              id,
              name,
              address
            )
          )
        ),
        tenants!contracts_tenant_id_fkey (
          id,
          name,
          legal_name,
          tax_id,
          address
        )
      `)
      .eq('id', contractId)
      .single();

    if (contractError) {
      throw contractError;
    }

    // Получаем данные версии шаблона
    const { data: versionData, error: versionError } = await supabase
      .from('contract_template_versions')
      .select(`
        *,
        contract_templates!contract_template_versions_template_id_fkey (
          id,
          name
        )
      `)
      .eq('id', templateVersionId)
      .single();

    if (versionError) {
      throw versionError;
    }

    // Получаем файлы версии шаблона
    const { data: filesData, error: filesError } = await supabase
      .from('contract_template_files')
      .select('*')
      .eq('template_version_id', templateVersionId)
      .order('created_at', { ascending: false })
      .limit(1);

    if (filesError) {
      throw filesError;
    }

    if (filesData.length === 0) {
      throw new Error('Для версии шаблона не найдены файлы');
    }

    // Используем данные из запроса напрямую
    const contract = contractData;
    const client = contractData.clients;
    const apartment = contractData.apartments;
    const building = apartment?.buildings;
    const complex = building?.complexes;
    const tenant = contractData.tenants;

    // Подготавливаем данные для подстановки в шаблон (в реальном приложении)
    // Эти данные будут использоваться для генерации документа
    // Для примера просто логируем их
    console.log('Данные для генерации документа:', {
      contract: {
        id: contract.id,
        number: contract.number,
        signed_date: contract.signed_date,
        start_date: contract.start_date,
        end_date: contract.end_date,
        total_amount: contract.total_amount,
        initial_payment: contract.initial_payment,
        monthly_payment: contract.monthly_payment,
        status: contract.status
      },
      client: client ? {
        id: client.id,
        first_name: client.first_name,
        last_name: client.last_name,
        middle_name: client.middle_name,
        full_name: `${client.last_name} ${client.first_name} ${client.middle_name || ''}`.trim(),
        passport_number: client.passport_number,
        passport_issued_by: client.passport_issued_by,
        passport_issued_date: client.passport_issued_date,
        address: client.address
      } : null,
      apartment: apartment ? {
        id: apartment.id,
        number: apartment.number,
        floor: apartment.floor,
        rooms: apartment.rooms,
        area: apartment.area,
        price: apartment.price
      } : null,
      building: building ? {
        id: building.id,
        name: building.name,
        address: building.address
      } : null,
      complex: complex ? {
        id: complex.id,
        name: complex.name,
        address: complex.address
      } : null,
      tenant: tenant ? {
        id: tenant.id,
        name: tenant.name,
        legal_name: tenant.legal_name,
        tax_id: tenant.tax_id,
        address: tenant.address
      } : null
    });

    // Генерируем PDF документ на основе шаблона и данных
    const { generateContractPDF } = await import('../utils/pdf-generator');
    const { contractToTemplateData } = await import('../utils/template-variables');

    // Подготавливаем данные для шаблона
    const templateData = contractToTemplateData(contractData);

    // Получаем содержимое шаблона (если есть)
    let templateContent: string | undefined;
    if (versionData.content) {
      templateContent = versionData.content;
    }

    // Генерируем PDF
    const pdfResult = await generateContractPDF({
      templateData,
      templateContent,
      fileName: `${contractData.number}_${new Date().toISOString().split('T')[0]}.pdf`,
    });

    // Загружаем сгенерированный PDF в хранилище
    const filePath = `${contractData.tenant_id}/contracts/${contractId}/documents/${Date.now()}_${pdfResult.fileName}`;

    const { error: uploadError } = await supabase.storage
      .from('contracts')
      .upload(filePath, pdfResult.blob, {
        contentType: 'application/pdf',
        upsert: false,
      });

    if (uploadError) {
      throw new Error(`Ошибка при загрузке PDF в хранилище: ${uploadError.message}`);
    }

    // Получаем публичный URL файла
    const { data: urlData } = supabase.storage
      .from('contracts')
      .getPublicUrl(filePath);

    // Создаем запись о документе в базе данных
    const documentData: ContractDocumentInsert = {
      contract_id: contractId,
      template_version_id: templateVersionId,
      tenant_id: contractData.tenant_id,
      file_name: pdfResult.fileName,
      file_path: urlData.publicUrl,
      file_type: 'application/pdf',
      file_size: pdfResult.blob.size,
      created_by: userId
    };

    const { data, error } = await supabase
      .from('contract_documents')
      .insert(documentData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data as ContractDocument;
  } catch (error) {
    console.error('Ошибка при генерации документа договора:', error);
    throw error;
  }
}

/**
 * Удаление документа договора
 *
 * @param supabase Клиент Supabase
 * @param id ID документа
 * @returns Успешность операции
 */
export async function deleteContractDocument(
  supabase: SupabaseClient,
  id: string
): Promise<boolean> {
  try {
    // Получаем информацию о документе
    const { data: documentData, error: documentError } = await supabase
      .from('contract_documents')
      .select('*')
      .eq('id', id)
      .single();

    if (documentError) {
      throw documentError;
    }

    // Удаляем файл из хранилища
    const filePath = documentData.file_path.split('/').slice(-3).join('/');
    const { error: storageError } = await supabase.storage
      .from('contracts')
      .remove([filePath]);

    if (storageError) {
      console.warn('Ошибка при удалении файла из хранилища:', storageError);
    }

    // Удаляем запись о документе из базы данных
    const { error } = await supabase
      .from('contract_documents')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при удалении документа договора:', error);
    throw error;
  }
}
