/**
 * @file: complexes.ts
 * @description: API функции для работы с жилыми комплексами
 * @dependencies: @supabase/supabase-js, types/properties
 * @created: 2024-12-26
 */

import { SupabaseClient } from '@supabase/supabase-js';
import {
  Complex,
  ComplexInsert,
  ComplexUpdate,
  ComplexFilters,
  PropertyPagination,
  ComplexSorting,
  ComplexesResult,
  ComplexStats,
} from '../types/properties';

/**
 * Получение списка жилых комплексов с фильтрацией, пагинацией и сортировкой
 *
 * @param supabase Клиент Supabase
 * @param filters Фильтры для жилых комплексов
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Результат запроса с жилыми комплексами и метаданными
 */
export async function getComplexes(
  supabase: SupabaseClient,
  filters?: ComplexFilters,
  pagination?: PropertyPagination,
  sorting?: ComplexSorting
): Promise<ComplexesResult> {
  try {
    // Базовый запрос с подсчетом зданий и квартир
    let query = supabase
      .from('complexes')
      .select(`
        *,
        buildings!complexes_buildings_complex_id_fkey (
          id,
          apartments!buildings_apartments_building_id_fkey (
            id,
            status,
            area,
            price
          )
        )
      `, { count: 'exact' });

    // Применение фильтров
    if (filters) {
      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,address.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      if (filters.status) {
        if (Array.isArray(filters.status)) {
          query = query.in('status', filters.status);
        } else {
          query = query.eq('status', filters.status);
        }
      }

      if (filters.created_after) {
        query = query.gte('created_at', filters.created_after);
      }

      if (filters.created_before) {
        query = query.lte('created_at', filters.created_before);
      }
    }

    // Применение сортировки
    if (sorting?.field && sorting?.order) {
      query = query.order(sorting.field, { ascending: sorting.order === 'asc' });
    } else {
      // Сортировка по умолчанию
      query = query.order('created_at', { ascending: false });
    }

    // Применение пагинации
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 20;
    const offset = pagination?.offset || (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    if (!data) {
      return {
        data: [],
        count: 0,
        page,
        limit,
        total_pages: 0,
      };
    }

    // Обработка данных и добавление вычисляемых полей
    const complexes: Complex[] = data.map((complex) => {
      const buildings = complex.buildings || [];
      const allApartments = buildings.flatMap((building: any) => building.apartments || []);

      const buildings_count = buildings.length;
      const apartments_count = allApartments.length;
      const available_apartments = allApartments.filter((apt: any) => apt.status === 'available').length;
      const sold_apartments = allApartments.filter((apt: any) => apt.status === 'sold').length;
      const total_area = allApartments.reduce((sum: number, apt: any) => sum + (apt.area || 0), 0);
      const average_price = apartments_count > 0
        ? allApartments.reduce((sum: number, apt: any) => sum + (apt.price || 0), 0) / apartments_count
        : 0;

      // Удаляем вложенные объекты для чистоты ответа
      const { buildings: _, ...complexData } = complex;

      return {
        ...complexData,
        buildings_count,
        apartments_count,
        available_apartments,
        sold_apartments,
        total_area,
        average_price,
      };
    });

    // Применение дополнительных фильтров после обработки данных
    let filteredComplexes = complexes;

    if (filters) {
      if (filters.min_apartments !== undefined) {
        filteredComplexes = filteredComplexes.filter(c => (c.apartments_count || 0) >= filters.min_apartments!);
      }

      if (filters.max_apartments !== undefined) {
        filteredComplexes = filteredComplexes.filter(c => (c.apartments_count || 0) <= filters.max_apartments!);
      }

      if (filters.min_price !== undefined) {
        filteredComplexes = filteredComplexes.filter(c => (c.average_price || 0) >= filters.min_price!);
      }

      if (filters.max_price !== undefined) {
        filteredComplexes = filteredComplexes.filter(c => (c.average_price || 0) <= filters.max_price!);
      }
    }

    const total_pages = Math.ceil((count || 0) / limit);

    return {
      data: filteredComplexes,
      count: count || 0,
      page,
      limit,
      total_pages,
    };
  } catch (error) {
    console.error('Ошибка при получении жилых комплексов:', error);
    throw error;
  }
}

/**
 * Получение жилого комплекса по ID
 *
 * @param supabase Клиент Supabase
 * @param id ID жилого комплекса
 * @returns Жилой комплекс с дополнительной информацией
 */
export async function getComplexById(
  supabase: SupabaseClient,
  id: string
): Promise<Complex | null> {
  try {
    const { data, error } = await supabase
      .from('complexes')
      .select(`
        *,
        buildings!complexes_buildings_complex_id_fkey (
          id,
          name,
          address,
          floors,
          status,
          created_at,
          apartments!buildings_apartments_building_id_fkey (
            id,
            number,
            floor,
            rooms,
            area,
            price,
            status
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return null;
    }

    const buildings = data.buildings || [];
    const allApartments = buildings.flatMap((building: any) => building.apartments || []);

    const buildings_count = buildings.length;
    const apartments_count = allApartments.length;
    const available_apartments = allApartments.filter((apt: any) => apt.status === 'available').length;
    const sold_apartments = allApartments.filter((apt: any) => apt.status === 'sold').length;
    const total_area = allApartments.reduce((sum: number, apt: any) => sum + (apt.area || 0), 0);
    const average_price = apartments_count > 0
      ? allApartments.reduce((sum: number, apt: any) => sum + (apt.price || 0), 0) / apartments_count
      : 0;

    const complex: Complex = {
      ...data,
      buildings_count,
      apartments_count,
      available_apartments,
      sold_apartments,
      total_area,
      average_price,
      buildings: buildings.map((building: any) => ({
        ...building,
        apartments_count: building.apartments?.length || 0,
        available_apartments: building.apartments?.filter((apt: any) => apt.status === 'available').length || 0,
        sold_apartments: building.apartments?.filter((apt: any) => apt.status === 'sold').length || 0,
        total_area: building.apartments?.reduce((sum: number, apt: any) => sum + (apt.area || 0), 0) || 0,
        average_price: building.apartments?.length
          ? building.apartments.reduce((sum: number, apt: any) => sum + (apt.price || 0), 0) / building.apartments.length
          : 0,
      })),
    };

    return complex;
  } catch (error) {
    console.error('Ошибка при получении жилого комплекса:', error);
    throw error;
  }
}

/**
 * Создание нового жилого комплекса
 *
 * @param supabase Клиент Supabase
 * @param complexData Данные для создания жилого комплекса
 * @returns Созданный жилой комплекс
 */
export async function createComplex(
  supabase: SupabaseClient,
  complexData: ComplexInsert
): Promise<Complex> {
  try {
    const { data, error } = await supabase
      .from('complexes')
      .insert(complexData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('Не удалось создать жилой комплекс');
    }

    return {
      ...data,
      buildings_count: 0,
      apartments_count: 0,
      available_apartments: 0,
      sold_apartments: 0,
      total_area: 0,
      average_price: 0,
    };
  } catch (error) {
    console.error('Ошибка при создании жилого комплекса:', error);
    throw error;
  }
}

/**
 * Обновление жилого комплекса
 *
 * @param supabase Клиент Supabase
 * @param id ID жилого комплекса
 * @param updates Данные для обновления
 * @returns Обновленный жилой комплекс
 */
export async function updateComplex(
  supabase: SupabaseClient,
  id: string,
  updates: ComplexUpdate
): Promise<Complex> {
  try {
    const { data, error } = await supabase
      .from('complexes')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('Не удалось обновить жилой комплекс');
    }

    // Получаем полную информацию о комплексе
    return await getComplexById(supabase, id) || data;
  } catch (error) {
    console.error('Ошибка при обновлении жилого комплекса:', error);
    throw error;
  }
}

/**
 * Удаление жилого комплекса
 *
 * @param supabase Клиент Supabase
 * @param id ID жилого комплекса
 * @returns Результат удаления
 */
export async function deleteComplex(
  supabase: SupabaseClient,
  id: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('complexes')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при удалении жилого комплекса:', error);
    throw error;
  }
}

/**
 * Получение статистики по жилому комплексу
 *
 * @param supabase Клиент Supabase
 * @param id ID жилого комплекса
 * @returns Статистика по жилому комплексу
 */
export async function getComplexStats(
  supabase: SupabaseClient,
  id: string
): Promise<ComplexStats> {
  try {
    const complex = await getComplexById(supabase, id);

    if (!complex) {
      throw new Error('Жилой комплекс не найден');
    }

    const buildings = complex.buildings || [];
    const allApartments = buildings.flatMap(building => building.apartments || []);

    const total_buildings = buildings.length;
    const total_apartments = allApartments.length;
    const available_apartments = allApartments.filter(apt => apt.status === 'available').length;
    const reserved_apartments = allApartments.filter(apt => apt.status === 'reserved').length;
    const sold_apartments = allApartments.filter(apt => apt.status === 'sold').length;
    const total_area = allApartments.reduce((sum, apt) => sum + (apt.area || 0), 0);

    const prices = allApartments.map(apt => apt.price || 0).filter(price => price > 0);
    const average_price = prices.length > 0 ? prices.reduce((sum, price) => sum + price, 0) / prices.length : 0;
    const min_price = prices.length > 0 ? Math.min(...prices) : 0;
    const max_price = prices.length > 0 ? Math.max(...prices) : 0;

    // Примерный расчет процента завершения (можно улучшить)
    const completion_percentage = total_apartments > 0
      ? Math.round(((sold_apartments + reserved_apartments) / total_apartments) * 100)
      : 0;

    return {
      total_buildings,
      total_apartments,
      available_apartments,
      reserved_apartments,
      sold_apartments,
      total_area,
      average_price,
      min_price,
      max_price,
      completion_percentage,
    };
  } catch (error) {
    console.error('Ошибка при получении статистики жилого комплекса:', error);
    throw error;
  }
}
