import React, { ReactNode } from 'react';
import { AuthProvider } from './AuthContext';
import { TenantProvider } from './TenantContext';
import { RoleProvider } from './RoleContext';

/**
 * Свойства комбинированного провайдера Supabase
 */
export interface SupabaseProviderProps {
  /** Дочерние элементы */
  children: ReactNode;
  /** Начальный ID арендатора */
  initialTenantId?: string | null;
}

/**
 * Комбинированный провайдер для Supabase
 *
 * Объединяет AuthProvider, TenantProvider и RoleProvider для удобства использования
 *
 * @param props Свойства компонента
 * @returns Комбинированный провайдер
 *
 * @example
 * ```tsx
 * <SupabaseProvider>
 *   <App />
 * </SupabaseProvider>
 * ```
 */
export function SupabaseProvider({
  children,
  initialTenantId
}: SupabaseProviderProps) {
  return (
    <AuthProvider>
      <TenantProvider initialTenantId={initialTenantId}>
        <RoleProvider>
          {children}
        </RoleProvider>
      </TenantProvider>
    </AuthProvider>
  );
}
