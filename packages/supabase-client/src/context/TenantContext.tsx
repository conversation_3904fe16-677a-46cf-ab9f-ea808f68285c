import React, { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';

/**
 * Тип контекста арендатора
 */
export interface TenantContextType {
  /** ID текущего арендатора */
  tenantId: string | null;
  /** Функция для установки ID арендатора */
  setTenantId: (id: string | null) => void;
}

/**
 * Контекст арендатора
 */
const TenantContext = createContext<TenantContextType | undefined>(undefined);

/**
 * Свойства провайдера арендатора
 */
export interface TenantProviderProps {
  /** Дочерние элементы */
  children: ReactNode;
  /** Начальный ID арендатора */
  initialTenantId?: string | null;
}

/**
 * Провайдер контекста арендатора
 * 
 * Предоставляет доступ к ID текущего арендатора и методу для его изменения
 * 
 * @param props Свойства компонента
 * @returns Провайдер контекста арендатора
 * 
 * @example
 * ```tsx
 * <TenantProvider>
 *   <App />
 * </TenantProvider>
 * ```
 */
export function TenantProvider({ 
  children,
  initialTenantId = null 
}: TenantProviderProps) {
  const [tenantId, setTenantId] = useState<string | null>(initialTenantId);
  const { user } = useAuth();

  // Если у пользователя есть tenant_id в метаданных, используем его
  useEffect(() => {
    if (user?.user_metadata?.tenant_id && !tenantId) {
      setTenantId(user.user_metadata.tenant_id);
    }
  }, [user, tenantId]);

  return (
    <TenantContext.Provider value={{ tenantId, setTenantId }}>
      {children}
    </TenantContext.Provider>
  );
}

/**
 * Хук для доступа к контексту арендатора
 * 
 * @returns Контекст арендатора
 * @throws Ошибку, если используется вне TenantProvider
 * 
 * @example
 * ```tsx
 * const { tenantId, setTenantId } = useTenant();
 * 
 * // Получение ID текущего арендатора
 * console.log('Текущий арендатор:', tenantId);
 * 
 * // Изменение арендатора
 * const handleChangeTenant = (newTenantId: string) => {
 *   setTenantId(newTenantId);
 * };
 * ```
 */
export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant должен использоваться внутри TenantProvider');
  }
  return context;
}
