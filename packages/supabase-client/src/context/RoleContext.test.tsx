/**
 * @file: RoleContext.test.tsx
 * @description: Тесты для контекста ролей
 * @dependencies: vitest, @testing-library/react, @testing-library/react-hooks
 * @created: 2024-05-03
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react-hooks';
import { ReactNode } from 'react';
import { RoleProvider, useRole } from '../context/RoleContext';
import { useAuth } from '../context/AuthContext';
import { useTenant } from '../context/TenantContext';
import { mockRoles, mockPermissions } from '../tests/mocks/supabase';

// Создаем моки для тестов
const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  user_metadata: { role: null as string | null },
  app_metadata: {},
  aud: 'authenticated',
  created_at: '2023-01-01T00:00:00.000Z',
  updated_at: '2023-01-01T00:00:00.000Z',
  phone: '',
  confirmed_at: '2023-01-01T00:00:00.000Z',
  last_sign_in_at: '2023-01-01T00:00:00.000Z',
  role: '',
  identities: []
};

const mockSupabaseRpc = vi.fn();

// Мок для сессии
const mockSession = {
  user: mockUser,
  access_token: 'mock-access-token',
  refresh_token: 'mock-refresh-token',
  expires_in: 3600,
  token_type: 'bearer',
  expires_at: Math.floor(Date.now() / 1000) + 3600
};

// Мок для AuthContext
const mockAuthContext = {
  user: mockUser,
  session: mockSession,
  supabase: {
    auth: {
      getUser: vi.fn().mockResolvedValue({ data: { user: mockUser } }),
      getSession: vi.fn().mockResolvedValue({ data: { session: mockSession } }),
      signOut: vi.fn().mockResolvedValue({ error: null }),
    },
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    rpc: mockSupabaseRpc,
  },
  signIn: vi.fn(),
  signUp: vi.fn(),
  signOut: vi.fn(),
  resetPassword: vi.fn(),
  loading: false,
  error: null,
};

// Мок для TenantContext
const mockTenantContext = {
  tenant: {
    id: 'tenant-1',
    name: 'Test Tenant',
    domain: 'test.example.com',
  },
  tenantId: 'tenant-1',
  loading: false,
  error: null,
  setTenantId: vi.fn(),
};

// Мок для хуков контекста
vi.mock('../context/AuthContext', () => {
  return {
    AuthContext: {
      Provider: ({ children }: { children: ReactNode }) => children,
      Consumer: ({ children }: { children: (value: any) => ReactNode }) => children({}),
    },
    useAuth: vi.fn(),
  };
});

vi.mock('../context/TenantContext', () => ({
  TenantContext: {
    Provider: ({ children }: { children: ReactNode }) => children,
    Consumer: ({ children }: { children: (value: any) => ReactNode }) => children({}),
  },
  useTenant: vi.fn(),
}));

// Мок для Supabase клиента
vi.mock('@supabase/auth-helpers-react', () => ({
  useSupabaseClient: () => mockAuthContext.supabase,
}));

describe('RoleContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Сбрасываем состояние моков перед каждым тестом
    mockUser.user_metadata.role = null;

    // Настраиваем моки для хуков
    const useAuthMock = vi.mocked(useAuth);
    useAuthMock.mockReturnValue(mockAuthContext);

    const useTenantMock = vi.mocked(useTenant);
    useTenantMock.mockReturnValue(mockTenantContext);
  });

  it('должен предоставлять контекст ролей', async () => {
    // Настраиваем мок для rpc
    mockSupabaseRpc
      .mockResolvedValueOnce({ data: mockRoles, error: null }) // get_user_roles
      .mockResolvedValueOnce({ data: mockPermissions, error: null }) // get_user_permissions
      .mockResolvedValueOnce({ data: mockRoles, error: null }) // get_user_roles при обновлении
      .mockResolvedValueOnce({ data: mockPermissions, error: null }); // get_user_permissions при обновлении

    // Рендерим хук с провайдером
    const wrapper = ({ children }: { children: ReactNode }) => (
      <RoleProvider>{children}</RoleProvider>
    );

    const { result, waitForNextUpdate } = renderHook(() => useRole(), { wrapper });

    // Проверяем начальное состояние
    expect(result.current.loading).toBe(true);
    expect(result.current.role).toBeNull();
    expect(result.current.roles).toEqual([]);
    expect(result.current.permissions).toEqual([]);

    // Ждем загрузки данных
    await waitForNextUpdate();

    // Проверяем, что данные загрузились, но роль все еще null
    expect(result.current.loading).toBe(false);
    expect(result.current.role).toBeNull();
    expect(result.current.roles).toEqual(mockRoles);
    expect(result.current.permissions).toEqual(mockPermissions);

    // Устанавливаем роль пользователя
    mockUser.user_metadata.role = 'superadmin';

    // Обновляем разрешения, чтобы изменения роли вступили в силу
    await act(async () => {
      await result.current.refreshPermissions();
    });

    // Проверяем загруженные данные после обновления
    expect(result.current.loading).toBe(false);
    expect(result.current.role).toBe('superadmin');
    expect(result.current.roles).toEqual(mockRoles);
    expect(result.current.permissions).toEqual(mockPermissions);

    // Проверяем, что rpc был вызван с правильными параметрами
    expect(mockSupabaseRpc).toHaveBeenCalledWith('get_user_roles', { p_user_id: 'user-1' });
    expect(mockSupabaseRpc).toHaveBeenCalledWith('get_user_permissions', { p_user_id: 'user-1' });
  });

  it('должен проверять наличие разрешения', async () => {
    // Настраиваем мок для rpc
    mockSupabaseRpc
      .mockResolvedValueOnce({ data: mockRoles, error: null }) // get_user_roles
      .mockResolvedValueOnce({ data: mockPermissions, error: null }) // get_user_permissions
      .mockResolvedValueOnce({ data: mockRoles, error: null }) // get_user_roles при обновлении с superadmin
      .mockResolvedValueOnce({ data: mockPermissions, error: null }) // get_user_permissions при обновлении с superadmin
      .mockResolvedValueOnce({ data: mockRoles, error: null }) // get_user_roles при обновлении с support
      .mockResolvedValueOnce({ data: mockPermissions, error: null }); // get_user_permissions при обновлении с support

    // Рендерим хук с провайдером
    const wrapper = ({ children }: { children: ReactNode }) => (
      <RoleProvider>{children}</RoleProvider>
    );

    const { result, waitForNextUpdate } = renderHook(() => useRole(), { wrapper });

    // Ждем загрузки данных
    await waitForNextUpdate();

    // Устанавливаем роль суперадмина
    mockUser.user_metadata.role = 'superadmin';

    // Обновляем разрешения, чтобы изменения роли вступили в силу
    await act(async () => {
      await result.current.refreshPermissions();
    });

    // Проверяем функцию hasPermission для суперадмина
    expect(result.current.hasPermission('any_resource', 'any_action')).toBe(true);

    // Устанавливаем роль support
    mockUser.user_metadata.role = 'support';

    // Обновляем разрешения, чтобы изменения роли вступили в силу
    await act(async () => {
      await result.current.refreshPermissions();
    });

    // Проверяем функцию hasPermission для support
    expect(result.current.hasPermission('any_resource', 'read')).toBe(true);
    expect(result.current.hasPermission('any_resource', 'create')).toBe(false);
  });

  it('должен проверять наличие роли', async () => {
    // Настраиваем мок для rpc
    mockSupabaseRpc
      // Первая загрузка
      .mockResolvedValueOnce({ data: mockRoles, error: null }) // get_user_roles
      .mockResolvedValueOnce({ data: mockPermissions, error: null }) // get_user_permissions
      // Обновление при refreshPermissions
      .mockResolvedValueOnce({ data: mockRoles, error: null }) // get_user_roles
      .mockResolvedValueOnce({ data: mockPermissions, error: null }); // get_user_permissions

    // Рендерим хук с провайдером
    const wrapper = ({ children }: { children: ReactNode }) => (
      <RoleProvider>{children}</RoleProvider>
    );

    const { result, waitForNextUpdate } = renderHook(() => useRole(), { wrapper });

    // Ждем загрузки данных
    await waitForNextUpdate();

    // Устанавливаем роль суперадмина
    mockUser.user_metadata.role = 'superadmin';

    // Обновляем разрешения, чтобы изменения роли вступили в силу
    await act(async () => {
      await result.current.refreshPermissions();
    });

    // Проверяем функцию hasRole для superadmin
    // Для текущей роли пользователя проверка происходит без вызова rpc
    expect(result.current.hasRole('superadmin')).toBe(true);

    // Для других ролей проверяем через список ролей
    // Мокаем roles.some, чтобы он возвращал false для 'client'
    const hasSpy = vi.spyOn(result.current.roles, 'some');
    hasSpy.mockReturnValue(false);

    expect(result.current.hasRole('client')).toBe(false);

    // Проверяем, что some был вызван с правильной функцией
    expect(hasSpy).toHaveBeenCalled();

    // Восстанавливаем оригинальную реализацию
    hasSpy.mockRestore();
  });

  it('должен обновлять разрешения', async () => {
    // Настраиваем мок для rpc
    mockSupabaseRpc
      // Первая загрузка
      .mockResolvedValueOnce({ data: mockRoles, error: null }) // get_user_roles при первой загрузке
      .mockResolvedValueOnce({ data: mockPermissions, error: null }) // get_user_permissions при первой загрузке
      // Обновление
      .mockResolvedValueOnce({ data: mockRoles, error: null }) // get_user_roles при обновлении
      .mockResolvedValueOnce({ data: mockPermissions, error: null }); // get_user_permissions при обновлении

    // Рендерим хук с провайдером
    const wrapper = ({ children }: { children: ReactNode }) => (
      <RoleProvider>{children}</RoleProvider>
    );

    const { result, waitForNextUpdate } = renderHook(() => useRole(), { wrapper });

    // Ждем загрузки данных
    await waitForNextUpdate();

    // Устанавливаем роль суперадмина
    mockUser.user_metadata.role = 'superadmin';

    // Проверяем, что данные загрузились
    expect(result.current.loading).toBe(false);
    expect(result.current.roles).toEqual(mockRoles);
    expect(result.current.permissions).toEqual(mockPermissions);

    // Обновляем разрешения
    await act(async () => {
      await result.current.refreshPermissions();
    });

    // Проверяем, что данные обновились
    expect(result.current.loading).toBe(false);
    expect(result.current.role).toBe('superadmin');
    expect(result.current.roles).toEqual(mockRoles);
    expect(result.current.permissions).toEqual(mockPermissions);

    // Проверяем, что rpc был вызван 4 раза
    expect(mockSupabaseRpc).toHaveBeenCalledTimes(4);
  });
});
