/**
 * Контекст для работы с ролями и разрешениями
 * 
 * @module context/RoleContext
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { useTenant } from './TenantContext';

/**
 * Тип разрешения
 */
export interface Permission {
  /** Название разрешения */
  name: string;
  /** Ресурс, к которому относится разрешение */
  resource: string;
  /** Действие (create, read, update, delete) */
  action: string;
  /** ID арендатора, к которому относится разрешение (если есть) */
  tenant_id?: string | null;
}

/**
 * Тип роли
 */
export interface Role {
  /** ID роли */
  id: string;
  /** Название роли */
  name: string;
  /** Описание роли */
  description?: string;
  /** ID арендатора, к которому относится роль (если есть) */
  tenant_id?: string | null;
}

/**
 * Тип контекста ролей
 */
export interface RoleContextType {
  /** Текущая роль пользователя */
  role: string | null;
  /** Список всех ролей пользователя */
  roles: Role[];
  /** Список всех разрешений пользователя */
  permissions: Permission[];
  /** Проверка наличия разрешения */
  hasPermission: (resource: string, action: string) => boolean;
  /** Проверка наличия роли */
  hasRole: (roleName: string) => boolean;
  /** Загрузка данных о ролях и разрешениях */
  loading: boolean;
  /** Ошибка при загрузке данных */
  error: Error | null;
  /** Обновление данных о ролях и разрешениях */
  refreshPermissions: () => Promise<void>;
}

/**
 * Контекст ролей
 */
const RoleContext = createContext<RoleContextType | undefined>(undefined);

/**
 * Свойства провайдера ролей
 */
export interface RoleProviderProps {
  /** Дочерние элементы */
  children: ReactNode;
}

/**
 * Провайдер контекста ролей
 * 
 * Предоставляет доступ к ролям и разрешениям пользователя
 * 
 * @param props Свойства компонента
 * @returns Провайдер контекста ролей
 * 
 * @example
 * ```tsx
 * <RoleProvider>
 *   <App />
 * </RoleProvider>
 * ```
 */
export function RoleProvider({ children }: RoleProviderProps) {
  const { user, supabase, loading: authLoading } = useAuth();
  const { tenantId } = useTenant();
  
  const [role, setRole] = useState<string | null>(null);
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Загрузка ролей и разрешений пользователя
  const loadUserRolesAndPermissions = async () => {
    if (!user) {
      setRoles([]);
      setPermissions([]);
      setRole(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Получаем основную роль из метаданных пользователя
      const userRole = user.user_metadata?.role || null;
      setRole(userRole);

      // Получаем все роли пользователя
      const { data: userRoles, error: rolesError } = await supabase
        .rpc('get_user_roles', { p_user_id: user.id });

      if (rolesError) throw rolesError;
      setRoles(userRoles || []);

      // Получаем все разрешения пользователя
      const { data: userPermissions, error: permissionsError } = await supabase
        .rpc('get_user_permissions', { p_user_id: user.id });

      if (permissionsError) throw permissionsError;
      setPermissions(userPermissions || []);
    } catch (err) {
      console.error('Ошибка при загрузке ролей и разрешений:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Загружаем роли и разрешения при изменении пользователя или арендатора
  useEffect(() => {
    if (!authLoading) {
      loadUserRolesAndPermissions();
    }
  }, [user, tenantId, authLoading]);

  // Проверка наличия разрешения
  const hasPermission = (resource: string, action: string): boolean => {
    // Если пользователь superadmin, у него есть все разрешения
    if (role === 'superadmin') return true;
    
    // Если пользователь support, у него есть разрешения только на чтение
    if (role === 'support' && action === 'read') return true;
    
    // Проверяем наличие разрешения в списке
    return permissions.some(
      (p) => p.resource === resource && p.action === action
    );
  };

  // Проверка наличия роли
  const hasRole = (roleName: string): boolean => {
    // Проверяем основную роль
    if (role === roleName) return true;
    
    // Проверяем наличие роли в списке всех ролей
    return roles.some((r) => r.name === roleName);
  };

  // Обновление данных о ролях и разрешениях
  const refreshPermissions = async () => {
    await loadUserRolesAndPermissions();
  };

  const value: RoleContextType = {
    role,
    roles,
    permissions,
    hasPermission,
    hasRole,
    loading,
    error,
    refreshPermissions
  };

  return <RoleContext.Provider value={value}>{children}</RoleContext.Provider>;
}

/**
 * Хук для доступа к контексту ролей
 * 
 * @returns Контекст ролей
 * @throws Ошибку, если используется вне RoleProvider
 * 
 * @example
 * ```tsx
 * const { hasPermission, hasRole } = useRole();
 * 
 * // Проверка наличия разрешения
 * if (hasPermission('clients', 'create')) {
 *   console.log('Пользователь может создавать клиентов');
 * }
 * 
 * // Проверка наличия роли
 * if (hasRole('tenant_admin')) {
 *   console.log('Пользователь является администратором арендатора');
 * }
 * ```
 */
export function useRole() {
  const context = useContext(RoleContext);
  if (context === undefined) {
    throw new Error('useRole должен использоваться внутри RoleProvider');
  }
  return context;
}
