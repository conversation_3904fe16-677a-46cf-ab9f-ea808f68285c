import React, { createContext, useContext, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { useAuth as useSupabaseAuth } from '../hooks/useAuth';
import { SupabaseClient } from '../supabase';

/**
 * Тип контекста аутентификации
 */
export interface AuthContextType {
  /** Текущий пользователь */
  user: User | null;
  /** Текущая сессия */
  session: Session | null;
  /** Флаг загрузки данных аутентификации */
  loading: boolean;
  /** Функция для входа по email и паролю */
  signIn: (email: string, password: string) => Promise<any>;
  /** Функция для регистрации нового пользователя */
  signUp: (email: string, password: string, metadata?: Record<string, any>) => Promise<any>;
  /** Функция для выхода из системы */
  signOut: () => Promise<any>;
  /** Функция для сброса пароля */
  resetPassword: (email: string) => Promise<any>;
  /** Клиент Supabase */
  supabase: SupabaseClient;
}

/**
 * Контекст аутентификации
 */
const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Свойства провайдера аутентификации
 */
export interface AuthProviderProps {
  /** Дочерние элементы */
  children: ReactNode;
}

/**
 * Провайдер контекста аутентификации
 * 
 * Предоставляет доступ к данным аутентификации и методам для работы с ней
 * 
 * @param props Свойства компонента
 * @returns Провайдер контекста аутентификации
 * 
 * @example
 * ```tsx
 * <AuthProvider>
 *   <App />
 * </AuthProvider>
 * ```
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const auth = useSupabaseAuth();

  return <AuthContext.Provider value={auth}>{children}</AuthContext.Provider>;
}

/**
 * Хук для доступа к контексту аутентификации
 * 
 * @returns Контекст аутентификации
 * @throws Ошибку, если используется вне AuthProvider
 * 
 * @example
 * ```tsx
 * const { user, signIn, signOut } = useAuth();
 * 
 * // Проверка авторизации
 * if (user) {
 *   console.log('Пользователь авторизован:', user.email);
 * }
 * ```
 */
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth должен использоваться внутри AuthProvider');
  }
  return context;
}
