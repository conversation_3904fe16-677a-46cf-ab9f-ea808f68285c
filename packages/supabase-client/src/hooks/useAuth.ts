"use client";

import { useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { createSupabaseClientFromEnv } from '../supabase';
import { useRouter } from 'next/navigation';

/**
 * Хук для управления аутентификацией пользователя
 *
 * Предоставляет доступ к текущему пользователю, сессии и методам аутентификации
 *
 * @returns Объект с данными пользователя, сессии и методами аутентификации
 *
 * @example
 * ```tsx
 * const { user, signIn, signOut } = useAuth();
 *
 * // Проверка авторизации
 * if (user) {
 *   console.log('Пользователь авторизован:', user.email);
 * }
 *
 * // Вход в систему
 * const handleLogin = async () => {
 *   const { error } = await signIn('<EMAIL>', 'password');
 *   if (error) console.error('Ошибка входа:', error.message);
 * };
 * ```
 */
export function useAuth() {
  const supabase = createSupabaseClientFromEnv();
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Получаем текущую сессию при инициализации
    const getSession = async () => {
      setLoading(true);

      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          throw error;
        }

        setSession(session);
        setUser(session?.user ?? null);
      } catch (error) {
        console.error('Ошибка при получении сессии:', error);
        setSession(null);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    getSession();

    // Подписываемся на изменения аутентификации
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, newSession) => {
        setSession(newSession);
        setUser(newSession?.user ?? null);
        setLoading(false);
      }
    );

    // Отписываемся при размонтировании компонента
    return () => {
      subscription.unsubscribe();
    };
  }, [supabase]);

  /**
   * Вход пользователя по email и паролю
   *
   * @param email Email пользователя
   * @param password Пароль пользователя
   * @returns Результат операции входа
   */
  const signIn = async (email: string, password: string) => {
    return await supabase.auth.signInWithPassword({ email, password });
  };

  /**
   * Регистрация нового пользователя
   *
   * @param email Email пользователя
   * @param password Пароль пользователя
   * @param metadata Дополнительные метаданные пользователя
   * @returns Результат операции регистрации
   */
  const signUp = async (email: string, password: string, metadata?: Record<string, any>) => {
    return await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });
  };

  /**
   * Выход пользователя из системы
   *
   * @returns Результат операции выхода
   */
  const signOut = async () => {
    const result = await supabase.auth.signOut();
    router.push('/');
    return result;
  };

  /**
   * Отправка ссылки для сброса пароля
   *
   * @param email Email пользователя
   * @returns Результат операции сброса пароля
   */
  const resetPassword = async (email: string) => {
    return await supabase.auth.resetPasswordForEmail(email);
  };

  /**
   * Обновление пароля пользователя
   *
   * @param password Новый пароль
   * @returns Результат операции обновления пароля
   */
  const updatePassword = async (password: string) => {
    return await supabase.auth.updateUser({ password });
  };

  /**
   * Обновление метаданных пользователя
   *
   * @param metadata Новые метаданные
   * @returns Результат операции обновления метаданных
   */
  const updateUserMetadata = async (metadata: Record<string, any>) => {
    return await supabase.auth.updateUser({
      data: metadata
    });
  };

  return {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    updateUserMetadata,
    supabase
  };
}
