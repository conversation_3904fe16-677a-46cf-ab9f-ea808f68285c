import { useEffect, useState } from 'react';
import { useAuth } from './useAuth';

/**
 * Опции для хука useDataById
 */
export interface DataByIdOptions {
  /** Колонки для выборки (формат Supabase) */
  columns?: string;
  /** Флаг активации запроса */
  enabled?: boolean;
}

/**
 * Хук для получения данных по ID
 * 
 * @param tableName Имя таблицы
 * @param id ID записи
 * @param options Опции запроса
 * @returns Объект с данными, ошибкой, статусом загрузки и функцией обновления
 * 
 * @example
 * ```tsx
 * // Получение клиента по ID
 * const { data: client, loading } = useDataById<Client>('clients', clientId);
 * 
 * // Получение клиента с выбором определенных полей
 * const { data: client } = useDataById<Client>('clients', clientId, {
 *   columns: 'id, name, email, status'
 * });
 * 
 * // Условное получение данных
 * const { data: client } = useDataById<Client>('clients', clientId, {
 *   enabled: !!clientId
 * });
 * ```
 */
export function useDataById<T>(
  tableName: string,
  id: string | undefined,
  options?: DataByIdOptions
) {
  const { supabase } = useAuth();
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(true);

  const enabled = options?.enabled !== undefined ? options.enabled : true;

  useEffect(() => {
    // Если ID не указан или запрос отключен, не выполняем его
    if (!id || !enabled) {
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Выполняем запрос с фильтрацией по ID
        const { data: result, error: queryError } = await supabase
          .from(tableName)
          .select(options?.columns || '*')
          .eq('id', id)
          .single();

        if (queryError) {
          throw queryError;
        }

        setData(result as T);
      } catch (err) {
        setError(err as Error);
        console.error(`Ошибка получения данных из ${tableName} с id ${id}:`, err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [supabase, tableName, id, options?.columns, enabled]);

  /**
   * Функция для принудительного обновления данных
   */
  const refetch = async () => {
    if (!id || !enabled) return;
    
    setLoading(true);
    setError(null);

    try {
      const { data: result, error: queryError } = await supabase
        .from(tableName)
        .select(options?.columns || '*')
        .eq('id', id)
        .single();

      if (queryError) {
        throw queryError;
      }

      setData(result as T);
    } catch (err) {
      setError(err as Error);
      console.error(`Ошибка обновления данных из ${tableName} с id ${id}:`, err);
    } finally {
      setLoading(false);
    }
  };

  return { data, error, loading, refetch };
}
