/**
 * @file: useContractTemplates.ts
 * @description: Хуки для работы с шаблонами договоров
 * @dependencies: react, @supabase/supabase-js
 * @created: 2024-05-10
 */

import { useState, useEffect, useCallback } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { 
  ContractTemplate, 
  ContractTemplateInsert, 
  ContractTemplateUpdate, 
  ContractTemplateFilters, 
  ContractTemplatePagination, 
  ContractTemplateSorting,
  ContractTemplatesResult,
  DbContractTemplateType
} from '../types/contract-templates';
import { 
  getContractTemplates, 
  getContractTemplateById, 
  createContractTemplate, 
  updateContractTemplate, 
  deleteContractTemplate,
  getContractTemplateTypes
} from '../api/contract-templates';
import { useTenant } from '../context/TenantContext';

/**
 * Хук для получения списка шаблонов договоров с фильтрацией, пагинацией и сортировкой
 * 
 * @param filters Фильтры для шаблонов
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Объект с данными и функциями для работы со списком шаблонов
 */
export function useContractTemplatesList(
  initialFilters?: ContractTemplateFilters,
  initialPagination?: ContractTemplatePagination,
  initialSorting?: ContractTemplateSorting
) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();
  
  const [filters, setFilters] = useState<ContractTemplateFilters>(initialFilters || {});
  const [pagination, setPagination] = useState<ContractTemplatePagination>(initialPagination || { page: 1, limit: 10 });
  const [sorting, setSorting] = useState<ContractTemplateSorting | undefined>(initialSorting);
  
  const [result, setResult] = useState<ContractTemplatesResult>({
    data: [],
    count: 0,
    page: pagination.page,
    limit: pagination.limit,
    totalPages: 0
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Функция для загрузки данных
  const fetchTemplates = useCallback(async () => {
    if (!tenantId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const result = await getContractTemplates(supabase, filters, pagination, sorting);
      setResult(result);
    } catch (err) {
      console.error('Ошибка при загрузке шаблонов договоров:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, tenantId, filters, pagination, sorting]);

  // Загружаем данные при изменении параметров
  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  // Функция для обновления фильтров
  const updateFilters = useCallback((newFilters: ContractTemplateFilters) => {
    setFilters(newFilters);
    // Сбрасываем пагинацию при изменении фильтров
    setPagination(prev => ({ ...prev, page: 1 }));
  }, []);

  // Функция для обновления пагинации
  const updatePagination = useCallback((newPagination: Partial<ContractTemplatePagination>) => {
    setPagination(prev => ({ ...prev, ...newPagination }));
  }, []);

  // Функция для обновления сортировки
  const updateSorting = useCallback((newSorting: ContractTemplateSorting) => {
    setSorting(newSorting);
  }, []);

  // Функция для перехода на следующую страницу
  const nextPage = useCallback(() => {
    if (pagination.page < result.totalPages) {
      setPagination(prev => ({ ...prev, page: prev.page + 1 }));
    }
  }, [pagination.page, result.totalPages]);

  // Функция для перехода на предыдущую страницу
  const prevPage = useCallback(() => {
    if (pagination.page > 1) {
      setPagination(prev => ({ ...prev, page: prev.page - 1 }));
    }
  }, [pagination.page]);

  return {
    templates: result.data,
    count: result.count,
    page: result.page,
    limit: result.limit,
    totalPages: result.totalPages,
    loading,
    error,
    filters,
    updateFilters,
    pagination,
    updatePagination,
    sorting,
    updateSorting,
    refresh: fetchTemplates,
    nextPage,
    prevPage
  };
}

/**
 * Хук для работы с отдельным шаблоном договора
 * 
 * @param id ID шаблона (если не указан, то создается новый шаблон)
 * @returns Объект с данными и функциями для работы с шаблоном
 */
export function useContractTemplate(id?: string) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();
  
  const [template, setTemplate] = useState<ContractTemplate | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<Error | null>(null);

  // Функция для загрузки шаблона
  const fetchTemplate = useCallback(async () => {
    if (!id || !tenantId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const template = await getContractTemplateById(supabase, id);
      setTemplate(template);
    } catch (err) {
      console.error('Ошибка при загрузке шаблона договора:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, id, tenantId]);

  // Загружаем шаблон при изменении ID
  useEffect(() => {
    if (id) {
      fetchTemplate();
    } else {
      setTemplate(null);
    }
  }, [id, fetchTemplate]);

  // Функция для сохранения шаблона (создание или обновление)
  const saveTemplate = useCallback(async (data: ContractTemplateInsert | ContractTemplateUpdate) => {
    if (!tenantId) return null;
    
    try {
      setIsSaving(true);
      setSaveError(null);
      
      // Добавляем tenant_id к данным
      const templateData = { ...data, tenant_id: tenantId };
      
      let savedTemplate: ContractTemplate;
      
      if (id) {
        // Обновляем существующий шаблон
        savedTemplate = await updateContractTemplate(supabase, id, templateData as ContractTemplateUpdate);
      } else {
        // Создаем новый шаблон
        savedTemplate = await createContractTemplate(supabase, templateData as ContractTemplateInsert);
      }
      
      setTemplate(savedTemplate);
      return savedTemplate;
    } catch (err) {
      console.error('Ошибка при сохранении шаблона договора:', err);
      setSaveError(err instanceof Error ? err : new Error(String(err)));
      return null;
    } finally {
      setIsSaving(false);
    }
  }, [supabase, id, tenantId]);

  // Функция для удаления шаблона
  const removeTemplate = useCallback(async () => {
    if (!id) return false;
    
    try {
      setIsSaving(true);
      setSaveError(null);
      
      const success = await deleteContractTemplate(supabase, id);
      
      if (success) {
        setTemplate(null);
      }
      
      return success;
    } catch (err) {
      console.error('Ошибка при удалении шаблона договора:', err);
      setSaveError(err instanceof Error ? err : new Error(String(err)));
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [supabase, id]);

  return {
    template,
    loading,
    error,
    isSaving,
    saveError,
    saveTemplate,
    removeTemplate,
    refresh: fetchTemplate
  };
}

/**
 * Хук для получения типов шаблонов договоров
 * 
 * @returns Объект с данными и функциями для работы с типами шаблонов
 */
export function useContractTemplateTypes() {
  const supabase = useSupabaseClient();
  
  const [types, setTypes] = useState<DbContractTemplateType[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Функция для загрузки типов шаблонов
  const fetchTypes = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const types = await getContractTemplateTypes(supabase);
      setTypes(types);
    } catch (err) {
      console.error('Ошибка при загрузке типов шаблонов договоров:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase]);

  // Загружаем типы шаблонов при монтировании компонента
  useEffect(() => {
    fetchTypes();
  }, [fetchTypes]);

  return {
    types,
    loading,
    error,
    refresh: fetchTypes
  };
}
