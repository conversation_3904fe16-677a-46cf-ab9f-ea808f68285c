/**
 * @file: useClients.ts
 * @description: Хуки для работы с клиентами
 * @dependencies: react, @supabase/supabase-js, swr
 * @created: 2024-12-26
 */

import { useState, useCallback, useEffect } from 'react';
import { useSupabaseClient } from '../context/SupabaseProvider';
import { useTenant } from '../context/TenantContext';
import useSWR from 'swr';
import {
  Client,
  ClientInsert,
  ClientUpdate,
  ClientFilters,
  ClientPagination,
  ClientSorting,
  ClientsResult,
  ClientStats,
  ClientsOverallStats,
} from '../types/clients';
import {
  getClients,
  getClientById,
  createClient,
  updateClient,
  deleteClient,
  getClientStats,
  getClientsOverallStats,
} from '../api/clients';

/**
 * Хук для работы со списком клиентов
 * 
 * @param filters Фильтры для клиентов
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Объект с данными и функциями для работы с клиентами
 */
export function useClients(
  filters?: ClientFilters,
  pagination?: ClientPagination,
  sorting?: ClientSorting
) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();

  // Создаем ключ для SWR на основе параметров
  const swrKey = tenantId 
    ? ['clients', tenantId, filters, pagination, sorting]
    : null;

  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWR<ClientsResult>(
    swrKey,
    () => getClients(supabase, filters, pagination, sorting),
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000, // 30 секунд
    }
  );

  const [isCreating, setIsCreating] = useState(false);
  const [createError, setCreateError] = useState<Error | null>(null);

  // Функция для создания клиента
  const createClientMutation = useCallback(async (clientData: Omit<ClientInsert, 'tenant_id'>) => {
    if (!tenantId) throw new Error('Tenant ID не найден');
    
    try {
      setIsCreating(true);
      setCreateError(null);
      
      const newClient = await createClient(supabase, {
        ...clientData,
        tenant_id: tenantId,
      });
      
      // Обновляем кэш
      await mutate();
      
      return newClient;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setCreateError(error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  }, [supabase, tenantId, mutate]);

  return {
    clients: data?.data || [],
    count: data?.count || 0,
    page: data?.page || 1,
    limit: data?.limit || 20,
    total_pages: data?.total_pages || 0,
    isLoading,
    error,
    refresh: mutate,
    createClient: createClientMutation,
    isCreating,
    createError,
  };
}

/**
 * Хук для работы с отдельным клиентом
 * 
 * @param id ID клиента (если не указан, то создается новый клиент)
 * @returns Объект с данными и функциями для работы с клиентом
 */
export function useClient(id?: string) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();
  
  const [client, setClient] = useState<Client | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<Error | null>(null);

  // Функция для загрузки клиента
  const fetchClient = useCallback(async () => {
    if (!id || !tenantId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const client = await getClientById(supabase, id);
      setClient(client);
    } catch (err) {
      console.error('Ошибка при загрузке клиента:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, id, tenantId]);

  // Загружаем клиента при изменении ID
  useEffect(() => {
    if (id) {
      fetchClient();
    } else {
      setClient(null);
    }
  }, [id, fetchClient]);

  // Функция для сохранения клиента (создание или обновление)
  const saveClient = useCallback(async (data: ClientInsert | ClientUpdate) => {
    if (!tenantId) return null;
    
    try {
      setIsSaving(true);
      setSaveError(null);
      
      let savedClient: Client;
      
      if (id) {
        // Обновление существующего клиента
        savedClient = await updateClient(supabase, id, data as ClientUpdate);
      } else {
        // Создание нового клиента
        savedClient = await createClient(supabase, {
          ...data as ClientInsert,
          tenant_id: tenantId,
        });
      }
      
      setClient(savedClient);
      return savedClient;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setSaveError(error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [supabase, id, tenantId]);

  // Функция для удаления клиента
  const deleteClientMutation = useCallback(async () => {
    if (!id) return false;
    
    try {
      const result = await deleteClient(supabase, id);
      if (result) {
        setClient(null);
      }
      return result;
    } catch (err) {
      console.error('Ошибка при удалении клиента:', err);
      throw err;
    }
  }, [supabase, id]);

  return {
    client,
    loading,
    error,
    isSaving,
    saveError,
    saveClient,
    deleteClient: deleteClientMutation,
    refresh: fetchClient,
  };
}

/**
 * Хук для получения статистики по клиенту
 * 
 * @param clientId ID клиента
 * @returns Статистика по клиенту
 */
export function useClientStats(clientId?: string) {
  const supabase = useSupabaseClient();

  const swrKey = clientId ? ['client-stats', clientId] : null;

  const {
    data: stats,
    error,
    isLoading,
    mutate,
  } = useSWR<ClientStats>(
    swrKey,
    () => clientId ? getClientStats(supabase, clientId) : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 минута
    }
  );

  return {
    stats,
    isLoading,
    error,
    refresh: mutate,
  };
}

/**
 * Хук для получения общей статистики по клиентам
 * 
 * @returns Общая статистика по клиентам
 */
export function useClientsOverallStats() {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();

  const swrKey = tenantId ? ['clients-overall-stats', tenantId] : null;

  const {
    data: stats,
    error,
    isLoading,
    mutate,
  } = useSWR<ClientsOverallStats>(
    swrKey,
    () => tenantId ? getClientsOverallStats(supabase, tenantId) : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 300000, // 5 минут
    }
  );

  return {
    stats,
    isLoading,
    error,
    refresh: mutate,
  };
}

/**
 * Хук для поиска клиентов
 * 
 * @param searchQuery Поисковый запрос
 * @param limit Лимит результатов
 * @returns Результаты поиска клиентов
 */
export function useClientSearch(searchQuery?: string, limit: number = 10) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();

  const [results, setResults] = useState<Client[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<Error | null>(null);

  const search = useCallback(async (query: string) => {
    if (!query.trim() || !tenantId) {
      setResults([]);
      return;
    }

    try {
      setIsSearching(true);
      setSearchError(null);

      const filters: ClientFilters = {
        search: query,
      };

      const pagination: ClientPagination = {
        page: 1,
        limit,
      };

      const result = await getClients(supabase, filters, pagination);
      setResults(result.data);
    } catch (err) {
      console.error('Ошибка при поиске клиентов:', err);
      setSearchError(err instanceof Error ? err : new Error(String(err)));
      setResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [supabase, tenantId, limit]);

  // Автоматический поиск при изменении запроса
  useEffect(() => {
    if (searchQuery) {
      search(searchQuery);
    } else {
      setResults([]);
    }
  }, [searchQuery, search]);

  const clearResults = useCallback(() => {
    setResults([]);
    setSearchError(null);
  }, []);

  return {
    results,
    isSearching,
    searchError,
    search,
    clearResults,
  };
}

/**
 * Хук для массового обновления клиентов
 * 
 * @returns Функции для массового обновления клиентов
 */
export function useBulkClientOperations() {
  const supabase = useSupabaseClient();
  const [isProcessing, setIsProcessing] = useState(false);
  const [processError, setProcessError] = useState<Error | null>(null);

  const bulkUpdate = useCallback(async (clientIds: string[], updates: Partial<ClientUpdate>) => {
    try {
      setIsProcessing(true);
      setProcessError(null);

      const results = await Promise.allSettled(
        clientIds.map(id => updateClient(supabase, id, updates))
      );

      const updated: Client[] = [];
      const errors: Array<{ client_id: string; error: string }> = [];

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          updated.push(result.value);
        } else {
          errors.push({
            client_id: clientIds[index],
            error: result.reason?.message || 'Неизвестная ошибка',
          });
        }
      });

      return { updated, errors };
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setProcessError(error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [supabase]);

  const bulkDelete = useCallback(async (clientIds: string[]) => {
    try {
      setIsProcessing(true);
      setProcessError(null);

      const results = await Promise.allSettled(
        clientIds.map(id => deleteClient(supabase, id))
      );

      const deleted: string[] = [];
      const errors: Array<{ client_id: string; error: string }> = [];

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          deleted.push(clientIds[index]);
        } else {
          errors.push({
            client_id: clientIds[index],
            error: result.reason?.message || 'Неизвестная ошибка',
          });
        }
      });

      return { deleted, errors };
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setProcessError(error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [supabase]);

  return {
    bulkUpdate,
    bulkDelete,
    isProcessing,
    processError,
  };
}
