import { useState } from 'react';
import { useAuth } from './useAuth';

/**
 * Результат операции мутации данных
 */
export interface MutationResult<T> {
  /** Данные, полученные в результате операции */
  data: T | null;
  /** Ошибка, если операция завершилась неудачно */
  error: Error | null;
}

/**
 * Хук для работы с данными (CRUD операции)
 * 
 * @param tableName Имя таблицы
 * @returns Объект с методами для создания, обновления и удаления данных
 * 
 * @example
 * ```tsx
 * // Использование хука для работы с клиентами
 * const { create, update, remove, loading } = useDataMutation<Client>('clients');
 * 
 * // Создание нового клиента
 * const handleCreate = async () => {
 *   try {
 *     const newClient = await create({
 *       name: 'Иван Иванов',
 *       email: '<EMAIL>',
 *       tenant_id: currentTenantId
 *     });
 *     console.log('Клиент создан:', newClient);
 *   } catch (error) {
 *     console.error('Ошибка создания клиента:', error);
 *   }
 * };
 * 
 * // Обновление клиента
 * const handleUpdate = async (id: string) => {
 *   await update(id, { status: 'active' });
 * };
 * 
 * // Удаление клиента
 * const handleDelete = async (id: string) => {
 *   await remove(id);
 * };
 * ```
 */
export function useDataMutation<T>(tableName: string) {
  const { supabase } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  /**
   * Создание новой записи
   * 
   * @param data Данные для создания
   * @returns Созданная запись
   */
  const create = async (data: Partial<T>): Promise<T> => {
    setLoading(true);
    setError(null);

    try {
      const { data: result, error: queryError } = await supabase
        .from(tableName)
        .insert(data)
        .select();

      if (queryError) {
        throw queryError;
      }

      return result[0] as T;
    } catch (err) {
      setError(err as Error);
      console.error(`Ошибка создания записи в ${tableName}:`, err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Обновление существующей записи
   * 
   * @param id ID записи
   * @param data Данные для обновления
   * @returns Обновленная запись
   */
  const update = async (id: string, data: Partial<T>): Promise<T> => {
    setLoading(true);
    setError(null);

    try {
      const { data: result, error: queryError } = await supabase
        .from(tableName)
        .update(data)
        .eq('id', id)
        .select();

      if (queryError) {
        throw queryError;
      }

      return result[0] as T;
    } catch (err) {
      setError(err as Error);
      console.error(`Ошибка обновления записи в ${tableName} с id ${id}:`, err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Удаление записи
   * 
   * @param id ID записи
   * @returns true, если удаление прошло успешно
   */
  const remove = async (id: string): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      const { error: queryError } = await supabase
        .from(tableName)
        .delete()
        .eq('id', id);

      if (queryError) {
        throw queryError;
      }

      return true;
    } catch (err) {
      setError(err as Error);
      console.error(`Ошибка удаления записи из ${tableName} с id ${id}:`, err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    create,
    update,
    remove,
    loading,
    error
  };
}
