/**
 * @file: useContractTemplateVersions.ts
 * @description: Хуки для работы с версиями шаблонов договоров
 * @dependencies: react, @supabase/supabase-js
 * @created: 2024-05-10
 */

import { useState, useEffect, useCallback } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { 
  ContractTemplateVersion, 
  ContractTemplateVersionInsert, 
  ContractTemplateVersionUpdate, 
  ContractTemplateVersionsResult,
  DbContractTemplateFile
} from '../types/contract-templates';
import { 
  getContractTemplateVersions, 
  getContractTemplateVersionById, 
  createContractTemplateVersion, 
  updateContractTemplateVersion, 
  deleteContractTemplateVersion,
  uploadContractTemplateFile,
  deleteContractTemplateFile
} from '../api/contract-template-versions';
import { useTenant } from '../context/TenantContext';
import { useUser } from '@supabase/auth-helpers-react';

/**
 * Хук для получения списка версий шаблона договора
 * 
 * @param templateId ID шаблона
 * @param page Номер страницы
 * @param limit Количество записей на странице
 * @returns Объект с данными и функциями для работы со списком версий
 */
export function useContractTemplateVersionsList(
  templateId: string,
  initialPage: number = 1,
  initialLimit: number = 10
) {
  const supabase = useSupabaseClient();
  
  const [page, setPage] = useState<number>(initialPage);
  const [limit, setLimit] = useState<number>(initialLimit);
  
  const [result, setResult] = useState<ContractTemplateVersionsResult>({
    data: [],
    count: 0,
    page,
    limit,
    totalPages: 0
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Функция для загрузки данных
  const fetchVersions = useCallback(async () => {
    if (!templateId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const result = await getContractTemplateVersions(supabase, templateId, page, limit);
      setResult(result);
    } catch (err) {
      console.error('Ошибка при загрузке версий шаблона договора:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, templateId, page, limit]);

  // Загружаем данные при изменении параметров
  useEffect(() => {
    fetchVersions();
  }, [fetchVersions]);

  // Функция для обновления пагинации
  const updatePagination = useCallback((newPage: number, newLimit?: number) => {
    setPage(newPage);
    if (newLimit) {
      setLimit(newLimit);
    }
  }, []);

  // Функция для перехода на следующую страницу
  const nextPage = useCallback(() => {
    if (page < result.totalPages) {
      setPage(page + 1);
    }
  }, [page, result.totalPages]);

  // Функция для перехода на предыдущую страницу
  const prevPage = useCallback(() => {
    if (page > 1) {
      setPage(page - 1);
    }
  }, [page]);

  return {
    versions: result.data,
    count: result.count,
    page: result.page,
    limit: result.limit,
    totalPages: result.totalPages,
    loading,
    error,
    updatePagination,
    refresh: fetchVersions,
    nextPage,
    prevPage
  };
}

/**
 * Хук для работы с отдельной версией шаблона договора
 * 
 * @param id ID версии шаблона (если не указан, то создается новая версия)
 * @param templateId ID шаблона (обязателен при создании новой версии)
 * @returns Объект с данными и функциями для работы с версией шаблона
 */
export function useContractTemplateVersion(id?: string, templateId?: string) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();
  const user = useUser();
  
  const [version, setVersion] = useState<ContractTemplateVersion | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<Error | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<Error | null>(null);

  // Функция для загрузки версии шаблона
  const fetchVersion = useCallback(async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const version = await getContractTemplateVersionById(supabase, id);
      setVersion(version);
    } catch (err) {
      console.error('Ошибка при загрузке версии шаблона договора:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, id]);

  // Загружаем версию шаблона при изменении ID
  useEffect(() => {
    if (id) {
      fetchVersion();
    } else {
      setVersion(null);
    }
  }, [id, fetchVersion]);

  // Функция для сохранения версии шаблона (создание или обновление)
  const saveVersion = useCallback(async (data: Partial<ContractTemplateVersionInsert | ContractTemplateVersionUpdate>) => {
    if (!tenantId) return null;
    if (!id && !templateId) {
      setSaveError(new Error('Для создания новой версии необходимо указать ID шаблона'));
      return null;
    }
    
    try {
      setIsSaving(true);
      setSaveError(null);
      
      // Добавляем tenant_id и created_by к данным
      const versionData = { 
        ...data, 
        tenant_id: tenantId,
        created_by: user?.id
      };
      
      let savedVersion: ContractTemplateVersion;
      
      if (id) {
        // Обновляем существующую версию
        savedVersion = await updateContractTemplateVersion(supabase, id, versionData as ContractTemplateVersionUpdate);
      } else {
        // Создаем новую версию
        savedVersion = await createContractTemplateVersion(supabase, {
          ...versionData,
          template_id: templateId!
        } as ContractTemplateVersionInsert);
      }
      
      setVersion(savedVersion);
      return savedVersion;
    } catch (err) {
      console.error('Ошибка при сохранении версии шаблона договора:', err);
      setSaveError(err instanceof Error ? err : new Error(String(err)));
      return null;
    } finally {
      setIsSaving(false);
    }
  }, [supabase, id, templateId, tenantId, user]);

  // Функция для удаления версии шаблона
  const removeVersion = useCallback(async () => {
    if (!id) return false;
    
    try {
      setIsSaving(true);
      setSaveError(null);
      
      const success = await deleteContractTemplateVersion(supabase, id);
      
      if (success) {
        setVersion(null);
      }
      
      return success;
    } catch (err) {
      console.error('Ошибка при удалении версии шаблона договора:', err);
      setSaveError(err instanceof Error ? err : new Error(String(err)));
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [supabase, id]);

  // Функция для загрузки файла для версии шаблона
  const uploadFile = useCallback(async (file: File) => {
    if (!id || !tenantId) {
      setUploadError(new Error('Для загрузки файла необходимо указать ID версии шаблона'));
      return null;
    }
    
    try {
      setIsUploading(true);
      setUploadError(null);
      
      const uploadedFile = await uploadContractTemplateFile(supabase, id, tenantId, file);
      
      // Обновляем список файлов в версии
      setVersion(prev => {
        if (!prev) return prev;
        
        const files = prev.files || [];
        return {
          ...prev,
          files: [...files, uploadedFile]
        };
      });
      
      return uploadedFile;
    } catch (err) {
      console.error('Ошибка при загрузке файла для версии шаблона договора:', err);
      setUploadError(err instanceof Error ? err : new Error(String(err)));
      return null;
    } finally {
      setIsUploading(false);
    }
  }, [supabase, id, tenantId]);

  // Функция для удаления файла версии шаблона
  const removeFile = useCallback(async (fileId: string) => {
    try {
      setIsUploading(true);
      setUploadError(null);
      
      const success = await deleteContractTemplateFile(supabase, fileId);
      
      if (success) {
        // Обновляем список файлов в версии
        setVersion(prev => {
          if (!prev || !prev.files) return prev;
          
          return {
            ...prev,
            files: prev.files.filter(file => file.id !== fileId)
          };
        });
      }
      
      return success;
    } catch (err) {
      console.error('Ошибка при удалении файла версии шаблона договора:', err);
      setUploadError(err instanceof Error ? err : new Error(String(err)));
      return false;
    } finally {
      setIsUploading(false);
    }
  }, [supabase]);

  return {
    version,
    loading,
    error,
    isSaving,
    saveError,
    isUploading,
    uploadError,
    saveVersion,
    removeVersion,
    uploadFile,
    removeFile,
    refresh: fetchVersion
  };
}
