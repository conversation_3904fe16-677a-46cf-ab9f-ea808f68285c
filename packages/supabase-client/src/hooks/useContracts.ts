/**
 * @file: useContracts.ts
 * @description: Хуки для работы с договорами
 * @dependencies: react, @supabase/supabase-js
 * @created: 2024-05-10
 */

import { useState, useEffect, useCallback } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { 
  Contract, 
  ContractInsert, 
  ContractUpdate, 
  ContractFilters, 
  ContractPagination, 
  ContractSorting,
  ContractsResult
} from '../types/contracts';
import { 
  getContracts, 
  getContractById, 
  createContract, 
  updateContract, 
  deleteContract 
} from '../api/contracts';
import { useTenant } from '../context/TenantContext';

/**
 * Хук для получения списка договоров с фильтрацией, пагинацией и сортировкой
 * 
 * @param filters Фильтры для договоров
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Объект с данными и функциями для работы со списком договоров
 */
export function useContractsList(
  initialFilters?: ContractFilters,
  initialPagination?: ContractPagination,
  initialSorting?: ContractSorting
) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();
  
  const [filters, setFilters] = useState<ContractFilters>(initialFilters || {});
  const [pagination, setPagination] = useState<ContractPagination>(initialPagination || { page: 1, limit: 10 });
  const [sorting, setSorting] = useState<ContractSorting | undefined>(initialSorting);
  
  const [result, setResult] = useState<ContractsResult>({
    data: [],
    count: 0,
    page: pagination.page,
    limit: pagination.limit,
    totalPages: 0
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Функция для загрузки данных
  const fetchContracts = useCallback(async () => {
    if (!tenantId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // Добавляем tenant_id к фильтрам
      const contractsFilters = { ...filters, tenant_id: tenantId };
      
      const result = await getContracts(supabase, contractsFilters, pagination, sorting);
      setResult(result);
    } catch (err) {
      console.error('Ошибка при загрузке договоров:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, tenantId, filters, pagination, sorting]);

  // Загружаем данные при изменении параметров
  useEffect(() => {
    fetchContracts();
  }, [fetchContracts]);

  // Функция для обновления фильтров
  const updateFilters = useCallback((newFilters: ContractFilters) => {
    setFilters(newFilters);
    // Сбрасываем пагинацию при изменении фильтров
    setPagination(prev => ({ ...prev, page: 1 }));
  }, []);

  // Функция для обновления пагинации
  const updatePagination = useCallback((newPagination: Partial<ContractPagination>) => {
    setPagination(prev => ({ ...prev, ...newPagination }));
  }, []);

  // Функция для обновления сортировки
  const updateSorting = useCallback((newSorting: ContractSorting) => {
    setSorting(newSorting);
  }, []);

  // Функция для перехода на следующую страницу
  const nextPage = useCallback(() => {
    if (pagination.page < result.totalPages) {
      setPagination(prev => ({ ...prev, page: prev.page + 1 }));
    }
  }, [pagination.page, result.totalPages]);

  // Функция для перехода на предыдущую страницу
  const prevPage = useCallback(() => {
    if (pagination.page > 1) {
      setPagination(prev => ({ ...prev, page: prev.page - 1 }));
    }
  }, [pagination.page]);

  return {
    contracts: result.data,
    count: result.count,
    page: result.page,
    limit: result.limit,
    totalPages: result.totalPages,
    loading,
    error,
    filters,
    updateFilters,
    pagination,
    updatePagination,
    sorting,
    updateSorting,
    refresh: fetchContracts,
    nextPage,
    prevPage
  };
}

/**
 * Хук для работы с отдельным договором
 * 
 * @param id ID договора (если не указан, то создается новый договор)
 * @returns Объект с данными и функциями для работы с договором
 */
export function useContract(id?: string) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();
  
  const [contract, setContract] = useState<Contract | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<Error | null>(null);

  // Функция для загрузки договора
  const fetchContract = useCallback(async () => {
    if (!id || !tenantId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const contract = await getContractById(supabase, id);
      setContract(contract);
    } catch (err) {
      console.error('Ошибка при загрузке договора:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, id, tenantId]);

  // Загружаем договор при изменении ID
  useEffect(() => {
    if (id) {
      fetchContract();
    } else {
      setContract(null);
    }
  }, [id, fetchContract]);

  // Функция для сохранения договора (создание или обновление)
  const saveContract = useCallback(async (data: ContractInsert | ContractUpdate) => {
    if (!tenantId) return null;
    
    try {
      setIsSaving(true);
      setSaveError(null);
      
      // Добавляем tenant_id к данным
      const contractData = { ...data, tenant_id: tenantId };
      
      let savedContract: Contract;
      
      if (id) {
        // Обновляем существующий договор
        savedContract = await updateContract(supabase, id, contractData as ContractUpdate);
      } else {
        // Создаем новый договор
        savedContract = await createContract(supabase, contractData as ContractInsert);
      }
      
      setContract(savedContract);
      return savedContract;
    } catch (err) {
      console.error('Ошибка при сохранении договора:', err);
      setSaveError(err instanceof Error ? err : new Error(String(err)));
      return null;
    } finally {
      setIsSaving(false);
    }
  }, [supabase, id, tenantId]);

  // Функция для удаления договора
  const removeContract = useCallback(async () => {
    if (!id) return false;
    
    try {
      setIsSaving(true);
      setSaveError(null);
      
      const success = await deleteContract(supabase, id);
      
      if (success) {
        setContract(null);
      }
      
      return success;
    } catch (err) {
      console.error('Ошибка при удалении договора:', err);
      setSaveError(err instanceof Error ? err : new Error(String(err)));
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [supabase, id]);

  return {
    contract,
    loading,
    error,
    isSaving,
    saveError,
    saveContract,
    removeContract,
    refresh: fetchContract
  };
}
