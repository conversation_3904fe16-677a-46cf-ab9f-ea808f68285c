import { useEffect, useState } from 'react';
import { useAuth } from './useAuth';

/**
 * Опции для хука useTenantData
 */
export interface TenantDataOptions {
  /** Колонки для выборки (формат Supabase) */
  columns?: string;
  /** Дополнительный фильтр (формат Supabase) */
  filter?: string;
  /** ID арендатора (если не указан, берется из метаданных пользователя) */
  tenantId?: string;
  /** Флаг активации запроса */
  enabled?: boolean;
}

/**
 * Хук для получения данных с учетом tenant_id
 * 
 * Автоматически применяет фильтрацию по tenant_id из текущего пользователя
 * или из переданного параметра
 * 
 * @param tableName Имя таблицы
 * @param options Опции запроса
 * @returns Объект с данными, ошибкой, статусом загрузки и функцией обновления
 * 
 * @example
 * ```tsx
 * // Получение всех клиентов текущего арендатора
 * const { data: clients, loading } = useTenantData<Client>('clients');
 * 
 * // Получение клиентов с фильтрацией
 * const { data: activeClients } = useTenantData<Client>('clients', {
 *   filter: 'status.eq.active',
 *   columns: 'id, name, email, status'
 * });
 * ```
 */
export function useTenantData<T>(
  tableName: string,
  options?: TenantDataOptions
) {
  const { user, supabase } = useAuth();
  const [data, setData] = useState<T[] | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(true);

  // Получаем tenant_id из опций или из метаданных пользователя
  const tenantId = options?.tenantId || user?.user_metadata?.tenant_id;
  const enabled = options?.enabled !== undefined ? options.enabled : true;

  useEffect(() => {
    // Если запрос отключен, не выполняем его
    if (!enabled) {
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        // Формируем базовый запрос
        let query = supabase
          .from(tableName)
          .select(options?.columns || '*');

        // Применяем фильтр по tenant_id, если он есть
        if (tenantId) {
          query = query.eq('tenant_id', tenantId);
        }

        // Применяем дополнительный фильтр, если он есть
        if (options?.filter) {
          query = query.or(options.filter);
        }

        // Выполняем запрос
        const { data: result, error: queryError } = await query;

        if (queryError) {
          throw queryError;
        }

        setData(result as T[]);
      } catch (err) {
        setError(err as Error);
        console.error(`Ошибка получения данных из ${tableName}:`, err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [supabase, tableName, tenantId, options?.columns, options?.filter, enabled]);

  /**
   * Функция для принудительного обновления данных
   */
  const refetch = async () => {
    if (!enabled) return;
    
    setLoading(true);
    setError(null);

    try {
      let query = supabase
        .from(tableName)
        .select(options?.columns || '*');

      if (tenantId) {
        query = query.eq('tenant_id', tenantId);
      }

      if (options?.filter) {
        query = query.or(options.filter);
      }

      const { data: result, error: queryError } = await query;

      if (queryError) {
        throw queryError;
      }

      setData(result as T[]);
    } catch (err) {
      setError(err as Error);
      console.error(`Ошибка обновления данных из ${tableName}:`, err);
    } finally {
      setLoading(false);
    }
  };

  return { data, error, loading, refetch };
}
