/**
 * @file: useContractDocuments.ts
 * @description: Хуки для работы с документами договоров
 * @dependencies: react, @supabase/supabase-js
 * @created: 2024-05-10
 */

import { useState, useEffect, useCallback } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { 
  ContractDocument, 
  ContractDocumentsResult
} from '../types/contract-templates';
import { 
  getContractDocuments, 
  getContractDocumentById, 
  generateContractDocument, 
  deleteContractDocument
} from '../api/contract-documents';
import { useUser } from '@supabase/auth-helpers-react';

/**
 * Хук для получения списка документов договора
 * 
 * @param contractId ID договора
 * @param page Номер страницы
 * @param limit Количество записей на странице
 * @returns Объект с данными и функциями для работы со списком документов
 */
export function useContractDocumentsList(
  contractId: string,
  initialPage: number = 1,
  initialLimit: number = 10
) {
  const supabase = useSupabaseClient();
  
  const [page, setPage] = useState<number>(initialPage);
  const [limit, setLimit] = useState<number>(initialLimit);
  
  const [result, setResult] = useState<ContractDocumentsResult>({
    data: [],
    count: 0,
    page,
    limit,
    totalPages: 0
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Функция для загрузки данных
  const fetchDocuments = useCallback(async () => {
    if (!contractId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const result = await getContractDocuments(supabase, contractId, page, limit);
      setResult(result);
    } catch (err) {
      console.error('Ошибка при загрузке документов договора:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, contractId, page, limit]);

  // Загружаем данные при изменении параметров
  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  // Функция для обновления пагинации
  const updatePagination = useCallback((newPage: number, newLimit?: number) => {
    setPage(newPage);
    if (newLimit) {
      setLimit(newLimit);
    }
  }, []);

  // Функция для перехода на следующую страницу
  const nextPage = useCallback(() => {
    if (page < result.totalPages) {
      setPage(page + 1);
    }
  }, [page, result.totalPages]);

  // Функция для перехода на предыдущую страницу
  const prevPage = useCallback(() => {
    if (page > 1) {
      setPage(page - 1);
    }
  }, [page]);

  return {
    documents: result.data,
    count: result.count,
    page: result.page,
    limit: result.limit,
    totalPages: result.totalPages,
    loading,
    error,
    updatePagination,
    refresh: fetchDocuments,
    nextPage,
    prevPage
  };
}

/**
 * Хук для работы с отдельным документом договора
 * 
 * @param id ID документа
 * @returns Объект с данными и функциями для работы с документом
 */
export function useContractDocument(id?: string) {
  const supabase = useSupabaseClient();
  
  const [document, setDocument] = useState<ContractDocument | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<Error | null>(null);

  // Функция для загрузки документа
  const fetchDocument = useCallback(async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const document = await getContractDocumentById(supabase, id);
      setDocument(document);
    } catch (err) {
      console.error('Ошибка при загрузке документа договора:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, id]);

  // Загружаем документ при изменении ID
  useEffect(() => {
    if (id) {
      fetchDocument();
    } else {
      setDocument(null);
    }
  }, [id, fetchDocument]);

  // Функция для удаления документа
  const removeDocument = useCallback(async () => {
    if (!id) return false;
    
    try {
      setIsDeleting(true);
      setDeleteError(null);
      
      const success = await deleteContractDocument(supabase, id);
      
      if (success) {
        setDocument(null);
      }
      
      return success;
    } catch (err) {
      console.error('Ошибка при удалении документа договора:', err);
      setDeleteError(err instanceof Error ? err : new Error(String(err)));
      return false;
    } finally {
      setIsDeleting(false);
    }
  }, [supabase, id]);

  return {
    document,
    loading,
    error,
    isDeleting,
    deleteError,
    removeDocument,
    refresh: fetchDocument
  };
}

/**
 * Хук для генерации документа договора
 * 
 * @returns Объект с функциями для генерации документа
 */
export function useContractDocumentGenerator() {
  const supabase = useSupabaseClient();
  const user = useUser();
  
  const [isGenerating, setIsGenerating] = useState(false);
  const [generateError, setGenerateError] = useState<Error | null>(null);

  // Функция для генерации документа
  const generateDocument = useCallback(async (contractId: string, templateVersionId: string) => {
    if (!user?.id) {
      setGenerateError(new Error('Для генерации документа необходимо авторизоваться'));
      return null;
    }
    
    try {
      setIsGenerating(true);
      setGenerateError(null);
      
      const document = await generateContractDocument(supabase, contractId, templateVersionId, user.id);
      return document;
    } catch (err) {
      console.error('Ошибка при генерации документа договора:', err);
      setGenerateError(err instanceof Error ? err : new Error(String(err)));
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, [supabase, user]);

  return {
    isGenerating,
    generateError,
    generateDocument
  };
}
