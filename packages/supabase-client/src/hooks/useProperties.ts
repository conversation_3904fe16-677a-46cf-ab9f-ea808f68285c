/**
 * @file: useProperties.ts
 * @description: Хуки для работы с объектами недвижимости
 * @dependencies: react, @supabase/supabase-js, swr
 * @created: 2024-12-26
 */

import { useState, useCallback, useEffect } from 'react';
import { useSupabaseClient } from '../context/SupabaseProvider';
import { useTenant } from '../context/TenantContext';
import useSWR from 'swr';
import {
  Complex,
  ComplexInsert,
  ComplexUpdate,
  ComplexFilters,
  PropertyPagination,
  ComplexSorting,
  ComplexesResult,
  ComplexStats,
  Building,
  BuildingInsert,
  BuildingUpdate,
  BuildingFilters,
  BuildingSorting,
  BuildingsResult,
  BuildingStats,
  Apartment,
  ApartmentInsert,
  ApartmentUpdate,
  ApartmentFilters,
  ApartmentSorting,
  ApartmentsResult,
  BulkApartmentCreate,
  BulkApartmentResult,
} from '../types/properties';
import {
  getComplexes,
  getComplexById,
  createComplex,
  updateComplex,
  deleteComplex,
  getComplexStats,
} from '../api/complexes';
import {
  getBuildings,
  getBuildingById,
  createBuilding,
  updateBuilding,
  deleteBuilding,
  getBuildingStats,
} from '../api/buildings';
import {
  getApartments,
  getApartmentById,
  createApartment,
  createApartmentsBulk,
  updateApartment,
  deleteApartment,
  updateApartmentStatus,
} from '../api/apartments';

/**
 * Хук для работы со списком жилых комплексов
 * 
 * @param filters Фильтры для жилых комплексов
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Объект с данными и функциями для работы с жилыми комплексами
 */
export function useComplexes(
  filters?: ComplexFilters,
  pagination?: PropertyPagination,
  sorting?: ComplexSorting
) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();

  // Создаем ключ для SWR на основе параметров
  const swrKey = tenantId 
    ? ['complexes', tenantId, filters, pagination, sorting]
    : null;

  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWR<ComplexesResult>(
    swrKey,
    () => getComplexes(supabase, filters, pagination, sorting),
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000, // 30 секунд
    }
  );

  const [isCreating, setIsCreating] = useState(false);
  const [createError, setCreateError] = useState<Error | null>(null);

  // Функция для создания жилого комплекса
  const createComplexMutation = useCallback(async (complexData: Omit<ComplexInsert, 'tenant_id'>) => {
    if (!tenantId) throw new Error('Tenant ID не найден');
    
    try {
      setIsCreating(true);
      setCreateError(null);
      
      const newComplex = await createComplex(supabase, {
        ...complexData,
        tenant_id: tenantId,
      });
      
      // Обновляем кэш
      await mutate();
      
      return newComplex;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setCreateError(error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  }, [supabase, tenantId, mutate]);

  return {
    complexes: data?.data || [],
    count: data?.count || 0,
    page: data?.page || 1,
    limit: data?.limit || 20,
    total_pages: data?.total_pages || 0,
    isLoading,
    error,
    refresh: mutate,
    createComplex: createComplexMutation,
    isCreating,
    createError,
  };
}

/**
 * Хук для работы с отдельным жилым комплексом
 * 
 * @param id ID жилого комплекса (если не указан, то создается новый комплекс)
 * @returns Объект с данными и функциями для работы с жилым комплексом
 */
export function useComplex(id?: string) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();
  
  const [complex, setComplex] = useState<Complex | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<Error | null>(null);

  // Функция для загрузки жилого комплекса
  const fetchComplex = useCallback(async () => {
    if (!id || !tenantId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const complex = await getComplexById(supabase, id);
      setComplex(complex);
    } catch (err) {
      console.error('Ошибка при загрузке жилого комплекса:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, id, tenantId]);

  // Загружаем комплекс при изменении ID
  useEffect(() => {
    if (id) {
      fetchComplex();
    } else {
      setComplex(null);
    }
  }, [id, fetchComplex]);

  // Функция для сохранения жилого комплекса (создание или обновление)
  const saveComplex = useCallback(async (data: ComplexInsert | ComplexUpdate) => {
    if (!tenantId) return null;
    
    try {
      setIsSaving(true);
      setSaveError(null);
      
      let savedComplex: Complex;
      
      if (id) {
        // Обновление существующего комплекса
        savedComplex = await updateComplex(supabase, id, data as ComplexUpdate);
      } else {
        // Создание нового комплекса
        savedComplex = await createComplex(supabase, {
          ...data as ComplexInsert,
          tenant_id: tenantId,
        });
      }
      
      setComplex(savedComplex);
      return savedComplex;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setSaveError(error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [supabase, id, tenantId]);

  // Функция для удаления жилого комплекса
  const deleteComplexMutation = useCallback(async () => {
    if (!id) return false;
    
    try {
      const result = await deleteComplex(supabase, id);
      if (result) {
        setComplex(null);
      }
      return result;
    } catch (err) {
      console.error('Ошибка при удалении жилого комплекса:', err);
      throw err;
    }
  }, [supabase, id]);

  return {
    complex,
    loading,
    error,
    isSaving,
    saveError,
    saveComplex,
    deleteComplex: deleteComplexMutation,
    refresh: fetchComplex,
  };
}

/**
 * Хук для работы со списком зданий
 * 
 * @param filters Фильтры для зданий
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Объект с данными и функциями для работы со зданиями
 */
export function useBuildings(
  filters?: BuildingFilters,
  pagination?: PropertyPagination,
  sorting?: BuildingSorting
) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();

  // Создаем ключ для SWR на основе параметров
  const swrKey = tenantId 
    ? ['buildings', tenantId, filters, pagination, sorting]
    : null;

  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWR<BuildingsResult>(
    swrKey,
    () => getBuildings(supabase, filters, pagination, sorting),
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000, // 30 секунд
    }
  );

  const [isCreating, setIsCreating] = useState(false);
  const [createError, setCreateError] = useState<Error | null>(null);

  // Функция для создания здания
  const createBuildingMutation = useCallback(async (buildingData: Omit<BuildingInsert, 'tenant_id'>) => {
    if (!tenantId) throw new Error('Tenant ID не найден');
    
    try {
      setIsCreating(true);
      setCreateError(null);
      
      const newBuilding = await createBuilding(supabase, {
        ...buildingData,
        tenant_id: tenantId,
      });
      
      // Обновляем кэш
      await mutate();
      
      return newBuilding;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setCreateError(error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  }, [supabase, tenantId, mutate]);

  return {
    buildings: data?.data || [],
    count: data?.count || 0,
    page: data?.page || 1,
    limit: data?.limit || 20,
    total_pages: data?.total_pages || 0,
    isLoading,
    error,
    refresh: mutate,
    createBuilding: createBuildingMutation,
    isCreating,
    createError,
  };
}

/**
 * Хук для работы со списком квартир
 * 
 * @param filters Фильтры для квартир
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Объект с данными и функциями для работы с квартирами
 */
export function useApartments(
  filters?: ApartmentFilters,
  pagination?: PropertyPagination,
  sorting?: ApartmentSorting
) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();

  // Создаем ключ для SWR на основе параметров
  const swrKey = tenantId 
    ? ['apartments', tenantId, filters, pagination, sorting]
    : null;

  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWR<ApartmentsResult>(
    swrKey,
    () => getApartments(supabase, filters, pagination, sorting),
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000, // 30 секунд
    }
  );

  const [isCreating, setIsCreating] = useState(false);
  const [createError, setCreateError] = useState<Error | null>(null);

  // Функция для создания квартиры
  const createApartmentMutation = useCallback(async (apartmentData: Omit<ApartmentInsert, 'tenant_id'>) => {
    if (!tenantId) throw new Error('Tenant ID не найден');
    
    try {
      setIsCreating(true);
      setCreateError(null);
      
      const newApartment = await createApartment(supabase, {
        ...apartmentData,
        tenant_id: tenantId,
      });
      
      // Обновляем кэш
      await mutate();
      
      return newApartment;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setCreateError(error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  }, [supabase, tenantId, mutate]);

  // Функция для массового создания квартир
  const createApartmentsBulkMutation = useCallback(async (bulkData: BulkApartmentCreate) => {
    try {
      setIsCreating(true);
      setCreateError(null);
      
      const result = await createApartmentsBulk(supabase, bulkData);
      
      // Обновляем кэш
      await mutate();
      
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setCreateError(error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  }, [supabase, mutate]);

  return {
    apartments: data?.data || [],
    count: data?.count || 0,
    page: data?.page || 1,
    limit: data?.limit || 20,
    total_pages: data?.total_pages || 0,
    isLoading,
    error,
    refresh: mutate,
    createApartment: createApartmentMutation,
    createApartmentsBulk: createApartmentsBulkMutation,
    isCreating,
    createError,
  };
}

/**
 * Хук для получения статистики по жилому комплексу
 * 
 * @param complexId ID жилого комплекса
 * @returns Статистика по жилому комплексу
 */
export function useComplexStats(complexId?: string) {
  const supabase = useSupabaseClient();

  const swrKey = complexId ? ['complex-stats', complexId] : null;

  const {
    data: stats,
    error,
    isLoading,
    mutate,
  } = useSWR<ComplexStats>(
    swrKey,
    () => complexId ? getComplexStats(supabase, complexId) : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 минута
    }
  );

  return {
    stats,
    isLoading,
    error,
    refresh: mutate,
  };
}
