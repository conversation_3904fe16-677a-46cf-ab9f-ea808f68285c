/**
 * Утилиты для работы с пользователями
 * 
 * @module utils/userUtils
 */

import { SupabaseClient } from '../supabase';
import { Database } from '../types';

/**
 * Тип пользователя
 */
export type User = Database['public']['Tables']['users']['Row'];

/**
 * Получает список пользователей арендатора
 * 
 * @param supabase Клиент Supabase
 * @param tenantId ID арендатора
 * @returns Список пользователей
 * 
 * @example
 * ```ts
 * const users = await getTenantUsers(supabase, '123');
 * console.log(`Найдено ${users.length} пользователей`);
 * ```
 */
export async function getTenantUsers(supabase: SupabaseClient, tenantId: string) {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('tenant_id', tenantId);

  if (error) {
    throw error;
  }

  return data;
}

/**
 * Получает пользователя по ID
 * 
 * @param supabase Клиент Supabase
 * @param id ID пользователя
 * @returns Пользователь или null, если не найден
 * 
 * @example
 * ```ts
 * const user = await getUserById(supabase, '123');
 * if (user) {
 *   console.log(`Найден пользователь: ${user.email}`);
 * }
 * ```
 */
export async function getUserById(supabase: SupabaseClient, id: string) {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // Запись не найдена
      return null;
    }
    throw error;
  }

  return data;
}

/**
 * Создает нового пользователя
 * 
 * @param supabase Клиент Supabase
 * @param user Данные пользователя
 * @returns Созданный пользователь
 * 
 * @example
 * ```ts
 * const newUser = await createUser(supabase, {
 *   id: authUser.id, // ID из auth.users
 *   email: '<EMAIL>',
 *   full_name: 'Иван Иванов',
 *   tenant_id: '123',
 *   role: 'manager'
 * });
 * ```
 */
export async function createUser(
  supabase: SupabaseClient,
  user: Omit<User, 'created_at'>
) {
  const { data, error } = await supabase
    .from('users')
    .insert(user)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * Обновляет данные пользователя
 * 
 * @param supabase Клиент Supabase
 * @param id ID пользователя
 * @param user Данные для обновления
 * @returns Обновленный пользователь
 * 
 * @example
 * ```ts
 * const updatedUser = await updateUser(supabase, '123', {
 *   full_name: 'Новое Имя',
 *   role: 'admin'
 * });
 * ```
 */
export async function updateUser(
  supabase: SupabaseClient,
  id: string,
  user: Partial<Omit<User, 'id' | 'created_at'>>
) {
  const { data, error } = await supabase
    .from('users')
    .update(user)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * Удаляет пользователя
 * 
 * @param supabase Клиент Supabase
 * @param id ID пользователя
 * @returns true, если удаление прошло успешно
 * 
 * @example
 * ```ts
 * const success = await deleteUser(supabase, '123');
 * if (success) {
 *   console.log('Пользователь успешно удален');
 * }
 * ```
 */
export async function deleteUser(supabase: SupabaseClient, id: string) {
  const { error } = await supabase
    .from('users')
    .delete()
    .eq('id', id);

  if (error) {
    throw error;
  }

  return true;
}
