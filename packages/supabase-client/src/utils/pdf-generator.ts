/**
 * @file: pdf-generator.ts
 * @description: Утилиты для генерации PDF документов
 * @dependencies: @react-pdf/renderer
 * @created: 2024-12-26
 */

import { pdf } from '@react-pdf/renderer';
import { ContractPDFDocument } from '../components/pdf/ContractPDFDocument';
import { TemplateData, replaceTemplateVariables } from './template-variables';

/**
 * Интерфейс для результата генерации PDF
 */
export interface PDFGenerationResult {
  blob: Blob;
  buffer: ArrayBuffer;
  fileName: string;
}

/**
 * Опции для генерации PDF
 */
export interface PDFGenerationOptions {
  templateData: TemplateData;
  templateContent?: string;
  fileName?: string;
}

/**
 * Генерирует PDF документ договора
 */
export async function generateContractPDF(
  options: PDFGenerationOptions
): Promise<PDFGenerationResult> {
  try {
    const { templateData, templateContent, fileName } = options;
    
    // Создаем React компонент для PDF
    const pdfDocument = ContractPDFDocument({
      templateData,
      templateContent: templateContent ? 
        replaceTemplateVariables(templateContent, templateData) : 
        undefined,
    });
    
    // Генерируем PDF
    const blob = await pdf(pdfDocument).toBlob();
    const buffer = await blob.arrayBuffer();
    
    // Генерируем имя файла
    const generatedFileName = fileName || generateFileName(templateData);
    
    return {
      blob,
      buffer,
      fileName: generatedFileName,
    };
  } catch (error) {
    console.error('Ошибка при генерации PDF:', error);
    throw new Error(`Не удалось сгенерировать PDF: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`);
  }
}

/**
 * Генерирует имя файла для PDF документа
 */
function generateFileName(templateData: TemplateData): string {
  const contractNumber = templateData.contract.number || 'unknown';
  const currentDate = new Date().toISOString().split('T')[0];
  
  return `contract_${contractNumber}_${currentDate}.pdf`;
}

/**
 * Конвертирует Blob в Base64 строку
 */
export function blobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      // Убираем префикс data:application/pdf;base64,
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

/**
 * Конвертирует ArrayBuffer в Base64 строку
 */
export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

/**
 * Создает URL для скачивания PDF
 */
export function createDownloadURL(blob: Blob): string {
  return URL.createObjectURL(blob);
}

/**
 * Освобождает URL созданный для скачивания
 */
export function revokeDownloadURL(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * Скачивает PDF файл в браузере
 */
export function downloadPDF(blob: Blob, fileName: string): void {
  const url = createDownloadURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  revokeDownloadURL(url);
}

/**
 * Проверяет, поддерживается ли генерация PDF в текущем окружении
 */
export function isPDFGenerationSupported(): boolean {
  try {
    // Проверяем наличие необходимых API
    return (
      typeof window !== 'undefined' &&
      typeof Blob !== 'undefined' &&
      typeof URL !== 'undefined' &&
      typeof FileReader !== 'undefined'
    );
  } catch {
    return false;
  }
}

/**
 * Получает MIME тип для PDF
 */
export function getPDFMimeType(): string {
  return 'application/pdf';
}

/**
 * Валидирует размер сгенерированного PDF
 */
export function validatePDFSize(blob: Blob, maxSizeMB = 10): boolean {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return blob.size <= maxSizeBytes;
}

/**
 * Получает информацию о сгенерированном PDF
 */
export function getPDFInfo(blob: Blob): {
  size: number;
  sizeFormatted: string;
  type: string;
} {
  const size = blob.size;
  const sizeFormatted = formatFileSize(size);
  const type = blob.type;
  
  return {
    size,
    sizeFormatted,
    type,
  };
}

/**
 * Форматирует размер файла в читаемый формат
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Создает превью PDF (первая страница как изображение)
 * Требует дополнительных библиотек для работы
 */
export async function generatePDFPreview(blob: Blob): Promise<string | null> {
  try {
    // Эта функция требует дополнительной библиотеки типа pdf-lib или pdf2pic
    // Пока возвращаем null, можно реализовать позже
    console.warn('PDF preview generation not implemented yet');
    return null;
  } catch (error) {
    console.error('Ошибка при создании превью PDF:', error);
    return null;
  }
}
