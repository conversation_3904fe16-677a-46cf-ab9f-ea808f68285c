/**
 * @file: template-variables.ts
 * @description: Утилиты для работы с переменными в шаблонах договоров
 * @dependencies: none
 * @created: 2024-12-26
 */

import { Contract } from '../types/contracts';

/**
 * Интерфейс для данных, используемых в шаблонах
 */
export interface TemplateData {
  // Данные договора
  contract: {
    number: string;
    signed_date: string;
    start_date: string;
    end_date: string;
    total_amount: string;
    initial_payment: string;
    monthly_payment: string;
    status: string;
  };
  
  // Данные клиента
  client: {
    full_name: string;
    first_name: string;
    last_name: string;
    middle_name: string;
    passport_number: string;
    passport_issued_by: string;
    passport_issued_date: string;
    address: string;
    phone: string;
    email: string;
  };
  
  // Данные объекта недвижимости
  property: {
    complex_name: string;
    building_name: string;
    apartment_number: string;
    floor: string;
    rooms: string;
    area: string;
    price: string;
  };
  
  // Системные данные
  system: {
    current_date: string;
    current_year: string;
    current_month: string;
    current_day: string;
  };
}

/**
 * Преобразует данные договора в формат для шаблона
 */
export function contractToTemplateData(contract: Contract): TemplateData {
  const currentDate = new Date();
  
  return {
    contract: {
      number: contract.number || '',
      signed_date: contract.signed_date ? formatDate(new Date(contract.signed_date)) : '',
      start_date: contract.start_date ? formatDate(new Date(contract.start_date)) : '',
      end_date: contract.end_date ? formatDate(new Date(contract.end_date)) : '',
      total_amount: formatCurrency(contract.total_amount || 0),
      initial_payment: formatCurrency(contract.initial_payment || 0),
      monthly_payment: formatCurrency(contract.monthly_payment || 0),
      status: getContractStatusText(contract.status),
    },
    
    client: {
      full_name: contract.client_name || '',
      first_name: contract.clients?.first_name || '',
      last_name: contract.clients?.last_name || '',
      middle_name: contract.clients?.middle_name || '',
      passport_number: contract.clients?.passport_number || '',
      passport_issued_by: contract.clients?.passport_issued_by || '',
      passport_issued_date: contract.clients?.passport_issued_date ? 
        formatDate(new Date(contract.clients.passport_issued_date)) : '',
      address: contract.clients?.address || '',
      phone: contract.clients?.phone || '',
      email: contract.clients?.email || '',
    },
    
    property: {
      complex_name: contract.complex_name || '',
      building_name: contract.building_name || '',
      apartment_number: contract.apartment_number || '',
      floor: contract.apartments?.floor?.toString() || '',
      rooms: contract.apartments?.rooms?.toString() || '',
      area: contract.apartments?.area?.toString() || '',
      price: formatCurrency(contract.apartments?.price || 0),
    },
    
    system: {
      current_date: formatDate(currentDate),
      current_year: currentDate.getFullYear().toString(),
      current_month: (currentDate.getMonth() + 1).toString().padStart(2, '0'),
      current_day: currentDate.getDate().toString().padStart(2, '0'),
    },
  };
}

/**
 * Заменяет переменные в тексте шаблона на реальные данные
 */
export function replaceTemplateVariables(template: string, data: TemplateData): string {
  let result = template;
  
  // Рекурсивная функция для замены вложенных объектов
  function replaceObject(obj: any, prefix = ''): void {
    Object.keys(obj).forEach(key => {
      const value = obj[key];
      const variableName = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null) {
        replaceObject(value, variableName);
      } else {
        // Заменяем переменные в формате {{variable}} и {variable}
        const patterns = [
          new RegExp(`\\{\\{\\s*${variableName}\\s*\\}\\}`, 'g'),
          new RegExp(`\\{\\s*${variableName}\\s*\\}`, 'g'),
        ];
        
        patterns.forEach(pattern => {
          result = result.replace(pattern, String(value || ''));
        });
      }
    });
  }
  
  replaceObject(data);
  
  return result;
}

/**
 * Извлекает все переменные из шаблона
 */
export function extractTemplateVariables(template: string): string[] {
  const patterns = [
    /\{\{\s*([^}]+)\s*\}\}/g,  // {{variable}}
    /\{\s*([^}]+)\s*\}/g,      // {variable}
  ];
  
  const variables = new Set<string>();
  
  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(template)) !== null) {
      variables.add(match[1].trim());
    }
  });
  
  return Array.from(variables);
}

/**
 * Проверяет, все ли переменные в шаблоне могут быть заменены
 */
export function validateTemplateVariables(template: string, data: TemplateData): {
  isValid: boolean;
  missingVariables: string[];
} {
  const templateVariables = extractTemplateVariables(template);
  const missingVariables: string[] = [];
  
  templateVariables.forEach(variable => {
    if (!hasNestedProperty(data, variable)) {
      missingVariables.push(variable);
    }
  });
  
  return {
    isValid: missingVariables.length === 0,
    missingVariables,
  };
}

/**
 * Проверяет наличие вложенного свойства в объекте
 */
function hasNestedProperty(obj: any, path: string): boolean {
  const keys = path.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current === null || current === undefined || !(key in current)) {
      return false;
    }
    current = current[key];
  }
  
  return true;
}

/**
 * Форматирует дату в читаемый формат
 */
function formatDate(date: Date): string {
  return date.toLocaleDateString('ru-RU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

/**
 * Форматирует денежную сумму
 */
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('ru-RU', {
    style: 'currency',
    currency: 'GEL',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(amount);
}

/**
 * Получает текстовое представление статуса договора
 */
function getContractStatusText(status: string): string {
  const statusMap: Record<string, string> = {
    'draft': 'Черновик',
    'active': 'Активный',
    'completed': 'Завершен',
    'cancelled': 'Отменен',
    'suspended': 'Приостановлен',
  };
  
  return statusMap[status] || status;
}

/**
 * Получает список всех доступных переменных для шаблонов
 */
export function getAvailableTemplateVariables(): Record<string, string[]> {
  return {
    'Договор': [
      'contract.number',
      'contract.signed_date',
      'contract.start_date',
      'contract.end_date',
      'contract.total_amount',
      'contract.initial_payment',
      'contract.monthly_payment',
      'contract.status',
    ],
    'Клиент': [
      'client.full_name',
      'client.first_name',
      'client.last_name',
      'client.middle_name',
      'client.passport_number',
      'client.passport_issued_by',
      'client.passport_issued_date',
      'client.address',
      'client.phone',
      'client.email',
    ],
    'Недвижимость': [
      'property.complex_name',
      'property.building_name',
      'property.apartment_number',
      'property.floor',
      'property.rooms',
      'property.area',
      'property.price',
    ],
    'Система': [
      'system.current_date',
      'system.current_year',
      'system.current_month',
      'system.current_day',
    ],
  };
}
