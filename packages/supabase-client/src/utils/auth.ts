/**
 * Утилиты для работы с аутентификацией
 * 
 * @module utils/auth
 */

import { createSupabaseClientFromEnv } from '../supabase';

/**
 * Создание пользователя с определенной ролью
 * 
 * @param email Email пользователя
 * @param password Пароль пользователя
 * @param role Роль пользователя
 * @param tenantId ID арендатора (для tenant_admin, after_sales_manager, client)
 * @returns Результат создания пользователя
 * 
 * @example
 * ```ts
 * // Создание superadmin
 * await createUser('<EMAIL>', 'password', 'superadmin');
 * 
 * // Создание tenant_admin
 * await createUser('<EMAIL>', 'password', 'tenant_admin', 'tenant-uuid');
 * ```
 */
export async function createUser(
  email: string, 
  password: string, 
  role: string, 
  tenantId?: string
) {
  const supabase = createSupabaseClientFromEnv();
  
  const userData = {
    email,
    password,
    options: {
      data: {
        role,
        ...(tenantId && { tenant_id: tenantId }),
      }
    }
  };
  
  return await supabase.auth.admin.createUser(userData);
}

/**
 * Изменение роли пользователя
 * 
 * @param userId ID пользователя
 * @param role Новая роль пользователя
 * @param tenantId ID арендатора (для tenant_admin, after_sales_manager, client)
 * @returns Результат обновления пользователя
 * 
 * @example
 * ```ts
 * // Изменение роли на support
 * await updateUserRole('user-uuid', 'support');
 * 
 * // Изменение роли на tenant_admin с указанием tenant_id
 * await updateUserRole('user-uuid', 'tenant_admin', 'tenant-uuid');
 * ```
 */
export async function updateUserRole(
  userId: string, 
  role: string, 
  tenantId?: string
) {
  const supabase = createSupabaseClientFromEnv();
  
  const metadata = {
    role,
    ...(tenantId && { tenant_id: tenantId }),
  };
  
  return await supabase.auth.admin.updateUserById(userId, {
    user_metadata: metadata,
  });
}

/**
 * Получение пользователя по ID
 * 
 * @param userId ID пользователя
 * @returns Данные пользователя
 * 
 * @example
 * ```ts
 * const user = await getUserById('user-uuid');
 * console.log(user.role); // 'tenant_admin'
 * ```
 */
export async function getUserById(userId: string) {
  const supabase = createSupabaseClientFromEnv();
  
  const { data, error } = await supabase.auth.admin.getUserById(userId);
  
  if (error) {
    throw error;
  }
  
  return data.user;
}

/**
 * Получение списка пользователей
 * 
 * @returns Список пользователей
 * 
 * @example
 * ```ts
 * const users = await getUsers();
 * console.log(`Всего пользователей: ${users.length}`);
 * ```
 */
export async function getUsers() {
  const supabase = createSupabaseClientFromEnv();
  
  const { data, error } = await supabase.auth.admin.listUsers();
  
  if (error) {
    throw error;
  }
  
  return data.users;
}

/**
 * Удаление пользователя
 * 
 * @param userId ID пользователя
 * @returns Результат удаления пользователя
 * 
 * @example
 * ```ts
 * await deleteUser('user-uuid');
 * ```
 */
export async function deleteUser(userId: string) {
  const supabase = createSupabaseClientFromEnv();
  
  return await supabase.auth.admin.deleteUser(userId);
}
