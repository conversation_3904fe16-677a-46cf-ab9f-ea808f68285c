/**
 * Утилиты для работы с арендаторами (tenants)
 * 
 * @module utils/tenantUtils
 */

import { SupabaseClient } from '../supabase';
import { Database } from '../types';

/**
 * Тип арендатора
 */
export type Tenant = Database['public']['Tables']['tenants']['Row'];

/**
 * Получает список всех арендаторов
 * 
 * @param supabase Клиент Supabase
 * @returns Список арендаторов
 * 
 * @example
 * ```ts
 * const tenants = await getAllTenants(supabase);
 * console.log(`Найдено ${tenants.length} арендаторов`);
 * ```
 */
export async function getAllTenants(supabase: SupabaseClient) {
  const { data, error } = await supabase
    .from('tenants')
    .select('*');

  if (error) {
    throw error;
  }

  return data;
}

/**
 * Получает арендатора по ID
 * 
 * @param supabase Клиент Supabase
 * @param id ID арендатора
 * @returns Арендатор или null, если не найден
 * 
 * @example
 * ```ts
 * const tenant = await getTenantById(supabase, '123');
 * if (tenant) {
 *   console.log(`Найден арендатор: ${tenant.name}`);
 * }
 * ```
 */
export async function getTenantById(supabase: SupabaseClient, id: string) {
  const { data, error } = await supabase
    .from('tenants')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // Запись не найдена
      return null;
    }
    throw error;
  }

  return data;
}

/**
 * Создает нового арендатора
 * 
 * @param supabase Клиент Supabase
 * @param tenant Данные арендатора
 * @returns Созданный арендатор
 * 
 * @example
 * ```ts
 * const newTenant = await createTenant(supabase, {
 *   name: 'ООО "Застройщик"',
 *   settings: { theme: 'light' }
 * });
 * console.log(`Создан арендатор с ID: ${newTenant.id}`);
 * ```
 */
export async function createTenant(
  supabase: SupabaseClient,
  tenant: Omit<Tenant, 'id' | 'created_at'>
) {
  const { data, error } = await supabase
    .from('tenants')
    .insert(tenant)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * Обновляет данные арендатора
 * 
 * @param supabase Клиент Supabase
 * @param id ID арендатора
 * @param tenant Данные для обновления
 * @returns Обновленный арендатор
 * 
 * @example
 * ```ts
 * const updatedTenant = await updateTenant(supabase, '123', {
 *   name: 'ООО "Новое название"'
 * });
 * console.log(`Арендатор обновлен: ${updatedTenant.name}`);
 * ```
 */
export async function updateTenant(
  supabase: SupabaseClient,
  id: string,
  tenant: Partial<Omit<Tenant, 'id' | 'created_at'>>
) {
  const { data, error } = await supabase
    .from('tenants')
    .update(tenant)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  return data;
}

/**
 * Удаляет арендатора
 * 
 * @param supabase Клиент Supabase
 * @param id ID арендатора
 * @returns true, если удаление прошло успешно
 * 
 * @example
 * ```ts
 * const success = await deleteTenant(supabase, '123');
 * if (success) {
 *   console.log('Арендатор успешно удален');
 * }
 * ```
 */
export async function deleteTenant(supabase: SupabaseClient, id: string) {
  const { error } = await supabase
    .from('tenants')
    .delete()
    .eq('id', id);

  if (error) {
    throw error;
  }

  return true;
}
