/**
 * @file: UserRoleManager.tsx
 * @description: Компонент для управления ролями пользователей
 * @dependencies: RoleContext, AuthContext, TenantContext
 * @created: 2024-05-01
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useRole, Role } from '../context/RoleContext';
import { useTenant } from '../context/TenantContext';

/**
 * Тип пользователя для UserRoleManager
 */
export interface UserRoleManagerUser {
  id: string;
  email: string;
  full_name?: string;
  tenant_id?: string;
  role?: string;
  created_at?: string;
}

/**
 * Тип связи пользователя и роли
 */
export interface UserRole {
  id: string;
  user_id: string;
  role_id: string;
  tenant_id?: string;
  created_at?: string;
}

/**
 * Свойства компонента UserRoleManager
 */
export interface UserRoleManagerProps {
  /** Обработчик события назначения роли пользователю */
  onRoleAssigned?: (userId: string, roleId: string) => void;
  /** Обработчик события отзыва роли у пользователя */
  onRoleRevoked?: (userId: string, roleId: string) => void;
}

/**
 * Компонент для управления ролями пользователей
 *
 * @param props Свойства компонента
 * @returns Компонент для управления ролями пользователей
 *
 * @example
 * ```tsx
 * <UserRoleManager
 *   onRoleAssigned={(userId, roleId) => console.log('Роль назначена:', userId, roleId)}
 *   onRoleRevoked={(userId, roleId) => console.log('Роль отозвана:', userId, roleId)}
 * />
 * ```
 */
export function UserRoleManager({
  onRoleAssigned,
  onRoleRevoked
}: UserRoleManagerProps) {
  const { supabase } = useAuth();
  const { tenantId } = useTenant();
  const { refreshPermissions } = useRole();

  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [userRoles, setUserRoles] = useState<Record<string, string[]>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Загрузка пользователей
  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase.from('users').select('*');

      // Если пользователь не superadmin, фильтруем по tenant_id
      if (tenantId) {
        query = query.eq('tenant_id', tenantId);
      }

      const { data, error: usersError } = await query;

      if (usersError) throw usersError;
      setUsers(data || []);
    } catch (err) {
      console.error('Ошибка при загрузке пользователей:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Загрузка ролей
  const loadRoles = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase.from('roles').select('*');

      // Если пользователь не superadmin, фильтруем по tenant_id
      if (tenantId) {
        query = query.eq('tenant_id', tenantId);
      }

      const { data, error: rolesError } = await query;

      if (rolesError) throw rolesError;
      setRoles(data || []);
    } catch (err) {
      console.error('Ошибка при загрузке ролей:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Загрузка связей пользователей и ролей
  const loadUserRoles = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase.from('user_roles').select('*');

      // Если пользователь не superadmin, фильтруем по tenant_id
      if (tenantId) {
        query = query.eq('tenant_id', tenantId);
      }

      const { data, error: userRolesError } = await query;

      if (userRolesError) throw userRolesError;

      // Преобразуем данные в формат { userId: [roleId1, roleId2, ...] }
      const userRoleMap: Record<string, string[]> = {};
      data?.forEach(ur => {
        if (!userRoleMap[ur.user_id]) {
          userRoleMap[ur.user_id] = [];
        }
        userRoleMap[ur.user_id].push(ur.role_id);
      });

      setUserRoles(userRoleMap);
    } catch (err) {
      console.error('Ошибка при загрузке связей пользователей и ролей:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Назначение роли пользователю
  const assignRoleToUser = async (userId: string, roleId: string) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: assignError } = await supabase
        .from('user_roles')
        .insert({
          user_id: userId,
          role_id: roleId,
          tenant_id: tenantId
        })
        .select()
        .single();

      if (assignError) throw assignError;

      // Обновляем список связей пользователей и ролей
      await loadUserRoles();

      // Обновляем разрешения пользователя
      await refreshPermissions();

      // Вызываем обработчик события
      if (onRoleAssigned) {
        onRoleAssigned(userId, roleId);
      }

      return data;
    } catch (err) {
      console.error('Ошибка при назначении роли пользователю:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Отзыв роли у пользователя
  const revokeRoleFromUser = async (userId: string, roleId: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error: revokeError } = await supabase
        .from('user_roles')
        .delete()
        .eq('user_id', userId)
        .eq('role_id', roleId);

      if (revokeError) throw revokeError;

      // Обновляем список связей пользователей и ролей
      await loadUserRoles();

      // Обновляем разрешения пользователя
      await refreshPermissions();

      // Вызываем обработчик события
      if (onRoleRevoked) {
        onRoleRevoked(userId, roleId);
      }

      return true;
    } catch (err) {
      console.error('Ошибка при отзыве роли у пользователя:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Создание пользователя
  const createUser = async (email: string, password: string, fullName: string, role: string) => {
    try {
      setLoading(true);
      setError(null);

      // Создаем пользователя в Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          full_name: fullName,
          role,
          tenant_id: tenantId
        }
      });

      if (authError) throw authError;

      // Создаем запись в таблице users
      const { data: userData, error: userError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email,
          full_name: fullName,
          tenant_id: tenantId,
          role
        })
        .select()
        .single();

      if (userError) throw userError;

      // Обновляем список пользователей
      await loadUsers();

      return userData;
    } catch (err) {
      console.error('Ошибка при создании пользователя:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Обновление пользователя
  const updateUser = async (id: string, fullName: string, role: string) => {
    try {
      setLoading(true);
      setError(null);

      // Обновляем метаданные пользователя в Supabase Auth
      const { error: authError } = await supabase.auth.admin.updateUserById(
        id,
        {
          user_metadata: {
            full_name: fullName,
            role,
            tenant_id: tenantId
          }
        }
      );

      if (authError) throw authError;

      // Обновляем запись в таблице users
      const { data: userData, error: userError } = await supabase
        .from('users')
        .update({
          full_name: fullName,
          role
        })
        .eq('id', id)
        .select()
        .single();

      if (userError) throw userError;

      // Обновляем список пользователей
      await loadUsers();

      return userData;
    } catch (err) {
      console.error('Ошибка при обновлении пользователя:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Удаление пользователя
  const deleteUser = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      // Удаляем пользователя из Supabase Auth
      const { error: authError } = await supabase.auth.admin.deleteUser(id);

      if (authError) throw authError;

      // Удаляем запись из таблицы users
      const { error: userError } = await supabase
        .from('users')
        .delete()
        .eq('id', id);

      if (userError) throw userError;

      // Обновляем список пользователей
      await loadUsers();

      return true;
    } catch (err) {
      console.error('Ошибка при удалении пользователя:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Загружаем данные при монтировании компонента
  useEffect(() => {
    loadUsers();
    loadRoles();
    loadUserRoles();
  }, [tenantId]);

  return {
    users,
    roles,
    userRoles,
    loading,
    error,
    createUser,
    updateUser,
    deleteUser,
    assignRoleToUser,
    revokeRoleFromUser,
    refresh: async () => {
      await Promise.all([
        loadUsers(),
        loadRoles(),
        loadUserRoles()
      ]);
    }
  };
}
