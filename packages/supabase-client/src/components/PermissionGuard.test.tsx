/**
 * @file: PermissionGuard.test.tsx
 * @description: Тесты для компонента PermissionGuard
 * @dependencies: vitest, @testing-library/react
 * @created: 2024-05-03
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { PermissionGuard, RoleGuard } from '../components/PermissionGuard';
import { mockRoleContext } from '../tests/mocks/contexts';

// Мок для хука useRole
vi.mock('../context/RoleContext', () => ({
  useRole: () => mockRoleContext,
}));

describe('PermissionGuard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('должен отображать дочерние элементы, если у пользователя есть разрешение', () => {
    // Настраиваем мок
    mockRoleContext.hasPermission.mockReturnValue(true);
    
    // Рендерим компонент
    render(
      <PermissionGuard resource="complexes" action="read">
        <div data-testid="protected-content">Защищенный контент</div>
      </PermissionGuard>
    );
    
    // Проверяем, что дочерние элементы отображаются
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.getByText('Защищенный контент')).toBeInTheDocument();
    
    // Проверяем, что hasPermission был вызван с правильными параметрами
    expect(mockRoleContext.hasPermission).toHaveBeenCalledWith('complexes', 'read');
  });

  it('не должен отображать дочерние элементы, если у пользователя нет разрешения', () => {
    // Настраиваем мок
    mockRoleContext.hasPermission.mockReturnValue(false);
    
    // Рендерим компонент
    render(
      <PermissionGuard resource="complexes" action="create">
        <div data-testid="protected-content">Защищенный контент</div>
      </PermissionGuard>
    );
    
    // Проверяем, что дочерние элементы не отображаются
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Защищенный контент')).not.toBeInTheDocument();
    
    // Проверяем, что hasPermission был вызван с правильными параметрами
    expect(mockRoleContext.hasPermission).toHaveBeenCalledWith('complexes', 'create');
  });

  it('должен отображать fallback, если у пользователя нет разрешения', () => {
    // Настраиваем мок
    mockRoleContext.hasPermission.mockReturnValue(false);
    
    // Рендерим компонент
    render(
      <PermissionGuard 
        resource="complexes" 
        action="create"
        fallback={<div data-testid="fallback-content">Доступ запрещен</div>}
      >
        <div data-testid="protected-content">Защищенный контент</div>
      </PermissionGuard>
    );
    
    // Проверяем, что fallback отображается
    expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    expect(screen.getByText('Доступ запрещен')).toBeInTheDocument();
    
    // Проверяем, что дочерние элементы не отображаются
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Защищенный контент')).not.toBeInTheDocument();
  });

  it('не должен отображать ничего, если данные о разрешениях еще загружаются', () => {
    // Настраиваем мок
    mockRoleContext.loading = true;
    
    // Рендерим компонент
    render(
      <PermissionGuard resource="complexes" action="read">
        <div data-testid="protected-content">Защищенный контент</div>
      </PermissionGuard>
    );
    
    // Проверяем, что ничего не отображается
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Защищенный контент')).not.toBeInTheDocument();
    
    // Сбрасываем мок
    mockRoleContext.loading = false;
  });
});

describe('RoleGuard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('должен отображать дочерние элементы, если у пользователя есть роль', () => {
    // Настраиваем мок
    mockRoleContext.hasRole.mockReturnValue(true);
    
    // Рендерим компонент
    render(
      <RoleGuard roles="tenant_admin">
        <div data-testid="protected-content">Защищенный контент</div>
      </RoleGuard>
    );
    
    // Проверяем, что дочерние элементы отображаются
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.getByText('Защищенный контент')).toBeInTheDocument();
    
    // Проверяем, что hasRole был вызван с правильными параметрами
    expect(mockRoleContext.hasRole).toHaveBeenCalledWith('tenant_admin');
  });

  it('должен отображать дочерние элементы, если у пользователя есть одна из ролей', () => {
    // Настраиваем мок
    mockRoleContext.hasRole
      .mockReturnValueOnce(false) // для 'superadmin'
      .mockReturnValueOnce(true); // для 'tenant_admin'
    
    // Рендерим компонент
    render(
      <RoleGuard roles={['superadmin', 'tenant_admin']}>
        <div data-testid="protected-content">Защищенный контент</div>
      </RoleGuard>
    );
    
    // Проверяем, что дочерние элементы отображаются
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.getByText('Защищенный контент')).toBeInTheDocument();
    
    // Проверяем, что hasRole был вызван с правильными параметрами
    expect(mockRoleContext.hasRole).toHaveBeenCalledWith('superadmin');
    expect(mockRoleContext.hasRole).toHaveBeenCalledWith('tenant_admin');
  });

  it('не должен отображать дочерние элементы, если у пользователя нет роли', () => {
    // Настраиваем мок
    mockRoleContext.hasRole.mockReturnValue(false);
    
    // Рендерим компонент
    render(
      <RoleGuard roles="superadmin">
        <div data-testid="protected-content">Защищенный контент</div>
      </RoleGuard>
    );
    
    // Проверяем, что дочерние элементы не отображаются
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Защищенный контент')).not.toBeInTheDocument();
    
    // Проверяем, что hasRole был вызван с правильными параметрами
    expect(mockRoleContext.hasRole).toHaveBeenCalledWith('superadmin');
  });
});
