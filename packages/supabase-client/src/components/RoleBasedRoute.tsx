/**
 * Компонент для защиты маршрутов на основе ролей и разрешений
 *
 * @module components/RoleBasedRoute
 */

import React, { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../context/AuthContext';
import { useRole } from '../context/RoleContext';

/**
 * Свойства компонента RoleBasedRoute
 */
export interface RoleBasedRouteProps {
  /** Дочерние элементы, которые будут отображены только при наличии доступа */
  children: ReactNode;
  /** Роли, которым разрешен доступ */
  allowedRoles?: string[];
  /** Требуемые разрешения для доступа */
  requiredPermissions?: Array<{ resource: string; action: string }>;
  /** URL для перенаправления при отсутствии доступа */
  redirectUrl?: string;
  /** Компонент, который будет отображен при отсутствии доступа (вместо перенаправления) */
  fallback?: ReactNode;
  /** Компонент, который будет отображен во время загрузки */
  loadingComponent?: ReactNode;
}

/**
 * Компонент для защиты маршрутов на основе ролей и разрешений
 *
 * Отображает дочерние элементы только если у пользователя есть необходимые роли или разрешения
 *
 * @param props Свойства компонента
 * @returns Защищенный компонент, fallback или loadingComponent
 *
 * @example
 * ```tsx
 * // Защита маршрута по ролям
 * <RoleBasedRoute allowedRoles={['tenant_admin', 'after_sales_manager']}>
 *   <DashboardPage />
 * </RoleBasedRoute>
 *
 * // Защита маршрута по разрешениям
 * <RoleBasedRoute
 *   requiredPermissions={[
 *     { resource: 'clients', action: 'read' },
 *     { resource: 'contracts', action: 'read' }
 *   ]}
 * >
 *   <ClientsPage />
 * </RoleBasedRoute>
 *
 * // Защита с кастомным компонентом при отсутствии доступа
 * <RoleBasedRoute
 *   allowedRoles={['superadmin']}
 *   fallback={<AccessDeniedPage />}
 * >
 *   <AdminSettingsPage />
 * </RoleBasedRoute>
 * ```
 */
export function RoleBasedRoute({
  children,
  allowedRoles,
  requiredPermissions,
  redirectUrl = '/login',
  fallback = null,
  loadingComponent = null
}: RoleBasedRouteProps) {
  const { user, loading: authLoading } = useAuth();
  const { hasRole, hasPermission, loading: roleLoading } = useRole();
  const router = useRouter();

  const loading = authLoading || roleLoading;

  useEffect(() => {
    // Если загрузка завершена и пользователь не аутентифицирован
    if (!loading && !user) {
      if (!fallback) {
        router.push(redirectUrl);
      }
      return;
    }

    // Если загрузка завершена и пользователь аутентифицирован
    if (!loading && user) {
      let hasAccess = true;

      // Проверяем роли, если они указаны
      if (allowedRoles && allowedRoles.length > 0) {
        hasAccess = allowedRoles.some(role => hasRole(role));
      }

      // Проверяем разрешения, если они указаны
      if (hasAccess && requiredPermissions && requiredPermissions.length > 0) {
        hasAccess = requiredPermissions.every(({ resource, action }) =>
          hasPermission(resource, action)
        );
      }

      // Если нет доступа и нет fallback, перенаправляем
      if (!hasAccess && !fallback) {
        router.push('/');
      }
    }
  }, [user, loading, router, allowedRoles, requiredPermissions, redirectUrl, fallback, hasRole, hasPermission]);

  // Показываем индикатор загрузки, пока проверяем аутентификацию и права доступа
  if (loading) {
    return <>{loadingComponent}</>;
  }

  // Если пользователь не аутентифицирован и есть fallback, показываем его
  if (!user && fallback) {
    return <>{fallback}</>;
  }

  // Если пользователь аутентифицирован, проверяем права доступа
  if (user) {
    let hasAccess = true;

    // Проверяем роли, если они указаны
    if (allowedRoles && allowedRoles.length > 0) {
      hasAccess = allowedRoles.some(role => hasRole(role));
    }

    // Проверяем разрешения, если они указаны
    if (hasAccess && requiredPermissions && requiredPermissions.length > 0) {
      hasAccess = requiredPermissions.every(({ resource, action }) =>
        hasPermission(resource, action)
      );
    }

    // Если нет доступа и есть fallback, показываем его
    if (!hasAccess && fallback) {
      return <>{fallback}</>;
    }

    // Если есть доступ, показываем дочерние элементы
    if (hasAccess) {
      return <>{children}</>;
    }
  }

  // В остальных случаях ничего не показываем (будет выполнено перенаправление)
  return null;
}
