/**
 * @file: RoleBasedRoute.test.tsx
 * @description: Тесты для компонента RoleBasedRoute
 * @dependencies: vitest, @testing-library/react
 * @created: 2024-05-03
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { RoleBasedRoute } from '../components/RoleBasedRoute';
import { mockRoleContext } from '../tests/mocks/contexts';
import { mockAuthContext } from '../tests/mocks/contexts';
import { mockUseRouter } from '../tests/mocks/next';

// Мок для хука useRole
vi.mock('../context/RoleContext', () => ({
  useRole: () => mockRoleContext,
}));

// Мок для хука useAuth
vi.mock('../context/AuthContext', () => ({
  useAuth: () => mockAuthContext,
}));

// Мок для Next.js router
const mockPush = vi.fn();
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn()
  })
}));

describe('RoleBasedRoute', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('должен отображать дочерние элементы, если у пользователя есть необходимая роль', () => {
    // Настраиваем моки
    mockRoleContext.hasRole.mockReturnValue(true);

    // Рендерим компонент
    render(
      <RoleBasedRoute allowedRoles={['tenant_admin']}>
        <div data-testid="protected-content">Защищенный контент</div>
      </RoleBasedRoute>
    );

    // Проверяем, что дочерние элементы отображаются
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.getByText('Защищенный контент')).toBeInTheDocument();

    // Проверяем, что hasRole был вызван с правильными параметрами
    expect(mockRoleContext.hasRole).toHaveBeenCalledWith('tenant_admin');
  });

  it('должен отображать дочерние элементы, если у пользователя есть необходимое разрешение', () => {
    // Настраиваем моки
    mockRoleContext.hasPermission.mockReturnValue(true);

    // Рендерим компонент
    render(
      <RoleBasedRoute requiredPermissions={[{ resource: 'complexes', action: 'read' }]}>
        <div data-testid="protected-content">Защищенный контент</div>
      </RoleBasedRoute>
    );

    // Проверяем, что дочерние элементы отображаются
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.getByText('Защищенный контент')).toBeInTheDocument();

    // Проверяем, что hasPermission был вызван с правильными параметрами
    expect(mockRoleContext.hasPermission).toHaveBeenCalledWith('complexes', 'read');
  });

  it('должен отображать fallback, если у пользователя нет необходимой роли', () => {
    // Настраиваем моки
    mockRoleContext.hasRole.mockReturnValue(false);

    // Рендерим компонент
    render(
      <RoleBasedRoute
        allowedRoles={['superadmin']}
        fallback={<div data-testid="fallback-content">Доступ запрещен</div>}
      >
        <div data-testid="protected-content">Защищенный контент</div>
      </RoleBasedRoute>
    );

    // Проверяем, что fallback отображается
    expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    expect(screen.getByText('Доступ запрещен')).toBeInTheDocument();

    // Проверяем, что дочерние элементы не отображаются
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Защищенный контент')).not.toBeInTheDocument();
  });

  it('должен перенаправлять на страницу входа, если пользователь не аутентифицирован', () => {
    // Настраиваем моки
    mockAuthContext.user = null;

    // Рендерим компонент
    render(
      <RoleBasedRoute allowedRoles={['tenant_admin']}>
        <div data-testid="protected-content">Защищенный контент</div>
      </RoleBasedRoute>
    );

    // Проверяем, что произошло перенаправление
    expect(mockPush).toHaveBeenCalledWith('/login');

    // Проверяем, что дочерние элементы не отображаются
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Защищенный контент')).not.toBeInTheDocument();

    // Восстанавливаем мок
    mockAuthContext.user = mockAuthContext.session.user;
  });

  it('должен перенаправлять на указанный URL, если пользователь не аутентифицирован', () => {
    // Настраиваем моки
    mockAuthContext.user = null;

    // Рендерим компонент
    render(
      <RoleBasedRoute allowedRoles={['tenant_admin']} redirectUrl="/custom-login">
        <div data-testid="protected-content">Защищенный контент</div>
      </RoleBasedRoute>
    );

    // Проверяем, что произошло перенаправление
    expect(mockPush).toHaveBeenCalledWith('/custom-login');

    // Восстанавливаем мок
    mockAuthContext.user = mockAuthContext.session.user;
  });

  it('должен отображать loadingComponent, если данные еще загружаются', () => {
    // Настраиваем моки
    mockRoleContext.loading = true;

    // Рендерим компонент
    render(
      <RoleBasedRoute
        allowedRoles={['tenant_admin']}
        loadingComponent={<div data-testid="loading-content">Загрузка...</div>}
      >
        <div data-testid="protected-content">Защищенный контент</div>
      </RoleBasedRoute>
    );

    // Проверяем, что loadingComponent отображается
    expect(screen.getByTestId('loading-content')).toBeInTheDocument();
    expect(screen.getByText('Загрузка...')).toBeInTheDocument();

    // Проверяем, что дочерние элементы не отображаются
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Защищенный контент')).not.toBeInTheDocument();

    // Восстанавливаем мок
    mockRoleContext.loading = false;
  });
});
