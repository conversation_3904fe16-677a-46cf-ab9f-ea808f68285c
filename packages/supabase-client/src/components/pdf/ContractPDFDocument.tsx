/**
 * @file: ContractPDFDocument.tsx
 * @description: Компонент для генерации PDF документов договоров
 * @dependencies: @react-pdf/renderer
 * @created: 2024-12-26
 */

import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Font,
} from '@react-pdf/renderer';
import { TemplateData } from '../../utils/template-variables';

// Регистрируем шрифты для поддержки кириллицы
Font.register({
  family: 'Roboto',
  fonts: [
    {
      src: 'https://fonts.gstatic.com/s/roboto/v30/KFOmCnqEu92Fr1Mu4mxK.woff2',
      fontWeight: 400,
    },
    {
      src: 'https://fonts.gstatic.com/s/roboto/v30/KFOlCnqEu92Fr1MmWUlfBBc4.woff2',
      fontWeight: 700,
    },
  ],
});

// Стили для PDF документа
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Roboto',
    fontSize: 12,
    lineHeight: 1.5,
  },
  header: {
    marginBottom: 30,
    textAlign: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 14,
    marginBottom: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
    borderBottom: '1 solid #000000',
    paddingBottom: 5,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  label: {
    width: '40%',
    fontWeight: 'bold',
  },
  value: {
    width: '60%',
  },
  table: {
    marginBottom: 20,
  },
  tableRow: {
    flexDirection: 'row',
    borderBottom: '1 solid #CCCCCC',
    paddingVertical: 5,
  },
  tableHeader: {
    backgroundColor: '#F5F5F5',
    fontWeight: 'bold',
  },
  tableCell: {
    flex: 1,
    padding: 5,
  },
  footer: {
    marginTop: 30,
    borderTop: '1 solid #CCCCCC',
    paddingTop: 20,
  },
  signature: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 40,
  },
  signatureBlock: {
    width: '45%',
    textAlign: 'center',
  },
  signatureLine: {
    borderBottom: '1 solid #000000',
    marginBottom: 5,
    height: 20,
  },
  text: {
    marginBottom: 10,
  },
  bold: {
    fontWeight: 'bold',
  },
  center: {
    textAlign: 'center',
  },
  right: {
    textAlign: 'right',
  },
});

interface ContractPDFDocumentProps {
  templateData: TemplateData;
  templateContent?: string;
}

/**
 * Компонент PDF документа договора
 */
export const ContractPDFDocument: React.FC<ContractPDFDocumentProps> = ({
  templateData,
  templateContent,
}) => {
  // Если есть кастомный шаблон, используем его
  if (templateContent) {
    return (
      <Document>
        <Page size="A4" style={styles.page}>
          <CustomTemplateContent content={templateContent} data={templateData} />
        </Page>
      </Document>
    );
  }

  // Иначе используем стандартный шаблон
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Заголовок */}
        <View style={styles.header}>
          <Text style={styles.title}>
            ДОГОВОР КУПЛИ-ПРОДАЖИ НЕДВИЖИМОСТИ В РАССРОЧКУ
          </Text>
          <Text style={styles.subtitle}>
            № {templateData.contract.number}
          </Text>
          <Text>
            г. Тбилиси, {templateData.contract.signed_date}
          </Text>
        </View>

        {/* Информация о сторонах */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>СТОРОНЫ ДОГОВОРА</Text>

          <View style={styles.row}>
            <Text style={styles.label}>Продавец:</Text>
            <Text style={styles.value}>ООО "ЗАСТРОЙЩИК"</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Покупатель:</Text>
            <Text style={styles.value}>{templateData.client.full_name}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Паспорт:</Text>
            <Text style={styles.value}>
              {templateData.client.passport_number}, выдан {templateData.client.passport_issued_by}
              {templateData.client.passport_issued_date && ` ${templateData.client.passport_issued_date}`}
            </Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Адрес:</Text>
            <Text style={styles.value}>{templateData.client.address}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Телефон:</Text>
            <Text style={styles.value}>{templateData.client.phone}</Text>
          </View>
        </View>

        {/* Предмет договора */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>ПРЕДМЕТ ДОГОВОРА</Text>

          <View style={styles.row}>
            <Text style={styles.label}>Жилой комплекс:</Text>
            <Text style={styles.value}>{templateData.property.complex_name}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Здание:</Text>
            <Text style={styles.value}>{templateData.property.building_name}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Квартира:</Text>
            <Text style={styles.value}>№ {templateData.property.apartment_number}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Этаж:</Text>
            <Text style={styles.value}>{templateData.property.floor}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Количество комнат:</Text>
            <Text style={styles.value}>{templateData.property.rooms}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Площадь:</Text>
            <Text style={styles.value}>{templateData.property.area} м²</Text>
          </View>
        </View>

        {/* Финансовые условия */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>ФИНАНСОВЫЕ УСЛОВИЯ</Text>

          <View style={styles.row}>
            <Text style={styles.label}>Общая стоимость:</Text>
            <Text style={styles.value}>{templateData.contract.total_amount}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Первоначальный взнос:</Text>
            <Text style={styles.value}>{templateData.contract.initial_payment}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Ежемесячный платеж:</Text>
            <Text style={styles.value}>{templateData.contract.monthly_payment}</Text>
          </View>
        </View>

        {/* Сроки */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>СРОКИ ИСПОЛНЕНИЯ</Text>

          <View style={styles.row}>
            <Text style={styles.label}>Дата начала:</Text>
            <Text style={styles.value}>{templateData.contract.start_date}</Text>
          </View>

          <View style={styles.row}>
            <Text style={styles.label}>Дата окончания:</Text>
            <Text style={styles.value}>{templateData.contract.end_date}</Text>
          </View>
        </View>

        {/* Подписи */}
        <View style={styles.signature}>
          <View style={styles.signatureBlock}>
            <Text>Продавец:</Text>
            <View style={styles.signatureLine} />
            <Text>_________________</Text>
          </View>

          <View style={styles.signatureBlock}>
            <Text>Покупатель:</Text>
            <View style={styles.signatureLine} />
            <Text>{templateData.client.full_name}</Text>
          </View>
        </View>

        {/* Футер */}
        <View style={styles.footer}>
          <Text style={styles.center}>
            Документ сгенерирован автоматически {templateData.system.current_date}
          </Text>
        </View>
      </Page>
    </Document>
  );
};

/**
 * Компонент для отображения кастомного шаблона
 */
const CustomTemplateContent: React.FC<{
  content: string;
  data: TemplateData;
}> = ({ content, data }) => {
  // Простая обработка HTML-подобного контента
  // В реальном приложении здесь может быть более сложная логика парсинга
  const lines = content.split('\n');

  return (
    <View>
      {lines.map((line, index) => (
        <Text key={index} style={styles.text}>
          {line}
        </Text>
      ))}
    </View>
  );
};

export default ContractPDFDocument;
