/**
 * @file: RoleManager.tsx
 * @description: Компонент для управления ролями и разрешениями
 * @dependencies: RoleContext, AuthContext, TenantContext
 * @created: 2024-05-01
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useRole, Role, Permission } from '../context/RoleContext';
import { useTenant } from '../context/TenantContext';

/**
 * Свойства компонента RoleManager
 */
export interface RoleManagerProps {
  /** Обработчик события создания роли */
  onRoleCreated?: (role: Role) => void;
  /** Обработчик события обновления роли */
  onRoleUpdated?: (role: Role) => void;
  /** Обработчик события удаления роли */
  onRoleDeleted?: (roleId: string) => void;
  /** Обработчик события назначения разрешения роли */
  onPermissionAssigned?: (roleId: string, permissionId: string) => void;
  /** Обработчик события отзыва разрешения у роли */
  onPermissionRevoked?: (roleId: string, permissionId: string) => void;
  /** Обработчик события назначения роли пользователю */
  onRoleAssigned?: (userId: string, roleId: string) => void;
  /** Обработчик события отзыва роли у пользователя */
  onRoleRevoked?: (userId: string, roleId: string) => void;
}

/**
 * Компонент для управления ролями и разрешениями
 * 
 * @param props Свойства компонента
 * @returns Компонент для управления ролями и разрешениями
 * 
 * @example
 * ```tsx
 * <RoleManager 
 *   onRoleCreated={(role) => console.log('Роль создана:', role)}
 *   onRoleUpdated={(role) => console.log('Роль обновлена:', role)}
 *   onRoleDeleted={(roleId) => console.log('Роль удалена:', roleId)}
 * />
 * ```
 */
export function RoleManager({
  onRoleCreated,
  onRoleUpdated,
  onRoleDeleted,
  onPermissionAssigned,
  onPermissionRevoked,
  onRoleAssigned,
  onRoleRevoked
}: RoleManagerProps) {
  const { supabase } = useAuth();
  const { tenantId } = useTenant();
  const { refreshPermissions } = useRole();
  
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [rolePermissions, setRolePermissions] = useState<Record<string, string[]>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Загрузка ролей
  const loadRoles = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase.from('roles').select('*');
      
      // Если пользователь не superadmin, фильтруем по tenant_id
      if (tenantId) {
        query = query.eq('tenant_id', tenantId);
      }

      const { data, error: rolesError } = await query;

      if (rolesError) throw rolesError;
      setRoles(data || []);
    } catch (err) {
      console.error('Ошибка при загрузке ролей:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Загрузка разрешений
  const loadPermissions = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase.from('permissions').select('*');
      
      // Если пользователь не superadmin, фильтруем по tenant_id
      if (tenantId) {
        query = query.eq('tenant_id', tenantId);
      }

      const { data, error: permissionsError } = await query;

      if (permissionsError) throw permissionsError;
      setPermissions(data || []);
    } catch (err) {
      console.error('Ошибка при загрузке разрешений:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Загрузка связей ролей и разрешений
  const loadRolePermissions = async () => {
    try {
      setLoading(true);
      setError(null);

      let query = supabase.from('role_permissions').select('*');
      
      // Если пользователь не superadmin, фильтруем по tenant_id
      if (tenantId) {
        query = query.eq('tenant_id', tenantId);
      }

      const { data, error: rolePermissionsError } = await query;

      if (rolePermissionsError) throw rolePermissionsError;
      
      // Преобразуем данные в формат { roleId: [permissionId1, permissionId2, ...] }
      const rolePermMap: Record<string, string[]> = {};
      data?.forEach(rp => {
        if (!rolePermMap[rp.role_id]) {
          rolePermMap[rp.role_id] = [];
        }
        rolePermMap[rp.role_id].push(rp.permission_id);
      });
      
      setRolePermissions(rolePermMap);
    } catch (err) {
      console.error('Ошибка при загрузке связей ролей и разрешений:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  };

  // Создание роли
  const createRole = async (name: string, description: string) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: createError } = await supabase
        .from('roles')
        .insert({
          name,
          description,
          tenant_id: tenantId,
          is_system: false
        })
        .select()
        .single();

      if (createError) throw createError;
      
      // Обновляем список ролей
      await loadRoles();
      
      // Вызываем обработчик события
      if (onRoleCreated && data) {
        onRoleCreated(data);
      }
      
      return data;
    } catch (err) {
      console.error('Ошибка при создании роли:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Обновление роли
  const updateRole = async (id: string, name: string, description: string) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: updateError } = await supabase
        .from('roles')
        .update({
          name,
          description
        })
        .eq('id', id)
        .select()
        .single();

      if (updateError) throw updateError;
      
      // Обновляем список ролей
      await loadRoles();
      
      // Вызываем обработчик события
      if (onRoleUpdated && data) {
        onRoleUpdated(data);
      }
      
      return data;
    } catch (err) {
      console.error('Ошибка при обновлении роли:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Удаление роли
  const deleteRole = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error: deleteError } = await supabase
        .from('roles')
        .delete()
        .eq('id', id);

      if (deleteError) throw deleteError;
      
      // Обновляем список ролей
      await loadRoles();
      
      // Вызываем обработчик события
      if (onRoleDeleted) {
        onRoleDeleted(id);
      }
      
      return true;
    } catch (err) {
      console.error('Ошибка при удалении роли:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Назначение разрешения роли
  const assignPermissionToRole = async (roleId: string, permissionId: string) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: assignError } = await supabase
        .from('role_permissions')
        .insert({
          role_id: roleId,
          permission_id: permissionId,
          tenant_id: tenantId
        })
        .select()
        .single();

      if (assignError) throw assignError;
      
      // Обновляем список связей ролей и разрешений
      await loadRolePermissions();
      
      // Обновляем разрешения пользователя
      await refreshPermissions();
      
      // Вызываем обработчик события
      if (onPermissionAssigned) {
        onPermissionAssigned(roleId, permissionId);
      }
      
      return data;
    } catch (err) {
      console.error('Ошибка при назначении разрешения роли:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Отзыв разрешения у роли
  const revokePermissionFromRole = async (roleId: string, permissionId: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error: revokeError } = await supabase
        .from('role_permissions')
        .delete()
        .eq('role_id', roleId)
        .eq('permission_id', permissionId);

      if (revokeError) throw revokeError;
      
      // Обновляем список связей ролей и разрешений
      await loadRolePermissions();
      
      // Обновляем разрешения пользователя
      await refreshPermissions();
      
      // Вызываем обработчик события
      if (onPermissionRevoked) {
        onPermissionRevoked(roleId, permissionId);
      }
      
      return true;
    } catch (err) {
      console.error('Ошибка при отзыве разрешения у роли:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Загружаем данные при монтировании компонента
  useEffect(() => {
    loadRoles();
    loadPermissions();
    loadRolePermissions();
  }, [tenantId]);

  return {
    roles,
    permissions,
    rolePermissions,
    loading,
    error,
    createRole,
    updateRole,
    deleteRole,
    assignPermissionToRole,
    revokePermissionFromRole,
    refresh: async () => {
      await Promise.all([
        loadRoles(),
        loadPermissions(),
        loadRolePermissions()
      ]);
    }
  };
}
