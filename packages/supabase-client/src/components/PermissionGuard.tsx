/**
 * Компонент для защиты UI элементов на основе разрешений
 * 
 * @module components/PermissionGuard
 */

import React, { ReactNode } from 'react';
import { useRole } from '../context/RoleContext';

/**
 * Свойства компонента PermissionGuard
 */
export interface PermissionGuardProps {
  /** Дочерние элементы, которые будут отображены только при наличии разрешения */
  children: ReactNode;
  /** Ресурс, к которому требуется доступ */
  resource: string;
  /** Действие, которое требуется выполнить (create, read, update, delete) */
  action: string;
  /** Компонент или элемент, который будет отображен при отсутствии разрешения */
  fallback?: ReactNode;
}

/**
 * Компонент для защиты UI элементов на основе разрешений
 * 
 * Отображает дочерние элементы только если у пользователя есть необходимое разрешение
 * 
 * @param props Свойства компонента
 * @returns Защищенный компонент или fallback
 * 
 * @example
 * ```tsx
 * // Базовое использование
 * <PermissionGuard resource="clients" action="create">
 *   <Button>Создать клиента</Button>
 * </PermissionGuard>
 * 
 * // С альтернативным содержимым
 * <PermissionGuard 
 *   resource="contracts" 
 *   action="delete" 
 *   fallback={<Tooltip content="У вас нет прав на удаление договоров">
 *     <Button disabled>Удалить договор</Button>
 *   </Tooltip>}
 * >
 *   <Button onClick={handleDelete}>Удалить договор</Button>
 * </PermissionGuard>
 * ```
 */
export function PermissionGuard({
  children,
  resource,
  action,
  fallback = null
}: PermissionGuardProps) {
  const { hasPermission, loading } = useRole();

  // Если данные о разрешениях еще загружаются, не отображаем ничего
  if (loading) {
    return null;
  }

  // Если у пользователя есть необходимое разрешение, отображаем дочерние элементы
  if (hasPermission(resource, action)) {
    return <>{children}</>;
  }

  // Иначе отображаем fallback
  return <>{fallback}</>;
}

/**
 * Свойства компонента RoleGuard
 */
export interface RoleGuardProps {
  /** Дочерние элементы, которые будут отображены только при наличии роли */
  children: ReactNode;
  /** Роль или массив ролей, которые имеют доступ */
  roles: string | string[];
  /** Компонент или элемент, который будет отображен при отсутствии роли */
  fallback?: ReactNode;
}

/**
 * Компонент для защиты UI элементов на основе ролей
 * 
 * Отображает дочерние элементы только если у пользователя есть необходимая роль
 * 
 * @param props Свойства компонента
 * @returns Защищенный компонент или fallback
 * 
 * @example
 * ```tsx
 * // Базовое использование
 * <RoleGuard roles="tenant_admin">
 *   <AdminPanel />
 * </RoleGuard>
 * 
 * // С несколькими ролями
 * <RoleGuard 
 *   roles={['superadmin', 'support']} 
 *   fallback={<AccessDeniedPage />}
 * >
 *   <GlobalSettings />
 * </RoleGuard>
 * ```
 */
export function RoleGuard({
  children,
  roles,
  fallback = null
}: RoleGuardProps) {
  const { hasRole, loading } = useRole();

  // Если данные о ролях еще загружаются, не отображаем ничего
  if (loading) {
    return null;
  }

  // Преобразуем roles в массив, если это строка
  const roleArray = Array.isArray(roles) ? roles : [roles];

  // Если у пользователя есть хотя бы одна из необходимых ролей, отображаем дочерние элементы
  if (roleArray.some(role => hasRole(role))) {
    return <>{children}</>;
  }

  // Иначе отображаем fallback
  return <>{fallback}</>;
}
