/**
 * @file: setup.ts
 * @description: Настройка окружения для тестирования
 * @dependencies: @testing-library/jest-dom
 * @created: 2024-05-03
 */

import '@testing-library/jest-dom';
import { expect, afterEach } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

// Расширяем матчеры Vitest матчерами из testing-library
expect.extend(matchers);

// Очищаем после каждого теста
afterEach(() => {
  cleanup();
});
