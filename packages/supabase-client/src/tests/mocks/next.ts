/**
 * @file: next.ts
 * @description: Моки для Next.js
 * @dependencies: next
 * @created: 2024-05-03
 */

import { vi } from 'vitest';

// Мок для Next.js Router
export const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  prefetch: vi.fn(),
  back: vi.fn(),
  reload: vi.fn(),
  events: {
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
  },
  pathname: '/',
  query: {},
  asPath: '/',
  isFallback: false,
  basePath: '',
  locale: 'en',
  locales: ['en'],
  defaultLocale: 'en',
  isReady: true,
  isPreview: false,
  isLocaleDomain: false,
};

// Мок для useRouter
export const mockUseRouter = vi.fn().mockReturnValue(mockRouter);
