/**
 * @file: contexts.ts
 * @description: Моки для контекстов
 * @dependencies: react
 * @created: 2024-05-03
 */

import { vi } from 'vitest';
import { mockUsers } from './supabase';

// Мок для AuthContext
export const mockAuthContext = {
  user: {
    ...mockUsers[0],
    user_metadata: { role: null } // Начальное значение role - null
  },
  session: {
    user: {
      ...mockUsers[0],
      user_metadata: { role: null } // Начальное значение role - null
    }
  },
  supabase: {
    auth: {
      getUser: vi.fn().mockResolvedValue({
        data: {
          user: {
            ...mockUsers[0],
            user_metadata: { role: null } // Начальное значение role - null
          }
        }
      }),
      getSession: vi.fn().mockResolvedValue({
        data: {
          session: {
            user: {
              ...mockUsers[0],
              user_metadata: { role: null } // Начальное значение role - null
            }
          }
        }
      }),
      signOut: vi.fn().mockResolvedValue({ error: null }),
    },
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    rpc: vi.fn(),
  },
  signIn: vi.fn(),
  signOut: vi.fn(),
  loading: false,
  error: null,
};

// Мок для TenantContext
export const mockTenantContext = {
  tenant: {
    id: 'tenant-1',
    name: 'Test Tenant',
    domain: 'test.example.com',
  },
  tenantId: 'tenant-1',
  loading: false,
  error: null,
  setTenant: vi.fn(),
};

// Мок для RoleContext
export const mockRoleContext = {
  role: 'superadmin',
  roles: [
    { id: '1', name: 'superadmin', description: 'Полный доступ ко всей системе', is_system: true },
  ],
  permissions: [
    { id: '1', name: 'view_complexes', description: 'Просмотр жилых комплексов', resource: 'complexes', action: 'read' },
    { id: '2', name: 'create_complexes', description: 'Создание жилых комплексов', resource: 'complexes', action: 'create' },
  ],
  hasPermission: vi.fn().mockReturnValue(true),
  hasRole: vi.fn().mockReturnValue(true),
  loading: false,
  error: null,
  refreshPermissions: vi.fn(),
};
