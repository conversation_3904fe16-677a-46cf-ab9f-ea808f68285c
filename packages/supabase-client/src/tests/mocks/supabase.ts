/**
 * @file: supabase.ts
 * @description: Моки для Supabase клиента
 * @dependencies: @supabase/supabase-js
 * @created: 2024-05-03
 */

import { vi } from 'vitest';

// Мок для функций базы данных
export const mockDbFunctions = {
  get_user_roles: vi.fn(),
  get_user_permissions: vi.fn(),
  has_permission: vi.fn(),
  has_role: vi.fn(),
  assign_role_to_user: vi.fn(),
  remove_role_from_user: vi.fn(),
  assign_permission_to_role: vi.fn(),
  remove_permission_from_role: vi.fn(),
};

// Мок для Supabase клиента
export const mockSupabaseClient = {
  auth: {
    getUser: vi.fn(),
    getSession: vi.fn(),
    signOut: vi.fn(),
    onAuthStateChange: vi.fn(),
    signInWithPassword: vi.fn(),
    signUp: vi.fn(),
  },
  from: vi.fn().mockImplementation((table) => ({
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    in: vi.fn().mockReturnThis(),
    single: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    maybeSingle: vi.fn().mockReturnThis(),
    match: vi.fn().mockReturnThis(),
    rpc: vi.fn().mockImplementation((func, params) => {
      if (mockDbFunctions[func]) {
        return {
          data: mockDbFunctions[func](params),
          error: null,
        };
      }
      return {
        data: null,
        error: new Error(`Function ${func} not implemented`),
      };
    }),
  })),
  rpc: vi.fn().mockImplementation((func, params) => {
    if (mockDbFunctions[func]) {
      try {
        const data = mockDbFunctions[func](params);
        return {
          data,
          error: null,
        };
      } catch (error) {
        return {
          data: null,
          error,
        };
      }
    }
    return {
      data: null,
      error: new Error(`Function ${func} not implemented`),
    };
  }),
};

// Мок для хука useSupabaseClient
export const mockUseSupabaseClient = vi.fn().mockReturnValue(mockSupabaseClient);

// Мок для данных о ролях и разрешениях
export const mockRoles = [
  { id: '1', name: 'superadmin', description: 'Полный доступ ко всей системе', is_system: true },
  { id: '2', name: 'support', description: 'Техническая поддержка с доступом к данным клиентов', is_system: true },
  { id: '3', name: 'tenant_admin', description: 'Администратор компании-застройщика', is_system: true },
  { id: '4', name: 'after_sales_manager', description: 'Менеджер по сопровождению клиентов', is_system: true },
  { id: '5', name: 'client', description: 'Клиент (покупатель недвижимости)', is_system: true },
];

export const mockPermissions = [
  { id: '1', name: 'view_complexes', description: 'Просмотр жилых комплексов', resource: 'complexes', action: 'read' },
  { id: '2', name: 'create_complexes', description: 'Создание жилых комплексов', resource: 'complexes', action: 'create' },
  { id: '3', name: 'update_complexes', description: 'Редактирование жилых комплексов', resource: 'complexes', action: 'update' },
  { id: '4', name: 'delete_complexes', description: 'Удаление жилых комплексов', resource: 'complexes', action: 'delete' },
  { id: '5', name: 'view_clients', description: 'Просмотр клиентов', resource: 'clients', action: 'read' },
  { id: '6', name: 'create_clients', description: 'Создание клиентов', resource: 'clients', action: 'create' },
  { id: '7', name: 'update_clients', description: 'Редактирование клиентов', resource: 'clients', action: 'update' },
  { id: '8', name: 'delete_clients', description: 'Удаление клиентов', resource: 'clients', action: 'delete' },
];

export const mockRolePermissions = [
  { id: '1', role_id: '3', permission_id: '1' },
  { id: '2', role_id: '3', permission_id: '2' },
  { id: '3', role_id: '3', permission_id: '3' },
  { id: '4', role_id: '3', permission_id: '4' },
  { id: '5', role_id: '3', permission_id: '5' },
  { id: '6', role_id: '3', permission_id: '6' },
  { id: '7', role_id: '3', permission_id: '7' },
  { id: '8', role_id: '3', permission_id: '8' },
  { id: '9', role_id: '4', permission_id: '1' },
  { id: '10', role_id: '4', permission_id: '5' },
  { id: '11', role_id: '5', permission_id: '1' },
];

export const mockUserRoles = [
  { id: '1', user_id: 'user-1', role_id: '1' },
  { id: '2', user_id: 'user-2', role_id: '2' },
  { id: '3', user_id: 'user-3', role_id: '3' },
  { id: '4', user_id: 'user-4', role_id: '4' },
  { id: '5', user_id: 'user-5', role_id: '5' },
];

// Мок для пользователей
export const mockUsers = [
  { id: 'user-1', email: '<EMAIL>', user_metadata: { role: 'superadmin' } },
  { id: 'user-2', email: '<EMAIL>', user_metadata: { role: 'support' } },
  { id: 'user-3', email: '<EMAIL>', user_metadata: { role: 'tenant_admin' } },
  { id: 'user-4', email: '<EMAIL>', user_metadata: { role: 'after_sales_manager' } },
  { id: 'user-5', email: '<EMAIL>', user_metadata: { role: 'client' } },
];
