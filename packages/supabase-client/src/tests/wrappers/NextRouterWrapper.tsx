/**
 * @file: NextRouterWrapper.tsx
 * @description: Обертка для тестирования компонентов, использующих Next.js Router
 * @dependencies: next, react
 * @created: 2024-05-03
 */

import React, { ReactNode } from 'react';
import { mockUseRouter } from '../mocks/next';

// Мок для Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => mockUseRouter(),
}));

/**
 * Свойства компонента NextRouterWrapper
 */
interface NextRouterWrapperProps {
  children: ReactNode;
}

/**
 * Компонент-обертка для тестирования компонентов, использующих Next.js Router
 * 
 * @param props Свойства компонента
 * @returns Компонент-обертка
 */
export function NextRouterWrapper({ children }: NextRouterWrapperProps) {
  return <>{children}</>;
}
