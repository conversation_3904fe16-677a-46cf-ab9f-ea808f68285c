/**
 * @file: db-functions.test.ts
 * @description: Тесты для функций базы данных RBAC
 * @dependencies: vitest, @supabase/supabase-js
 * @created: 2024-05-03
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mockSupabaseClient, mockDbFunctions, mockRoles, mockPermissions } from './mocks/supabase';

describe('RBAC Database Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('get_user_roles', () => {
    it('должна возвращать роли пользователя', async () => {
      // Настраиваем мок
      mockDbFunctions.get_user_roles.mockReturnValue(mockRoles);
      
      // Вызываем функцию
      const { data, error } = await mockSupabaseClient.rpc('get_user_roles', { p_user_id: 'user-1' });
      
      // Проверяем результат
      expect(error).toBeNull();
      expect(data).toEqual(mockRoles);
      expect(mockDbFunctions.get_user_roles).toHaveBeenCalledWith({ p_user_id: 'user-1' });
    });

    it('должна обрабатывать ошибки', async () => {
      // Настраиваем мок для имитации ошибки
      mockDbFunctions.get_user_roles.mockImplementation(() => {
        throw new Error('Database error');
      });
      
      // Вызываем функцию
      const { data, error } = await mockSupabaseClient.rpc('get_user_roles', { p_user_id: 'user-1' });
      
      // Проверяем результат
      expect(data).toBeNull();
      expect(error).toBeInstanceOf(Error);
      expect(mockDbFunctions.get_user_roles).toHaveBeenCalledWith({ p_user_id: 'user-1' });
    });
  });

  describe('get_user_permissions', () => {
    it('должна возвращать разрешения пользователя', async () => {
      // Настраиваем мок
      mockDbFunctions.get_user_permissions.mockReturnValue(mockPermissions);
      
      // Вызываем функцию
      const { data, error } = await mockSupabaseClient.rpc('get_user_permissions', { p_user_id: 'user-1' });
      
      // Проверяем результат
      expect(error).toBeNull();
      expect(data).toEqual(mockPermissions);
      expect(mockDbFunctions.get_user_permissions).toHaveBeenCalledWith({ p_user_id: 'user-1' });
    });

    it('должна обрабатывать ошибки', async () => {
      // Настраиваем мок для имитации ошибки
      mockDbFunctions.get_user_permissions.mockImplementation(() => {
        throw new Error('Database error');
      });
      
      // Вызываем функцию
      const { data, error } = await mockSupabaseClient.rpc('get_user_permissions', { p_user_id: 'user-1' });
      
      // Проверяем результат
      expect(data).toBeNull();
      expect(error).toBeInstanceOf(Error);
      expect(mockDbFunctions.get_user_permissions).toHaveBeenCalledWith({ p_user_id: 'user-1' });
    });
  });

  describe('has_permission', () => {
    it('должна возвращать true, если у пользователя есть разрешение', async () => {
      // Настраиваем мок
      mockDbFunctions.has_permission.mockReturnValue(true);
      
      // Вызываем функцию
      const { data, error } = await mockSupabaseClient.rpc('has_permission', { 
        p_user_id: 'user-1',
        p_resource: 'complexes',
        p_action: 'read'
      });
      
      // Проверяем результат
      expect(error).toBeNull();
      expect(data).toBe(true);
      expect(mockDbFunctions.has_permission).toHaveBeenCalledWith({ 
        p_user_id: 'user-1',
        p_resource: 'complexes',
        p_action: 'read'
      });
    });

    it('должна возвращать false, если у пользователя нет разрешения', async () => {
      // Настраиваем мок
      mockDbFunctions.has_permission.mockReturnValue(false);
      
      // Вызываем функцию
      const { data, error } = await mockSupabaseClient.rpc('has_permission', { 
        p_user_id: 'user-5',
        p_resource: 'clients',
        p_action: 'create'
      });
      
      // Проверяем результат
      expect(error).toBeNull();
      expect(data).toBe(false);
      expect(mockDbFunctions.has_permission).toHaveBeenCalledWith({ 
        p_user_id: 'user-5',
        p_resource: 'clients',
        p_action: 'create'
      });
    });
  });

  describe('has_role', () => {
    it('должна возвращать true, если у пользователя есть роль', async () => {
      // Настраиваем мок
      mockDbFunctions.has_role.mockReturnValue(true);
      
      // Вызываем функцию
      const { data, error } = await mockSupabaseClient.rpc('has_role', { 
        p_user_id: 'user-1',
        p_role_name: 'superadmin'
      });
      
      // Проверяем результат
      expect(error).toBeNull();
      expect(data).toBe(true);
      expect(mockDbFunctions.has_role).toHaveBeenCalledWith({ 
        p_user_id: 'user-1',
        p_role_name: 'superadmin'
      });
    });

    it('должна возвращать false, если у пользователя нет роли', async () => {
      // Настраиваем мок
      mockDbFunctions.has_role.mockReturnValue(false);
      
      // Вызываем функцию
      const { data, error } = await mockSupabaseClient.rpc('has_role', { 
        p_user_id: 'user-5',
        p_role_name: 'tenant_admin'
      });
      
      // Проверяем результат
      expect(error).toBeNull();
      expect(data).toBe(false);
      expect(mockDbFunctions.has_role).toHaveBeenCalledWith({ 
        p_user_id: 'user-5',
        p_role_name: 'tenant_admin'
      });
    });
  });
});
