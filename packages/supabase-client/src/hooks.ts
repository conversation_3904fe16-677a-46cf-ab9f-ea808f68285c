import { useEffect, useState } from 'react';
import { createSupabaseClientFromEnv, SupabaseClient } from './supabase';
import { User, Session } from '@supabase/supabase-js';

/**
 * Хук для получения и управления сессией пользователя
 */
export function useSupabaseAuth() {
  const supabase = createSupabaseClientFromEnv();
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Получаем текущую сессию
    const getSession = async () => {
      setLoading(true);
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Error getting session:', error);
      } else {
        setSession(data.session);
        setUser(data.session?.user ?? null);
      }

      setLoading(false);
    };

    getSession();

    // Подписываемся на изменения аутентификации
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, newSession) => {
        setSession(newSession);
        setUser(newSession?.user ?? null);
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [supabase]);

  // Функции для аутентификации
  const signIn = async (email: string, password: string) => {
    return await supabase.auth.signInWithPassword({ email, password });
  };

  const signUp = async (email: string, password: string, metadata?: { [key: string]: any }) => {
    return await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });
  };

  const signOut = async () => {
    return await supabase.auth.signOut();
  };

  const resetPassword = async (email: string) => {
    return await supabase.auth.resetPasswordForEmail(email);
  };

  return {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword,
    supabase
  };
}

/**
 * Хук для получения данных с учетом tenant_id
 */
export function useTenantData<T>(
  tableName: string,
  options?: {
    columns?: string;
    filter?: string;
    tenantId?: string;
    enabled?: boolean;
  }
) {
  const { user, supabase } = useSupabaseAuth();
  const [data, setData] = useState<T[] | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(true);

  const tenantId = options?.tenantId || user?.user_metadata?.tenant_id;
  const enabled = options?.enabled !== undefined ? options.enabled : true;

  useEffect(() => {
    if (!enabled) {
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        let query = (supabase as any)
          .from(tableName)
          .select(options?.columns || '*');

        // Применяем фильтр по tenant_id, если он есть
        if (tenantId) {
          query = query.eq('tenant_id', tenantId);
        }

        // Применяем дополнительный фильтр, если он есть
        if (options?.filter) {
          query = query.or(options.filter);
        }

        const { data: result, error: queryError } = await query;

        if (queryError) {
          throw queryError;
        }

        setData(result as T[]);
      } catch (err) {
        setError(err as Error);
        console.error(`Error fetching data from ${tableName}:`, err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [supabase, tableName, tenantId, options?.columns, options?.filter, enabled]);

  return { data, error, loading, refetch: () => {} };
}

/**
 * Хук для получения данных по ID
 */
export function useDataById<T>(
  tableName: string,
  id: string | undefined,
  options?: {
    columns?: string;
    enabled?: boolean;
  }
) {
  const { supabase } = useSupabaseAuth();
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [loading, setLoading] = useState(true);

  const enabled = options?.enabled !== undefined ? options.enabled : true;

  useEffect(() => {
    if (!id || !enabled) {
      setLoading(false);
      return;
    }

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const { data: result, error: queryError } = await (supabase as any)
          .from(tableName)
          .select(options?.columns || '*')
          .eq('id', id)
          .single();

        if (queryError) {
          throw queryError;
        }

        setData(result as T);
      } catch (err) {
        setError(err as Error);
        console.error(`Error fetching data from ${tableName} with id ${id}:`, err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [supabase, tableName, id, options?.columns, enabled]);

  return { data, error, loading, refetch: () => {} };
}

/**
 * Хук для работы с данными (CRUD операции)
 */
export function useDataMutation<T>(tableName: string) {
  const { supabase } = useSupabaseAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const create = async (data: Partial<T>) => {
    setLoading(true);
    setError(null);

    try {
      const { data: result, error: queryError } = await (supabase as any)
        .from(tableName)
        .insert(data)
        .select();

      if (queryError) {
        throw queryError;
      }

      return result[0];
    } catch (err) {
      setError(err as Error);
      console.error(`Error creating data in ${tableName}:`, err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const update = async (id: string, data: Partial<T>) => {
    setLoading(true);
    setError(null);

    try {
      const { data: result, error: queryError } = await (supabase as any)
        .from(tableName)
        .update(data)
        .eq('id', id)
        .select();

      if (queryError) {
        throw queryError;
      }

      return result[0];
    } catch (err) {
      setError(err as Error);
      console.error(`Error updating data in ${tableName} with id ${id}:`, err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const remove = async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const { error: queryError } = await (supabase as any)
        .from(tableName)
        .delete()
        .eq('id', id);

      if (queryError) {
        throw queryError;
      }

      return true;
    } catch (err) {
      setError(err as Error);
      console.error(`Error deleting data from ${tableName} with id ${id}:`, err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    create,
    update,
    remove,
    loading,
    error
  };
}
