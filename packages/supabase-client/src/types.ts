// Типы для базы данных Supabase
// Соответствуют существующей структуре базы данных PactCRM

export type Json = string | number | boolean | null | { [key: string]: J<PERSON> | undefined } | Json[];

export type Database = {
  public: {
    Tables: {
      tenants: {
        Row: {
          id: string;
          name: string;
          settings: Json | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          settings?: Json | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          settings?: Json | null;
          created_at?: string | null;
        };
        Relationships: [];
      };
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          tenant_id: string | null;
          role: string;
          created_at: string | null;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          tenant_id?: string | null;
          role: string;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          email?: string;
          full_name?: string | null;
          tenant_id?: string | null;
          role?: string;
          created_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "users_tenant_id_fkey";
            columns: ["tenant_id"];
            isOneToOne: false;
            referencedRelation: "tenants";
            referencedColumns: ["id"];
          }
        ];
      };
      roles: {
        Row: {
          id: string;
          name: string;
          tenant_id: string | null;
          description: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          tenant_id?: string | null;
          description?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          tenant_id?: string | null;
          description?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "roles_tenant_id_fkey";
            columns: ["tenant_id"];
            isOneToOne: false;
            referencedRelation: "tenants";
            referencedColumns: ["id"];
          }
        ];
      };
      clients: {
        Row: {
          id: string;
          tenant_id: string | null;
          full_name: string;
          contact_info: Json | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          tenant_id?: string | null;
          full_name: string;
          contact_info?: Json | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          tenant_id?: string | null;
          full_name?: string;
          contact_info?: Json | null;
          created_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "clients_tenant_id_fkey";
            columns: ["tenant_id"];
            isOneToOne: false;
            referencedRelation: "tenants";
            referencedColumns: ["id"];
          }
        ];
      };
      contracts: {
        Row: {
          id: string;
          tenant_id: string | null;
          client_id: string | null;
          amount: number | null;
          status: string | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          tenant_id?: string | null;
          client_id?: string | null;
          amount?: number | null;
          status?: string | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          tenant_id?: string | null;
          client_id?: string | null;
          amount?: number | null;
          status?: string | null;
          created_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "contracts_client_id_fkey";
            columns: ["client_id"];
            isOneToOne: false;
            referencedRelation: "clients";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "contracts_tenant_id_fkey";
            columns: ["tenant_id"];
            isOneToOne: false;
            referencedRelation: "tenants";
            referencedColumns: ["id"];
          }
        ];
      };
      payments: {
        Row: {
          id: string;
          contract_id: string | null;
          amount: number | null;
          status: string | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          contract_id?: string | null;
          amount?: number | null;
          status?: string | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          contract_id?: string | null;
          amount?: number | null;
          status?: string | null;
          created_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "payments_contract_id_fkey";
            columns: ["contract_id"];
            isOneToOne: false;
            referencedRelation: "contracts";
            referencedColumns: ["id"];
          }
        ];
      };
      permissions: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          resource: string;
          action: string;
          tenant_id: string | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          resource: string;
          action: string;
          tenant_id?: string | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          resource?: string;
          action?: string;
          tenant_id?: string | null;
          created_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "permissions_tenant_id_fkey";
            columns: ["tenant_id"];
            isOneToOne: false;
            referencedRelation: "tenants";
            referencedColumns: ["id"];
          }
        ];
      };
      role_permissions: {
        Row: {
          id: string;
          role_id: string;
          permission_id: string;
          tenant_id: string | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          role_id: string;
          permission_id: string;
          tenant_id?: string | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          role_id?: string;
          permission_id?: string;
          tenant_id?: string | null;
          created_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "role_permissions_permission_id_fkey";
            columns: ["permission_id"];
            isOneToOne: false;
            referencedRelation: "permissions";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "role_permissions_role_id_fkey";
            columns: ["role_id"];
            isOneToOne: false;
            referencedRelation: "roles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "role_permissions_tenant_id_fkey";
            columns: ["tenant_id"];
            isOneToOne: false;
            referencedRelation: "tenants";
            referencedColumns: ["id"];
          }
        ];
      };
      user_roles: {
        Row: {
          id: string;
          user_id: string;
          role_id: string;
          tenant_id: string | null;
          created_at: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          role_id: string;
          tenant_id?: string | null;
          created_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          role_id?: string;
          tenant_id?: string | null;
          created_at?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "user_roles_role_id_fkey";
            columns: ["role_id"];
            isOneToOne: false;
            referencedRelation: "roles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_roles_tenant_id_fkey";
            columns: ["tenant_id"];
            isOneToOne: false;
            referencedRelation: "tenants";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "user_roles_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "users";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
};
