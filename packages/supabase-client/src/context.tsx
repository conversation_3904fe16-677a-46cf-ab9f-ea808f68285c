"use client";

import React, { createContext, useContext, ReactNode } from 'react';
import { useSupabaseAuth } from './hooks';
import { User, Session } from '@supabase/supabase-js';
import { SupabaseClient } from './supabase';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<any>;
  signUp: (email: string, password: string, metadata?: { [key: string]: any }) => Promise<any>;
  signOut: () => Promise<any>;
  resetPassword: (email: string) => Promise<any>;
  supabase: SupabaseClient;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const auth = useSupabaseAuth();

  return <AuthContext.Provider value={auth}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Контекст для текущего арендатора
interface TenantContextType {
  tenantId: string | null;
  setTenantId: (id: string | null) => void;
}

const TenantContext = createContext<TenantContextType | undefined>(undefined);

export function TenantProvider({
  children,
  initialTenantId = null
}: {
  children: ReactNode;
  initialTenantId?: string | null;
}) {
  const [tenantId, setTenantId] = React.useState<string | null>(initialTenantId);
  const { user } = useAuth();

  // Если у пользователя есть tenant_id в метаданных, используем его
  React.useEffect(() => {
    if (user?.user_metadata?.tenant_id && !tenantId) {
      setTenantId(user.user_metadata.tenant_id);
    }
  }, [user, tenantId]);

  return (
    <TenantContext.Provider value={{ tenantId, setTenantId }}>
      {children}
    </TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}

// Комбинированный провайдер для удобства
export function SupabaseProvider({ children }: { children: ReactNode }) {
  return (
    <AuthProvider>
      <TenantProvider>
        {children}
      </TenantProvider>
    </AuthProvider>
  );
}
