/**
 * @file: clients.ts
 * @description: Типы данных для работы с клиентами
 * @dependencies: supabase.ts
 * @created: 2024-12-26
 */

import { Database } from './supabase';

/**
 * Тип клиента из базы данных
 */
export type DbClient = Database['public']['Tables']['clients']['Row'];

/**
 * Тип для создания нового клиента
 */
export type ClientInsert = Database['public']['Tables']['clients']['Insert'];

/**
 * Тип для обновления клиента
 */
export type ClientUpdate = Database['public']['Tables']['clients']['Update'];

/**
 * Статусы клиента
 */
export enum ClientStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  POTENTIAL = 'potential',
  BLOCKED = 'blocked',
}

/**
 * Типы коммуникации с клиентом
 */
export enum CommunicationType {
  EMAIL = 'email',
  PHONE = 'phone',
  WHATSAPP = 'whatsapp',
  MEETING = 'meeting',
  OTHER = 'other',
}

/**
 * Расширенный тип клиента с дополнительными данными
 */
export interface Client extends DbClient {
  full_name?: string;
  contracts_count?: number;
  active_contracts?: number;
  total_contract_amount?: number;
  last_payment_date?: string;
  risk_score?: number;
  status?: ClientStatus;
  contracts?: Contract[];
  communications?: ClientCommunication[];
}

/**
 * Интерфейс для коммуникации с клиентом
 */
export interface ClientCommunication {
  id: string;
  client_id: string;
  tenant_id: string;
  type: CommunicationType;
  subject?: string;
  message: string;
  direction: 'incoming' | 'outgoing';
  status: 'sent' | 'delivered' | 'read' | 'failed';
  created_at: string;
  updated_at: string;
  created_by?: string;
}

/**
 * Интерфейс для заметок о клиенте
 */
export interface ClientNote {
  id: string;
  client_id: string;
  tenant_id: string;
  title?: string;
  content: string;
  is_important: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
}

/**
 * Интерфейс для документов клиента
 */
export interface ClientDocument {
  id: string;
  client_id: string;
  tenant_id: string;
  name: string;
  type: 'passport' | 'contract' | 'payment' | 'other';
  file_url: string;
  file_size?: number;
  mime_type?: string;
  created_at: string;
  updated_at: string;
  uploaded_by: string;
}

/**
 * Фильтры для клиентов
 */
export interface ClientFilters {
  search?: string;
  status?: ClientStatus | ClientStatus[];
  has_contracts?: boolean;
  has_active_contracts?: boolean;
  min_contract_amount?: number;
  max_contract_amount?: number;
  registration_date_from?: string;
  registration_date_to?: string;
  last_activity_from?: string;
  last_activity_to?: string;
  risk_score_min?: number;
  risk_score_max?: number;
  city?: string;
}

/**
 * Параметры пагинации для клиентов
 */
export interface ClientPagination {
  page?: number;
  limit?: number;
  offset?: number;
}

/**
 * Параметры сортировки для клиентов
 */
export interface ClientSorting {
  field?: 'first_name' | 'last_name' | 'email' | 'phone' | 'created_at' | 'updated_at' | 'contracts_count' | 'total_contract_amount' | 'last_payment_date';
  order?: 'asc' | 'desc';
}

/**
 * Результат запроса клиентов
 */
export interface ClientsResult {
  data: Client[];
  count: number;
  page: number;
  limit: number;
  total_pages: number;
}

/**
 * Статистика по клиенту
 */
export interface ClientStats {
  total_contracts: number;
  active_contracts: number;
  completed_contracts: number;
  cancelled_contracts: number;
  total_contract_amount: number;
  paid_amount: number;
  outstanding_amount: number;
  last_payment_date?: string;
  next_payment_date?: string;
  risk_score: number;
  communication_count: number;
  last_communication_date?: string;
}

/**
 * Общая статистика по клиентам
 */
export interface ClientsOverallStats {
  total_clients: number;
  active_clients: number;
  potential_clients: number;
  blocked_clients: number;
  clients_with_contracts: number;
  average_contract_amount: number;
  total_contract_amount: number;
  high_risk_clients: number;
  new_clients_this_month: number;
}

/**
 * Данные для создания коммуникации с клиентом
 */
export interface ClientCommunicationInsert {
  client_id: string;
  tenant_id: string;
  type: CommunicationType;
  subject?: string;
  message: string;
  direction: 'incoming' | 'outgoing';
  status?: 'sent' | 'delivered' | 'read' | 'failed';
  created_by?: string;
}

/**
 * Данные для создания заметки о клиенте
 */
export interface ClientNoteInsert {
  client_id: string;
  tenant_id: string;
  title?: string;
  content: string;
  is_important?: boolean;
  created_by: string;
}

/**
 * Данные для загрузки документа клиента
 */
export interface ClientDocumentInsert {
  client_id: string;
  tenant_id: string;
  name: string;
  type: 'passport' | 'contract' | 'payment' | 'other';
  file_url: string;
  file_size?: number;
  mime_type?: string;
  uploaded_by: string;
}

/**
 * Результат анализа рисков клиента
 */
export interface ClientRiskAnalysis {
  risk_score: number;
  risk_level: 'low' | 'medium' | 'high' | 'critical';
  risk_factors: Array<{
    factor: string;
    weight: number;
    description: string;
  }>;
  recommendations: string[];
  last_updated: string;
}

/**
 * Данные для экспорта клиентов
 */
export interface ClientExportData {
  id: string;
  full_name: string;
  email?: string;
  phone?: string;
  address?: string;
  contracts_count: number;
  total_contract_amount: number;
  status: string;
  created_at: string;
  last_activity?: string;
}

/**
 * Параметры для экспорта клиентов
 */
export interface ClientExportParams {
  format: 'csv' | 'xlsx' | 'pdf';
  filters?: ClientFilters;
  fields?: Array<keyof ClientExportData>;
  include_contracts?: boolean;
  include_payments?: boolean;
}

/**
 * Результат массового обновления клиентов
 */
export interface BulkClientUpdateResult {
  updated: Client[];
  errors: Array<{
    client_id: string;
    error: string;
  }>;
}

/**
 * Данные для массового обновления клиентов
 */
export interface BulkClientUpdate {
  client_ids: string[];
  updates: Partial<ClientUpdate>;
}

// Импорт типа Contract для избежания циклических зависимостей
interface Contract {
  id: string;
  client_id: string;
  apartment_id: string;
  number: string;
  signed_date: string;
  total_amount: number;
  status: string;
  created_at: string;
  updated_at: string;
}
