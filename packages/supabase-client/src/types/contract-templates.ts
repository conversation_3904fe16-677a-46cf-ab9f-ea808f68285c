/**
 * @file: contract-templates.ts
 * @description: Типы данных для работы с шаблонами договоров
 * @dependencies: none
 * @created: 2024-05-10
 */

import { Database } from './supabase';

/**
 * Тип шаблона договора из базы данных
 */
export type DbContractTemplate = Database['public']['Tables']['contract_templates']['Row'];

/**
 * Тип для создания нового шаблона договора
 */
export type ContractTemplateInsert = Database['public']['Tables']['contract_templates']['Insert'];

/**
 * Тип для обновления шаблона договора
 */
export type ContractTemplateUpdate = Database['public']['Tables']['contract_templates']['Update'];

/**
 * Тип версии шаблона договора из базы данных
 */
export type DbContractTemplateVersion = Database['public']['Tables']['contract_template_versions']['Row'];

/**
 * Тип для создания новой версии шаблона договора
 */
export type ContractTemplateVersionInsert = Database['public']['Tables']['contract_template_versions']['Insert'];

/**
 * Тип для обновления версии шаблона договора
 */
export type ContractTemplateVersionUpdate = Database['public']['Tables']['contract_template_versions']['Update'];

/**
 * Тип файла шаблона договора из базы данных
 */
export type DbContractTemplateFile = Database['public']['Tables']['contract_template_files']['Row'];

/**
 * Тип для создания нового файла шаблона договора
 */
export type ContractTemplateFileInsert = Database['public']['Tables']['contract_template_files']['Insert'];

/**
 * Тип для обновления файла шаблона договора
 */
export type ContractTemplateFileUpdate = Database['public']['Tables']['contract_template_files']['Update'];

/**
 * Тип документа договора из базы данных
 */
export type DbContractDocument = Database['public']['Tables']['contract_documents']['Row'];

/**
 * Тип для создания нового документа договора
 */
export type ContractDocumentInsert = Database['public']['Tables']['contract_documents']['Insert'];

/**
 * Тип для обновления документа договора
 */
export type ContractDocumentUpdate = Database['public']['Tables']['contract_documents']['Update'];

/**
 * Тип типа шаблона договора из базы данных
 */
export type DbContractTemplateType = Database['public']['Tables']['contract_template_types']['Row'];

/**
 * Тип для создания нового типа шаблона договора
 */
export type ContractTemplateTypeInsert = Database['public']['Tables']['contract_template_types']['Insert'];

/**
 * Тип для обновления типа шаблона договора
 */
export type ContractTemplateTypeUpdate = Database['public']['Tables']['contract_template_types']['Update'];

/**
 * Расширенный тип шаблона договора с дополнительными данными
 */
export interface ContractTemplate extends DbContractTemplate {
  type_name?: string;
  latest_version?: number;
  versions_count?: number;
}

/**
 * Расширенный тип версии шаблона договора с дополнительными данными
 */
export interface ContractTemplateVersion extends DbContractTemplateVersion {
  template_name?: string;
  files?: DbContractTemplateFile[];
}

/**
 * Расширенный тип документа договора с дополнительными данными
 */
export interface ContractDocument extends DbContractDocument {
  contract_number?: string;
  template_name?: string;
  template_version?: number;
}

/**
 * Тип переменной шаблона
 */
export interface TemplateVariable {
  name: string;
  description: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  required: boolean;
  default_value?: string | number | boolean | Date;
}

/**
 * Тип для фильтрации шаблонов договоров
 */
export interface ContractTemplateFilters {
  type_id?: string;
  is_active?: boolean;
  search?: string;
}

/**
 * Тип для пагинации шаблонов договоров
 */
export interface ContractTemplatePagination {
  page: number;
  limit: number;
}

/**
 * Тип для сортировки шаблонов договоров
 */
export interface ContractTemplateSorting {
  column: keyof ContractTemplate;
  direction: 'asc' | 'desc';
}

/**
 * Тип для результата запроса шаблонов договоров
 */
export interface ContractTemplatesResult {
  data: ContractTemplate[];
  count: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Тип для результата запроса версий шаблонов договоров
 */
export interface ContractTemplateVersionsResult {
  data: ContractTemplateVersion[];
  count: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Тип для результата запроса документов договоров
 */
export interface ContractDocumentsResult {
  data: ContractDocument[];
  count: number;
  page: number;
  limit: number;
  totalPages: number;
}
