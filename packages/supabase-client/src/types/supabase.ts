/**
 * @file: supabase.ts
 * @description: Типы данных для Supabase
 * @dependencies: @supabase/supabase-js
 * @created: 2024-05-10
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      contract_documents: {
        Row: {
          id: string
          contract_id: string
          template_version_id: string | null
          tenant_id: string
          file_name: string
          file_path: string
          file_type: string
          file_size: number
          generated_at: string
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          contract_id: string
          template_version_id?: string | null
          tenant_id: string
          file_name: string
          file_path: string
          file_type: string
          file_size: number
          generated_at?: string
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          contract_id?: string
          template_version_id?: string | null
          tenant_id?: string
          file_name?: string
          file_path?: string
          file_type?: string
          file_size?: number
          generated_at?: string
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contract_documents_contract_id_fkey"
            columns: ["contract_id"]
            referencedRelation: "contracts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contract_documents_template_version_id_fkey"
            columns: ["template_version_id"]
            referencedRelation: "contract_template_versions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contract_documents_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      contract_template_files: {
        Row: {
          id: string
          template_version_id: string
          tenant_id: string
          file_name: string
          file_path: string
          file_type: string
          file_size: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          template_version_id: string
          tenant_id: string
          file_name: string
          file_path: string
          file_type: string
          file_size: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          template_version_id?: string
          tenant_id?: string
          file_name?: string
          file_path?: string
          file_type?: string
          file_size?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contract_template_files_template_version_id_fkey"
            columns: ["template_version_id"]
            referencedRelation: "contract_template_versions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contract_template_files_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      contract_templates: {
        Row: {
          id: string
          tenant_id: string
          type_id: string | null
          name: string
          description: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id: string
          type_id?: string | null
          name: string
          description?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: string
          type_id?: string | null
          name?: string
          description?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contract_templates_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contract_templates_type_id_fkey"
            columns: ["type_id"]
            referencedRelation: "contract_template_types"
            referencedColumns: ["id"]
          }
        ]
      }
      contract_template_types: {
        Row: {
          id: string
          tenant_id: string | null
          name: string
          description: string | null
          is_system: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id?: string | null
          name: string
          description?: string | null
          is_system?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: string | null
          name?: string
          description?: string | null
          is_system?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contract_template_types_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      contract_template_versions: {
        Row: {
          id: string
          template_id: string
          tenant_id: string
          version_number: number
          content: string
          variables: Json | null
          created_by: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          template_id: string
          tenant_id: string
          version_number: number
          content: string
          variables?: Json | null
          created_by?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          template_id?: string
          tenant_id?: string
          version_number?: number
          content?: string
          variables?: Json | null
          created_by?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contract_template_versions_template_id_fkey"
            columns: ["template_id"]
            referencedRelation: "contract_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contract_template_versions_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      apartments: {
        Row: {
          id: string
          tenant_id: string
          building_id: string
          number: string
          floor: number
          rooms: number
          area: number
          price: number
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id: string
          building_id: string
          number: string
          floor: number
          rooms: number
          area: number
          price: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: string
          building_id?: string
          number?: string
          floor?: number
          rooms?: number
          area?: number
          price?: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "apartments_building_id_fkey"
            columns: ["building_id"]
            referencedRelation: "buildings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "apartments_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      buildings: {
        Row: {
          id: string
          tenant_id: string
          complex_id: string
          name: string
          address: string
          floors: number
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id: string
          complex_id: string
          name: string
          address: string
          floors: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: string
          complex_id?: string
          name?: string
          address?: string
          floors?: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "buildings_complex_id_fkey"
            columns: ["complex_id"]
            referencedRelation: "complexes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "buildings_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      clients: {
        Row: {
          id: string
          tenant_id: string
          user_id: string | null
          first_name: string
          last_name: string
          middle_name: string | null
          email: string | null
          phone: string | null
          passport_number: string | null
          passport_issued_by: string | null
          passport_issued_date: string | null
          address: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id: string
          user_id?: string | null
          first_name: string
          last_name: string
          middle_name?: string | null
          email?: string | null
          phone?: string | null
          passport_number?: string | null
          passport_issued_by?: string | null
          passport_issued_date?: string | null
          address?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: string
          user_id?: string | null
          first_name?: string
          last_name?: string
          middle_name?: string | null
          email?: string | null
          phone?: string | null
          passport_number?: string | null
          passport_issued_by?: string | null
          passport_issued_date?: string | null
          address?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "clients_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      complexes: {
        Row: {
          id: string
          tenant_id: string
          name: string
          address: string
          description: string | null
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id: string
          name: string
          address: string
          description?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: string
          name?: string
          address?: string
          description?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "complexes_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      contracts: {
        Row: {
          id: string
          tenant_id: string
          client_id: string
          apartment_id: string
          number: string
          signed_date: string
          start_date: string
          end_date: string
          total_amount: number
          initial_payment: number
          monthly_payment: number
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id: string
          client_id: string
          apartment_id: string
          number: string
          signed_date: string
          start_date: string
          end_date: string
          total_amount: number
          initial_payment: number
          monthly_payment: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: string
          client_id?: string
          apartment_id?: string
          number?: string
          signed_date?: string
          start_date?: string
          end_date?: string
          total_amount?: number
          initial_payment?: number
          monthly_payment?: number
          status?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "contracts_apartment_id_fkey"
            columns: ["apartment_id"]
            referencedRelation: "apartments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contracts_client_id_fkey"
            columns: ["client_id"]
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contracts_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      payments: {
        Row: {
          id: string
          tenant_id: string
          contract_id: string
          amount: number
          due_date: string
          payment_date: string | null
          status: string
          payment_method: string | null
          transaction_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          tenant_id: string
          contract_id: string
          amount: number
          due_date: string
          payment_date?: string | null
          status?: string
          payment_method?: string | null
          transaction_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          tenant_id?: string
          contract_id?: string
          amount?: number
          due_date?: string
          payment_date?: string | null
          status?: string
          payment_method?: string | null
          transaction_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payments_contract_id_fkey"
            columns: ["contract_id"]
            referencedRelation: "contracts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payments_tenant_id_fkey"
            columns: ["tenant_id"]
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      tenants: {
        Row: {
          id: string
          name: string
          legal_name: string
          tax_id: string
          address: string | null
          phone: string | null
          email: string | null
          logo_url: string | null
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          legal_name: string
          tax_id: string
          address?: string | null
          phone?: string | null
          email?: string | null
          logo_url?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          legal_name?: string
          tax_id?: string
          address?: string | null
          phone?: string | null
          email?: string | null
          logo_url?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
