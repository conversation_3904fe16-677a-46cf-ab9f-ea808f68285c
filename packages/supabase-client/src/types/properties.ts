/**
 * @file: properties.ts
 * @description: Типы данных для работы с объектами недвижимости
 * @dependencies: supabase.ts
 * @created: 2024-12-26
 */

import { Database } from './supabase';

/**
 * Тип жилого комплекса из базы данных
 */
export type DbComplex = Database['public']['Tables']['complexes']['Row'];

/**
 * Тип для создания нового жилого комплекса
 */
export type ComplexInsert = Database['public']['Tables']['complexes']['Insert'];

/**
 * Тип для обновления жилого комплекса
 */
export type ComplexUpdate = Database['public']['Tables']['complexes']['Update'];

/**
 * Тип здания из базы данных
 */
export type DbBuilding = Database['public']['Tables']['buildings']['Row'];

/**
 * Тип для создания нового здания
 */
export type BuildingInsert = Database['public']['Tables']['buildings']['Insert'];

/**
 * Тип для обновления здания
 */
export type BuildingUpdate = Database['public']['Tables']['buildings']['Update'];

/**
 * Тип квартиры из базы данных
 */
export type DbApartment = Database['public']['Tables']['apartments']['Row'];

/**
 * Тип для создания новой квартиры
 */
export type ApartmentInsert = Database['public']['Tables']['apartments']['Insert'];

/**
 * Тип для обновления квартиры
 */
export type ApartmentUpdate = Database['public']['Tables']['apartments']['Update'];

/**
 * Статусы жилого комплекса
 */
export enum ComplexStatus {
  PLANNING = 'planning',
  CONSTRUCTION = 'construction',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  SUSPENDED = 'suspended',
}

/**
 * Статусы здания
 */
export enum BuildingStatus {
  PLANNING = 'planning',
  CONSTRUCTION = 'construction',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  SUSPENDED = 'suspended',
}

/**
 * Статусы квартиры
 */
export enum ApartmentStatus {
  AVAILABLE = 'available',
  RESERVED = 'reserved',
  SOLD = 'sold',
  UNAVAILABLE = 'unavailable',
}

/**
 * Расширенный тип жилого комплекса с дополнительными данными
 */
export interface Complex extends DbComplex {
  buildings_count?: number;
  apartments_count?: number;
  available_apartments?: number;
  sold_apartments?: number;
  total_area?: number;
  average_price?: number;
  buildings?: Building[];
}

/**
 * Расширенный тип здания с дополнительными данными
 */
export interface Building extends DbBuilding {
  complex_name?: string;
  apartments_count?: number;
  available_apartments?: number;
  sold_apartments?: number;
  total_area?: number;
  average_price?: number;
  apartments?: Apartment[];
  complex?: Complex;
}

/**
 * Расширенный тип квартиры с дополнительными данными
 */
export interface Apartment extends DbApartment {
  building_name?: string;
  complex_name?: string;
  building?: Building;
  complex?: Complex;
  is_sold?: boolean;
  contract_id?: string;
  client_name?: string;
}

/**
 * Фильтры для жилых комплексов
 */
export interface ComplexFilters {
  search?: string;
  status?: ComplexStatus | ComplexStatus[];
  city?: string;
  min_apartments?: number;
  max_apartments?: number;
  min_price?: number;
  max_price?: number;
  created_after?: string;
  created_before?: string;
}

/**
 * Фильтры для зданий
 */
export interface BuildingFilters {
  search?: string;
  complex_id?: string;
  status?: BuildingStatus | BuildingStatus[];
  min_floors?: number;
  max_floors?: number;
  min_apartments?: number;
  max_apartments?: number;
  created_after?: string;
  created_before?: string;
}

/**
 * Фильтры для квартир
 */
export interface ApartmentFilters {
  search?: string;
  complex_id?: string;
  building_id?: string;
  status?: ApartmentStatus | ApartmentStatus[];
  min_rooms?: number;
  max_rooms?: number;
  min_area?: number;
  max_area?: number;
  min_price?: number;
  max_price?: number;
  min_floor?: number;
  max_floor?: number;
  created_after?: string;
  created_before?: string;
}

/**
 * Параметры пагинации
 */
export interface PropertyPagination {
  page?: number;
  limit?: number;
  offset?: number;
}

/**
 * Параметры сортировки для жилых комплексов
 */
export interface ComplexSorting {
  field?: 'name' | 'address' | 'status' | 'created_at' | 'updated_at' | 'apartments_count' | 'average_price';
  order?: 'asc' | 'desc';
}

/**
 * Параметры сортировки для зданий
 */
export interface BuildingSorting {
  field?: 'name' | 'address' | 'floors' | 'status' | 'created_at' | 'updated_at' | 'apartments_count';
  order?: 'asc' | 'desc';
}

/**
 * Параметры сортировки для квартир
 */
export interface ApartmentSorting {
  field?: 'number' | 'floor' | 'rooms' | 'area' | 'price' | 'status' | 'created_at' | 'updated_at';
  order?: 'asc' | 'desc';
}

/**
 * Результат запроса жилых комплексов
 */
export interface ComplexesResult {
  data: Complex[];
  count: number;
  page: number;
  limit: number;
  total_pages: number;
}

/**
 * Результат запроса зданий
 */
export interface BuildingsResult {
  data: Building[];
  count: number;
  page: number;
  limit: number;
  total_pages: number;
}

/**
 * Результат запроса квартир
 */
export interface ApartmentsResult {
  data: Apartment[];
  count: number;
  page: number;
  limit: number;
  total_pages: number;
}

/**
 * Статистика по жилому комплексу
 */
export interface ComplexStats {
  total_buildings: number;
  total_apartments: number;
  available_apartments: number;
  reserved_apartments: number;
  sold_apartments: number;
  total_area: number;
  average_price: number;
  min_price: number;
  max_price: number;
  completion_percentage: number;
}

/**
 * Статистика по зданию
 */
export interface BuildingStats {
  total_apartments: number;
  available_apartments: number;
  reserved_apartments: number;
  sold_apartments: number;
  total_area: number;
  average_price: number;
  min_price: number;
  max_price: number;
  floors_completed: number;
}

/**
 * Данные для создания квартир массово
 */
export interface BulkApartmentCreate {
  building_id: string;
  apartments: Omit<ApartmentInsert, 'tenant_id' | 'building_id'>[];
}

/**
 * Результат массового создания квартир
 */
export interface BulkApartmentResult {
  created: Apartment[];
  errors: Array<{
    apartment: Omit<ApartmentInsert, 'tenant_id' | 'building_id'>;
    error: string;
  }>;
}
