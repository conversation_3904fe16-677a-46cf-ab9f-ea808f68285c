/**
 * @file: contracts.ts
 * @description: Типы данных для работы с договорами
 * @dependencies: none
 * @created: 2024-05-10
 */

import { Database } from './supabase';

/**
 * Тип договора из базы данных
 */
export type DbContract = Database['public']['Tables']['contracts']['Row'];

/**
 * Тип для создания нового договора
 */
export type ContractInsert = Database['public']['Tables']['contracts']['Insert'];

/**
 * Тип для обновления договора
 */
export type ContractUpdate = Database['public']['Tables']['contracts']['Update'];

/**
 * Статусы договора
 */
export enum ContractStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

/**
 * Расширенный тип договора с дополнительными данными
 */
export interface Contract extends DbContract {
  client_name?: string;
  apartment_number?: string;
  building_name?: string;
  complex_name?: string;
}

/**
 * Тип для фильтрации договоров
 */
export interface ContractFilters {
  client_id?: string;
  apartment_id?: string;
  status?: ContractStatus | string;
  search?: string;
  start_date?: Date | string;
  end_date?: Date | string;
}

/**
 * Тип для пагинации договоров
 */
export interface ContractPagination {
  page: number;
  limit: number;
}

/**
 * Тип для сортировки договоров
 */
export interface ContractSorting {
  column: keyof Contract;
  direction: 'asc' | 'desc';
}

/**
 * Тип для результата запроса договоров
 */
export interface ContractsResult {
  data: Contract[];
  count: number;
  page: number;
  limit: number;
  totalPages: number;
}
