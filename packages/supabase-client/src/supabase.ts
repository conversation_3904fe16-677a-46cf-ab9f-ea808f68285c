/**
 * Модуль для работы с Supabase клиентом
 *
 * @module supabase
 */

import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

/**
 * Создает клиент Supabase с типизацией
 *
 * @param supabaseUrl URL Supabase проекта
 * @param supabaseKey Ключ API Supabase
 * @returns Типизированный клиент Supabase
 *
 * @example
 * ```ts
 * const supabase = createSupabaseClient(
 *   'https://your-project.supabase.co',
 *   'your-anon-key'
 * );
 * ```
 */
export const createSupabaseClient = (supabaseUrl: string, supabaseKey: string) => {
  return createClient<Database>(supabaseUrl, supabaseKey);
};

/**
 * Создает клиент Supabase с использованием переменных окружения
 *
 * Использует следующие переменные окружения:
 * - NEXT_PUBLIC_SUPABASE_URL: URL Supabase проекта
 * - NEXT_PUBLIC_SUPABASE_ANON_KEY: Анонимный ключ API Supabase
 *
 * @returns Типизированный клиент Supabase
 * @throws Ошибку, если переменные окружения не определены
 *
 * @example
 * ```ts
 * const supabase = createSupabaseClientFromEnv();
 * ```
 */
export const createSupabaseClientFromEnv = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Не указаны URL и ключ Supabase в переменных окружения');
  }

  return createSupabaseClient(supabaseUrl, supabaseKey);
};

/**
 * Тип клиента Supabase
 */
export type SupabaseClient = ReturnType<typeof createSupabaseClient>;
