#!/bin/bash

# Добавляет директиву "use client" в начало всех компонентных файлов

# Находим все файлы .tsx в директории components
find src/components -name "*.tsx" | while read file; do
  # Проверяем, содержит ли файл уже директиву "use client"
  if ! grep -q "use client" "$file"; then
    echo "Adding 'use client' to $file"
    # Создаем временный файл с директивой "use client" в начале
    echo '"use client";' > temp_file
    echo "" >> temp_file
    cat "$file" >> temp_file
    # Заменяем оригинальный файл временным
    mv temp_file "$file"
  fi
done

echo "Done adding 'use client' directive to component files"
