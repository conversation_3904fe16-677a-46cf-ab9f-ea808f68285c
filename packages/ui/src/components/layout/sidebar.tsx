"use client";

import React from 'react';
import Link from 'next/link';
import { cn } from '../../lib/utils';

export interface SidebarItem {
  title: string;
  href: string;
  icon?: React.ReactNode;
  submenu?: SidebarItem[];
  isActive?: boolean;
}

export interface SidebarProps {
  items: SidebarItem[];
  className?: string;
  logo?: React.ReactNode;
  footer?: React.ReactNode;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function Sidebar({
  items,
  className,
  logo,
  footer,
  collapsed = false,
  onToggleCollapse,
}: SidebarProps) {
  return (
    <div
      className={cn(
        'flex h-screen flex-col border-r bg-background',
        collapsed ? 'w-16' : 'w-64',
        className
      )}
    >
      <div className="flex h-14 items-center border-b px-4">
        {logo}
        {!collapsed && (
          <button
            onClick={onToggleCollapse}
            className="ml-auto rounded-md p-2 hover:bg-accent"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-4 w-4"
            >
              <path d="m15 18-6-6 6-6" />
            </svg>
          </button>
        )}
        {collapsed && (
          <button
            onClick={onToggleCollapse}
            className="ml-auto rounded-md p-2 hover:bg-accent"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-4 w-4"
            >
              <path d="m9 18 6-6-6-6" />
            </svg>
          </button>
        )}
      </div>
      <div className="flex-1 overflow-auto py-2">
        <nav className="grid gap-1 px-2">
          {items.map((item, index) => (
            <SidebarNavItem
              key={index}
              item={item}
              collapsed={collapsed}
            />
          ))}
        </nav>
      </div>
      {footer && <div className="border-t p-4">{footer}</div>}
    </div>
  );
}

function SidebarNavItem({
  item,
  collapsed,
}: {
  item: SidebarItem;
  collapsed: boolean;
}) {
  const [open, setOpen] = React.useState(false);

  if (item.submenu) {
    return (
      <div className="relative">
        <button
          onClick={() => setOpen(!open)}
          className={cn(
            'flex w-full items-center rounded-md px-3 py-2 hover:bg-accent',
            item.isActive && 'bg-accent',
            collapsed && 'justify-center px-2'
          )}
        >
          {item.icon && (
            <span className="mr-2">{item.icon}</span>
          )}
          {!collapsed && (
            <>
              <span className="flex-1 truncate">{item.title}</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={cn('h-4 w-4 transition-transform', open && 'rotate-180')}
              >
                <path d="m6 9 6 6 6-6" />
              </svg>
            </>
          )}
        </button>
        {open && !collapsed && (
          <div className="mt-1 grid gap-1 pl-6">
            {item.submenu.map((subItem, index) => (
              <Link
                key={index}
                href={subItem.href}
                className={cn(
                  'flex items-center rounded-md px-3 py-2 hover:bg-accent',
                  subItem.isActive && 'bg-accent'
                )}
              >
                {subItem.icon && (
                  <span className="mr-2">{subItem.icon}</span>
                )}
                <span className="truncate">{subItem.title}</span>
              </Link>
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <Link
      href={item.href}
      className={cn(
        'flex items-center rounded-md px-3 py-2 hover:bg-accent',
        item.isActive && 'bg-accent',
        collapsed && 'justify-center px-2'
      )}
    >
      {item.icon && (
        <span className={cn('mr-2', collapsed && 'mr-0')}>{item.icon}</span>
      )}
      {!collapsed && <span className="truncate">{item.title}</span>}
    </Link>
  );
}
