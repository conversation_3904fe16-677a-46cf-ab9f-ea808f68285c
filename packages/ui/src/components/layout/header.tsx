"use client";

import React from 'react';
import { cn } from '../../lib/utils';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '../ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Button } from '../ui/button';

export interface HeaderProps {
  className?: string;
  userNavigation?: {
    name: string;
    email: string;
    imageUrl?: string;
    initials?: string;
    items: {
      label: string;
      href?: string;
      onClick?: () => void;
    }[];
  };
  actions?: React.ReactNode;
  searchComponent?: React.ReactNode;
}

export function Header({
  className,
  userNavigation,
  actions,
  searchComponent,
}: HeaderProps) {
  return (
    <header className={cn('flex h-14 items-center gap-4 border-b bg-background px-4 lg:px-6', className)}>
      {searchComponent && (
        <div className="flex-1 md:flex-initial">{searchComponent}</div>
      )}
      <div className="ml-auto flex items-center gap-4">
        {actions}
        {userNavigation && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  {userNavigation.imageUrl && (
                    <AvatarImage src={userNavigation.imageUrl} alt={userNavigation.name} />
                  )}
                  <AvatarFallback>{userNavigation.initials}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">{userNavigation.name}</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {userNavigation.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              {userNavigation.items.map((item, index) => (
                <DropdownMenuItem
                  key={index}
                  onClick={item.onClick}
                  className={item.onClick ? 'cursor-pointer' : ''}
                >
                  {item.href ? (
                    <a href={item.href} className="w-full">
                      {item.label}
                    </a>
                  ) : (
                    item.label
                  )}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </header>
  );
}
