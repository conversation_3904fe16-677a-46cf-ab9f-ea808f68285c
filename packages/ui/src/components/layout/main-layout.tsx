"use client";

import React from 'react';
import { cn } from '../../lib/utils';
import { Sidebar, SidebarItem } from './sidebar';
import { Header } from './header';
import { ThemeSwitcher } from './theme-switcher';
import { Search } from './search';

export interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
  sidebarItems: SidebarItem[];
  logo?: React.ReactNode;
  userNavigation?: {
    name: string;
    email: string;
    imageUrl?: string;
    initials?: string;
    items: {
      label: string;
      href?: string;
      onClick?: () => void;
    }[];
  };
  onSearch?: (value: string) => void;
  headerActions?: React.ReactNode;
}

export function MainLayout({
  children,
  className,
  sidebarItems,
  logo,
  userNavigation,
  onSearch,
  headerActions,
}: MainLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar
        items={sidebarItems}
        logo={logo}
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        footer={
          <div className="flex items-center justify-center">
            <ThemeSwitcher />
          </div>
        }
      />
      <div className="flex flex-1 flex-col overflow-hidden">
        <Header
          userNavigation={userNavigation}
          searchComponent={onSearch ? <Search onSearch={onSearch} className="w-full md:w-64" /> : undefined}
          actions={headerActions}
        />
        <main className={cn('flex-1 overflow-auto p-4 md:p-6', className)}>
          {children}
        </main>
      </div>
    </div>
  );
}
