"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '../ui/card';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';

export interface Task {
  id: string;
  title: string;
  description?: string;
  dueDate?: string;
  priority?: 'low' | 'medium' | 'high';
  status?: 'pending' | 'in-progress' | 'completed';
  assignee?: {
    name: string;
    avatar?: string;
  };
}

export interface TaskListProps {
  title: string;
  subtitle?: string;
  tasks: Task[];
  className?: string;
  action?: React.ReactNode;
  onTaskClick?: (task: Task) => void;
  onStatusChange?: (task: Task, status: 'pending' | 'in-progress' | 'completed') => void;
}

export function TaskList({
  title,
  subtitle,
  tasks,
  className,
  action,
  onTaskClick,
  onStatusChange,
}: TaskListProps) {
  return (
    <Card className={cn('overflow-hidden', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-base font-medium">{title}</CardTitle>
          {subtitle && <p className="text-xs text-muted-foreground">{subtitle}</p>}
        </div>
        {action && <div>{action}</div>}
      </CardHeader>
      <CardContent className="px-0 py-0">
        <div className="divide-y">
          {tasks.map((task) => (
            <div
              key={task.id}
              className={cn(
                'flex items-start p-4 transition-colors hover:bg-muted/50',
                onTaskClick && 'cursor-pointer'
              )}
              onClick={() => onTaskClick && onTaskClick(task)}
            >
              <div className="mr-4 flex h-5 w-5 items-center justify-center">
                <input
                  type="checkbox"
                  checked={task.status === 'completed'}
                  onChange={(e) => {
                    if (onStatusChange) {
                      onStatusChange(
                        task,
                        e.target.checked ? 'completed' : 'pending'
                      );
                    }
                  }}
                  onClick={(e) => e.stopPropagation()}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3
                    className={cn(
                      'text-sm font-medium',
                      task.status === 'completed' && 'line-through text-muted-foreground'
                    )}
                  >
                    {task.title}
                  </h3>
                  {task.priority && (
                    <span
                      className={cn(
                        'ml-2 rounded-full px-2 py-0.5 text-xs',
                        task.priority === 'high' && 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
                        task.priority === 'medium' && 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
                        task.priority === 'low' && 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                      )}
                    >
                      {task.priority}
                    </span>
                  )}
                </div>
                {task.description && (
                  <p className="mt-1 text-xs text-muted-foreground">
                    {task.description}
                  </p>
                )}
                <div className="mt-2 flex items-center justify-between">
                  {task.dueDate && (
                    <span className="text-xs text-muted-foreground">
                      {task.dueDate}
                    </span>
                  )}
                  {task.assignee && (
                    <div className="flex items-center">
                      {task.assignee.avatar ? (
                        <img
                          src={task.assignee.avatar}
                          alt={task.assignee.name}
                          className="h-5 w-5 rounded-full"
                        />
                      ) : (
                        <div className="flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
                          {task.assignee.name.charAt(0)}
                        </div>
                      )}
                      <span className="ml-1 text-xs text-muted-foreground">
                        {task.assignee.name}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              {onStatusChange && task.status !== 'completed' && (
                <div className="ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onStatusChange(task, 'in-progress');
                    }}
                    className="h-7 text-xs"
                  >
                    {task.status === 'pending' ? 'Начать' : 'В работе'}
                  </Button>
                </div>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
