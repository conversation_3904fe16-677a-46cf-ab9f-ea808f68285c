"use client";

import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { cn } from '../../lib/utils';

export interface ChartCardProps {
  title: string;
  subtitle?: string;
  chart: React.ReactNode;
  className?: string;
  action?: React.ReactNode;
}

export function ChartCard({
  title,
  subtitle,
  chart,
  className,
  action,
}: ChartCardProps) {
  return (
    <Card className={cn('overflow-hidden', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-base font-medium">{title}</CardTitle>
          {subtitle && <p className="text-xs text-muted-foreground">{subtitle}</p>}
        </div>
        {action && <div>{action}</div>}
      </CardHeader>
      <CardContent className="p-0">{chart}</CardContent>
    </Card>
  );
}
