"use client";

import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes/dist/types";

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;
}

export function useTheme() {
  // This is just a stub to maintain API compatibility
  // The actual implementation will be provided by next-themes in the app
  console.warn("useTheme() from @pactcrm/ui is deprecated. Use useTheme() from next-themes instead.");
  return {
    theme: "system",
    setTheme: () => {},
  };
}
