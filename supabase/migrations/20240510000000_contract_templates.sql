-- Миграция для создания системы шаблонов договоров

-- Создание таблицы типов шаблонов договоров
CREATE TABLE IF NOT EXISTS contract_template_types (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  is_system BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (tenant_id, name)
);

-- Создание таблицы шаблонов договоров
CREATE TABLE IF NOT EXISTS contract_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  type_id UUID REFERENCES contract_template_types(id) ON DELETE SET NULL,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (tenant_id, name)
);

-- Создание таблицы версий шаблонов договоров
CREATE TABLE IF NOT EXISTS contract_template_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  template_id UUID NOT NULL REFERENCES contract_templates(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  content TEXT NOT NULL, -- Содержимое шаблона в формате HTML или Markdown
  variables JSONB, -- Список переменных, используемых в шаблоне
  created_by UUID, -- Пользователь, создавший версию
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (template_id, version_number)
);

-- Создание таблицы файлов шаблонов
CREATE TABLE IF NOT EXISTS contract_template_files (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  template_version_id UUID NOT NULL REFERENCES contract_template_versions(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_type TEXT NOT NULL, -- DOCX, PDF, etc.
  file_size INTEGER NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Создание таблицы сгенерированных документов
CREATE TABLE IF NOT EXISTS contract_documents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  contract_id UUID NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
  template_version_id UUID REFERENCES contract_template_versions(id) ON DELETE SET NULL,
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_type TEXT NOT NULL, -- DOCX, PDF, etc.
  file_size INTEGER NOT NULL,
  generated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_by UUID, -- Пользователь, сгенерировавший документ
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Включение Row-Level Security для всех таблиц
ALTER TABLE contract_template_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE contract_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE contract_template_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE contract_template_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE contract_documents ENABLE ROW LEVEL SECURITY;

-- Политики RLS для таблицы contract_template_types
CREATE POLICY "Superadmin и Support могут просматривать все типы шаблонов"
ON contract_template_types FOR SELECT
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin') OR
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'support')
);

CREATE POLICY "Tenant Admin и After Sales Manager могут просматривать типы шаблонов своей компании"
ON contract_template_types FOR SELECT
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' IN ('tenant_admin', 'after_sales_manager')) AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Superadmin может изменять типы шаблонов"
ON contract_template_types FOR UPDATE
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin')
);

CREATE POLICY "Tenant Admin может изменять типы шаблонов своей компании"
ON contract_template_types FOR UPDATE
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Superadmin может создавать типы шаблонов"
ON contract_template_types FOR INSERT
WITH CHECK (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin')
);

CREATE POLICY "Tenant Admin может создавать типы шаблонов для своей компании"
ON contract_template_types FOR INSERT
WITH CHECK (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Superadmin может удалять типы шаблонов"
ON contract_template_types FOR DELETE
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin')
);

CREATE POLICY "Tenant Admin может удалять типы шаблонов своей компании"
ON contract_template_types FOR DELETE
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

-- Аналогичные политики для остальных таблиц
-- (contract_templates, contract_template_versions, contract_template_files, contract_documents)

-- Создание триггеров для автоматического обновления updated_at
CREATE TRIGGER update_contract_template_types_updated_at
BEFORE UPDATE ON contract_template_types
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contract_templates_updated_at
BEFORE UPDATE ON contract_templates
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contract_template_versions_updated_at
BEFORE UPDATE ON contract_template_versions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contract_template_files_updated_at
BEFORE UPDATE ON contract_template_files
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contract_documents_updated_at
BEFORE UPDATE ON contract_documents
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Создание начальных типов шаблонов
INSERT INTO contract_template_types (name, description, is_system) VALUES
('Договор купли-продажи', 'Стандартный договор купли-продажи недвижимости', TRUE),
('Договор рассрочки', 'Договор купли-продажи недвижимости с рассрочкой платежа', TRUE),
('Дополнительное соглашение', 'Дополнительное соглашение к договору', TRUE),
('Акт приема-передачи', 'Акт приема-передачи недвижимости', TRUE);

-- Создание разрешений для работы с шаблонами договоров
INSERT INTO permissions (name, description, resource, action) VALUES
('view_contract_templates', 'Просмотр шаблонов договоров', 'contract_templates', 'read'),
('create_contract_templates', 'Создание шаблонов договоров', 'contract_templates', 'create'),
('update_contract_templates', 'Редактирование шаблонов договоров', 'contract_templates', 'update'),
('delete_contract_templates', 'Удаление шаблонов договоров', 'contract_templates', 'delete'),
('generate_contract_documents', 'Генерация документов на основе шаблонов', 'contract_documents', 'create');

-- Назначение разрешений роли tenant_admin
DO $$
DECLARE
    tenant_admin_id UUID;
    perm_id UUID;
BEGIN
    -- Получаем ID роли tenant_admin
    SELECT id INTO tenant_admin_id FROM roles WHERE name = 'tenant_admin';

    -- Назначаем все разрешения для работы с шаблонами договоров
    FOR perm_id IN SELECT id FROM permissions WHERE resource IN ('contract_templates', 'contract_documents')
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = tenant_admin_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (tenant_admin_id, perm_id);
        END IF;
    END LOOP;
END $$;

-- Назначение разрешений роли after_sales_manager
DO $$
DECLARE
    after_sales_manager_id UUID;
    perm_id UUID;
BEGIN
    -- Получаем ID роли after_sales_manager
    SELECT id INTO after_sales_manager_id FROM roles WHERE name = 'after_sales_manager';

    -- Назначаем разрешения на просмотр шаблонов договоров и генерацию документов
    FOR perm_id IN SELECT id FROM permissions WHERE 
        (resource = 'contract_templates' AND action = 'read') OR
        (resource = 'contract_documents' AND action = 'create')
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = after_sales_manager_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (after_sales_manager_id, perm_id);
        END IF;
    END LOOP;
END $$;
