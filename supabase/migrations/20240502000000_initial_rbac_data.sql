-- Миграция для заполнения начальными данными системы ролей и разрешений (RBAC)

-- Создание системных ролей, если они не существуют
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM roles WHERE name = 'superadmin') THEN
        INSERT INTO roles (name, description, is_system) VALUES
        ('superadmin', 'Полный доступ ко всей системе', TRUE);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM roles WHERE name = 'support') THEN
        INSERT INTO roles (name, description, is_system) VALUES
        ('support', 'Техническая поддержка с доступом к данным клиентов', TRUE);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM roles WHERE name = 'tenant_admin') THEN
        INSERT INTO roles (name, description, is_system) VALUES
        ('tenant_admin', 'Администратор компании-застройщика', TRUE);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM roles WHERE name = 'after_sales_manager') THEN
        INSERT INTO roles (name, description, is_system) VALUES
        ('after_sales_manager', 'Менеджер по сопровождению клиентов', TRUE);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM roles WHERE name = 'client') THEN
        INSERT INTO roles (name, description, is_system) VALUES
        ('client', 'Клиент (покупатель недвижимости)', TRUE);
    END IF;
END $$;

-- Создание разрешений для работы с объектами недвижимости
-- Жилые комплексы
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'view_complexes') THEN
        INSERT INTO permissions (name, description, resource, action) VALUES
        ('view_complexes', 'Просмотр жилых комплексов', 'complexes', 'read');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'create_complexes') THEN
        INSERT INTO permissions (name, description, resource, action) VALUES
        ('create_complexes', 'Создание жилых комплексов', 'complexes', 'create');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'update_complexes') THEN
        INSERT INTO permissions (name, description, resource, action) VALUES
        ('update_complexes', 'Редактирование жилых комплексов', 'complexes', 'update');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM permissions WHERE name = 'delete_complexes') THEN
        INSERT INTO permissions (name, description, resource, action) VALUES
        ('delete_complexes', 'Удаление жилых комплексов', 'complexes', 'delete');
    END IF;
END $$;

-- Здания
INSERT INTO permissions (name, description, resource, action) VALUES
('view_buildings', 'Просмотр зданий', 'buildings', 'read'),
('create_buildings', 'Создание зданий', 'buildings', 'create'),
('update_buildings', 'Редактирование зданий', 'buildings', 'update'),
('delete_buildings', 'Удаление зданий', 'buildings', 'delete');

-- Квартиры
INSERT INTO permissions (name, description, resource, action) VALUES
('view_apartments', 'Просмотр квартир', 'apartments', 'read'),
('create_apartments', 'Создание квартир', 'apartments', 'create'),
('update_apartments', 'Редактирование квартир', 'apartments', 'update'),
('delete_apartments', 'Удаление квартир', 'apartments', 'delete');

-- Создание разрешений для работы с клиентами
INSERT INTO permissions (name, description, resource, action) VALUES
('view_clients', 'Просмотр клиентов', 'clients', 'read'),
('create_clients', 'Создание клиентов', 'clients', 'create'),
('update_clients', 'Редактирование клиентов', 'clients', 'update'),
('delete_clients', 'Удаление клиентов', 'clients', 'delete');

-- Создание разрешений для работы с договорами
INSERT INTO permissions (name, description, resource, action) VALUES
('view_contracts', 'Просмотр договоров', 'contracts', 'read'),
('create_contracts', 'Создание договоров', 'contracts', 'create'),
('update_contracts', 'Редактирование договоров', 'contracts', 'update'),
('delete_contracts', 'Удаление договоров', 'contracts', 'delete');

-- Создание разрешений для работы с платежами
INSERT INTO permissions (name, description, resource, action) VALUES
('view_payments', 'Просмотр платежей', 'payments', 'read'),
('create_payments', 'Создание платежей', 'payments', 'create'),
('update_payments', 'Редактирование платежей', 'payments', 'update'),
('delete_payments', 'Удаление платежей', 'payments', 'delete');

-- Создание разрешений для работы с пользователями
INSERT INTO permissions (name, description, resource, action) VALUES
('view_users', 'Просмотр пользователей', 'users', 'read'),
('create_users', 'Создание пользователей', 'users', 'create'),
('update_users', 'Редактирование пользователей', 'users', 'update'),
('delete_users', 'Удаление пользователей', 'users', 'delete');

-- Создание разрешений для работы с ролями
INSERT INTO permissions (name, description, resource, action) VALUES
('view_roles', 'Просмотр ролей', 'roles', 'read'),
('create_roles', 'Создание ролей', 'roles', 'create'),
('update_roles', 'Редактирование ролей', 'roles', 'update'),
('delete_roles', 'Удаление ролей', 'roles', 'delete');

-- Создание разрешений для работы с разрешениями
INSERT INTO permissions (name, description, resource, action) VALUES
('view_permissions', 'Просмотр разрешений', 'permissions', 'read'),
('assign_permissions', 'Назначение разрешений', 'permissions', 'assign');

-- Создание разрешений для работы с настройками
INSERT INTO permissions (name, description, resource, action) VALUES
('view_settings', 'Просмотр настроек', 'settings', 'read'),
('update_settings', 'Редактирование настроек', 'settings', 'update');

-- Создание разрешений для работы с отчетами
INSERT INTO permissions (name, description, resource, action) VALUES
('view_reports', 'Просмотр отчетов', 'reports', 'read'),
('create_reports', 'Создание отчетов', 'reports', 'create'),
('export_reports', 'Экспорт отчетов', 'reports', 'export');

-- Назначение разрешений роли tenant_admin
DO $$
DECLARE
    tenant_admin_id UUID;
    perm_id UUID;
BEGIN
    -- Получаем ID роли tenant_admin
    SELECT id INTO tenant_admin_id FROM roles WHERE name = 'tenant_admin';

    -- Назначаем все разрешения для работы с объектами недвижимости
    FOR perm_id IN SELECT id FROM permissions WHERE resource IN ('complexes', 'buildings', 'apartments')
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = tenant_admin_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (tenant_admin_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем все разрешения для работы с клиентами
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'clients'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = tenant_admin_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (tenant_admin_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем все разрешения для работы с договорами
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'contracts'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = tenant_admin_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (tenant_admin_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем все разрешения для работы с платежами
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'payments'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = tenant_admin_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (tenant_admin_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения для работы с пользователями
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'users'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = tenant_admin_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (tenant_admin_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения для работы с ролями
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'roles'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = tenant_admin_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (tenant_admin_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения для работы с разрешениями
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'permissions'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = tenant_admin_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (tenant_admin_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения для работы с настройками
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'settings'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = tenant_admin_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (tenant_admin_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения для работы с отчетами
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'reports'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = tenant_admin_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (tenant_admin_id, perm_id);
        END IF;
    END LOOP;
END $$;

-- Назначение разрешений роли after_sales_manager
DO $$
DECLARE
    after_sales_manager_id UUID;
    perm_id UUID;
BEGIN
    -- Получаем ID роли after_sales_manager
    SELECT id INTO after_sales_manager_id FROM roles WHERE name = 'after_sales_manager';

    -- Назначаем разрешения на просмотр объектов недвижимости
    FOR perm_id IN SELECT id FROM permissions WHERE resource IN ('complexes', 'buildings', 'apartments') AND action = 'read'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = after_sales_manager_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (after_sales_manager_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения для работы с клиентами
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'clients' AND action IN ('read', 'update')
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = after_sales_manager_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (after_sales_manager_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения для работы с договорами
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'contracts' AND action IN ('read', 'create', 'update')
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = after_sales_manager_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (after_sales_manager_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения для работы с платежами
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'payments' AND action IN ('read', 'create', 'update')
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = after_sales_manager_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (after_sales_manager_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения на просмотр отчетов
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'reports' AND action = 'read'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = after_sales_manager_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (after_sales_manager_id, perm_id);
        END IF;
    END LOOP;
END $$;

-- Назначение разрешений роли client
DO $$
DECLARE
    client_id UUID;
    perm_id UUID;
BEGIN
    -- Получаем ID роли client
    SELECT id INTO client_id FROM roles WHERE name = 'client';

    -- Назначаем разрешения на просмотр объектов недвижимости
    FOR perm_id IN SELECT id FROM permissions WHERE resource IN ('complexes', 'buildings', 'apartments') AND action = 'read'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = client_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (client_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения на просмотр своих договоров
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'contracts' AND action = 'read'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = client_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (client_id, perm_id);
        END IF;
    END LOOP;

    -- Назначаем разрешения на просмотр своих платежей
    FOR perm_id IN SELECT id FROM permissions WHERE resource = 'payments' AND action = 'read'
    LOOP
        IF NOT EXISTS (SELECT 1 FROM role_permissions WHERE role_id = client_id AND permission_id = perm_id) THEN
            INSERT INTO role_permissions (role_id, permission_id) VALUES (client_id, perm_id);
        END IF;
    END LOOP;
END $$;
