-- Создание расширений
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Создание таблицы арендаторов (компаний-з<PERSON><PERSON><PERSON>р<PERSON><PERSON><PERSON>иков)
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  legal_name TEXT NOT NULL,
  tax_id TEXT NOT NULL,
  address TEXT,
  phone TEXT,
  email TEXT,
  logo_url TEXT,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Создание таблицы жилых комплексов
CREATE TABLE complexes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  address TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Создание таблицы зданий
CREATE TABLE buildings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  complex_id UUID REFERENCES complexes(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  address TEXT NOT NULL,
  floors INTEGER NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Создание таблицы квартир
CREATE TABLE apartments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  building_id UUID NOT NULL REFERENCES buildings(id) ON DELETE CASCADE,
  number TEXT NOT NULL,
  floor INTEGER NOT NULL,
  rooms INTEGER NOT NULL,
  area NUMERIC(10, 2) NOT NULL,
  price NUMERIC(14, 2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'available',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Создание таблицы клиентов
CREATE TABLE clients (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  user_id UUID, -- Связь с auth.users
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  middle_name TEXT,
  email TEXT,
  phone TEXT,
  passport_number TEXT,
  passport_issued_by TEXT,
  passport_issued_date DATE,
  address TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Создание таблицы договоров
CREATE TABLE contracts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  apartment_id UUID NOT NULL REFERENCES apartments(id) ON DELETE CASCADE,
  number TEXT NOT NULL,
  signed_date DATE NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  total_amount NUMERIC(14, 2) NOT NULL,
  initial_payment NUMERIC(14, 2) NOT NULL,
  monthly_payment NUMERIC(14, 2) NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Создание таблицы платежей
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  contract_id UUID NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
  amount NUMERIC(14, 2) NOT NULL,
  due_date DATE NOT NULL,
  payment_date DATE,
  status TEXT NOT NULL DEFAULT 'pending',
  payment_method TEXT,
  transaction_id TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Включение Row-Level Security для всех таблиц
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE complexes ENABLE ROW LEVEL SECURITY;
ALTER TABLE buildings ENABLE ROW LEVEL SECURITY;
ALTER TABLE apartments ENABLE ROW LEVEL SECURITY;
ALTER TABLE clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- Политики RLS для таблицы tenants
CREATE POLICY "Superadmin и Support могут просматривать все компании" 
ON tenants FOR SELECT 
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin') OR
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'support')
);

CREATE POLICY "Tenant Admin и After Sales Manager могут просматривать только свою компанию" 
ON tenants FOR SELECT 
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' IN ('tenant_admin', 'after_sales_manager')) AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = id
  )
);

CREATE POLICY "Superadmin может изменять компании" 
ON tenants FOR UPDATE 
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin')
);

CREATE POLICY "Superadmin может создавать компании" 
ON tenants FOR INSERT 
WITH CHECK (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin')
);

CREATE POLICY "Superadmin может удалять компании" 
ON tenants FOR DELETE 
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin')
);

-- Аналогичные политики RLS для остальных таблиц
-- (complexes, buildings, apartments, clients, contracts, payments)
-- с проверкой tenant_id и соответствующих ролей
