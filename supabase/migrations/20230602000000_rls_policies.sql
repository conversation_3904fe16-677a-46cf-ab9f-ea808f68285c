-- Политики RLS для таблицы complexes
CREATE POLICY "Superadmin и Support могут просматривать все жилые комплексы" 
ON complexes FOR SELECT 
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin') OR
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'support')
);

CREATE POLICY "Tenant Admin и After Sales Manager могут просматривать жилые комплексы своей компании" 
ON complexes FOR SELECT 
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' IN ('tenant_admin', 'after_sales_manager')) AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Client может просматривать жилые комплексы своей компании" 
ON complexes FOR SELECT 
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'client') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Tenant Admin может изменять жилые комплексы своей компании" 
ON complexes FOR UPDATE 
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Tenant Admin может создавать жилые комплексы для своей компании" 
ON complexes FOR INSERT 
WITH CHECK (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Tenant Admin может удалять жилые комплексы своей компании" 
ON complexes FOR DELETE 
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

-- Политики RLS для таблицы buildings
CREATE POLICY "Superadmin и Support могут просматривать все здания" 
ON buildings FOR SELECT 
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin') OR
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'support')
);

CREATE POLICY "Tenant Admin и After Sales Manager могут просматривать здания своей компании" 
ON buildings FOR SELECT 
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' IN ('tenant_admin', 'after_sales_manager')) AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Client может просматривать здания своей компании" 
ON buildings FOR SELECT 
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'client') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Tenant Admin может изменять здания своей компании" 
ON buildings FOR UPDATE 
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Tenant Admin может создавать здания для своей компании" 
ON buildings FOR INSERT 
WITH CHECK (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Tenant Admin может удалять здания своей компании" 
ON buildings FOR DELETE 
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

-- Аналогичные политики RLS для остальных таблиц
-- (apartments, clients, contracts, payments)
-- с проверкой tenant_id и соответствующих ролей
