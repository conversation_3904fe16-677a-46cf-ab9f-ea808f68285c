-- Миграция для создания системы ролей и разрешений (RBAC)

-- Создание таблицы ролей
CREATE TABLE IF NOT EXISTS roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  is_system BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (tenant_id, name)
);

-- Создание таблицы разрешений
CREATE TABLE IF NOT EXISTS permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  resource TEXT NOT NULL,
  action TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (tenant_id, resource, action)
);

-- Создание таблицы связи ролей и разрешений
CREATE TABLE IF NOT EXISTS role_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (role_id, permission_id)
);

-- Создание таблицы связи пользователей и ролей
CREATE TABLE IF NOT EXISTS user_roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL,
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE (user_id, role_id)
);

-- Включение Row-Level Security для всех таблиц
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE role_permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_roles ENABLE ROW LEVEL SECURITY;

-- Политики RLS для таблицы roles
CREATE POLICY "Superadmin и Support могут просматривать все роли"
ON roles FOR SELECT
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin') OR
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'support')
);

CREATE POLICY "Tenant Admin может просматривать роли своей компании"
ON roles FOR SELECT
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Superadmin может изменять глобальные роли"
ON roles FOR UPDATE
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin')
);

CREATE POLICY "Tenant Admin может изменять роли своей компании"
ON roles FOR UPDATE
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Superadmin может создавать глобальные роли"
ON roles FOR INSERT
WITH CHECK (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin')
);

CREATE POLICY "Tenant Admin может создавать роли для своей компании"
ON roles FOR INSERT
WITH CHECK (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

CREATE POLICY "Superadmin может удалять глобальные роли"
ON roles FOR DELETE
USING (
  (auth.jwt() -> 'user_metadata' ->> 'role' = 'superadmin')
);

CREATE POLICY "Tenant Admin может удалять роли своей компании"
ON roles FOR DELETE
USING (
  (
    (auth.jwt() -> 'user_metadata' ->> 'role' = 'tenant_admin') AND
    (auth.jwt() -> 'user_metadata' ->> 'tenant_id')::UUID = tenant_id
  )
);

-- Аналогичные политики для permissions, role_permissions и user_roles

-- Создание функции для получения ролей пользователя
DROP FUNCTION IF EXISTS get_user_roles(UUID);

CREATE OR REPLACE FUNCTION get_user_roles(p_user_id UUID)
RETURNS SETOF roles
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT r.*
  FROM roles r
  JOIN user_roles ur ON r.id = ur.role_id
  WHERE ur.user_id = p_user_id;
END;
$$;

-- Создание функции для получения разрешений пользователя
DROP FUNCTION IF EXISTS get_user_permissions(UUID);

CREATE OR REPLACE FUNCTION get_user_permissions(p_user_id UUID)
RETURNS SETOF permissions
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT DISTINCT p.*
  FROM permissions p
  JOIN role_permissions rp ON p.id = rp.permission_id
  JOIN user_roles ur ON rp.role_id = ur.role_id
  WHERE ur.user_id = p_user_id;
END;
$$;

-- Создание функции для проверки наличия разрешения у пользователя
DROP FUNCTION IF EXISTS has_permission(UUID, TEXT, TEXT);

CREATE OR REPLACE FUNCTION has_permission(p_user_id UUID, p_resource TEXT, p_action TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_has_permission BOOLEAN;
  v_user_role TEXT;
BEGIN
  -- Получаем роль пользователя из метаданных
  SELECT auth.users.raw_user_meta_data->>'role' INTO v_user_role
  FROM auth.users
  WHERE id = p_user_id;

  -- Если пользователь superadmin, у него есть все разрешения
  IF v_user_role = 'superadmin' THEN
    RETURN TRUE;
  END IF;

  -- Если пользователь support, у него есть разрешения только на чтение
  IF v_user_role = 'support' AND p_action = 'read' THEN
    RETURN TRUE;
  END IF;

  -- Проверяем наличие разрешения в таблице
  SELECT EXISTS (
    SELECT 1
    FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    JOIN user_roles ur ON rp.role_id = ur.role_id
    WHERE ur.user_id = p_user_id
    AND p.resource = p_resource
    AND p.action = p_action
  ) INTO v_has_permission;

  RETURN v_has_permission;
END;
$$;

-- Создание функции для проверки наличия роли у пользователя
DROP FUNCTION IF EXISTS has_role(UUID, TEXT);

CREATE OR REPLACE FUNCTION has_role(p_user_id UUID, p_role_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_has_role BOOLEAN;
  v_user_role TEXT;
BEGIN
  -- Получаем роль пользователя из метаданных
  SELECT auth.users.raw_user_meta_data->>'role' INTO v_user_role
  FROM auth.users
  WHERE id = p_user_id;

  -- Проверяем основную роль
  IF v_user_role = p_role_name THEN
    RETURN TRUE;
  END IF;

  -- Проверяем наличие роли в таблице
  SELECT EXISTS (
    SELECT 1
    FROM roles r
    JOIN user_roles ur ON r.id = ur.role_id
    WHERE ur.user_id = p_user_id
    AND r.name = p_role_name
  ) INTO v_has_role;

  RETURN v_has_role;
END;
$$;

-- Создание триггера для автоматического обновления updated_at
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_roles_updated_at
BEFORE UPDATE ON roles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_permissions_updated_at
BEFORE UPDATE ON permissions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_role_permissions_updated_at
BEFORE UPDATE ON role_permissions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_roles_updated_at
BEFORE UPDATE ON user_roles
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
