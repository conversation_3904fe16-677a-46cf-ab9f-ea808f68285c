# Dependencies
node_modules/
.pnp
.pnp.js
.yarn/install-state.gz

# Testing
coverage/
.nyc_output/

# Next.js
.next/
out/
build/

# Production
/build

# Misc
.DS_Store
*.pem
*.sh

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local env files
.env
.env*.local
.env.development.local
.env.test.local
.env.production.local

# Turbo
.turbo/

# Vercel
.vercel/

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Supabase
.supabase/

# Build outputs
dist/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Memory
.memory

# Temporary folders
tmp/
temp/
